const axios = require('axios');

const testLogin = async () => {
  try {
    console.log('🧪 Testing login functionality...');
    
    // Test with an existing admin account
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'  // We'll need to find the correct password
    };
    
    console.log('📝 Attempting login with:', loginData.email);
    
    const response = await axios.post('http://localhost:5000/api/users/login', loginData);
    
    console.log('✅ Login successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Login failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
};

const testRegister = async () => {
  try {
    console.log('🧪 Testing registration functionality...');
    
    const registerData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      level: 'primary',
      class: '1'
    };
    
    console.log('📝 Attempting registration with:', registerData.email);
    
    const response = await axios.post('http://localhost:5000/api/users/register', registerData);
    
    console.log('✅ Registration successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Registration failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
};

const runTests = async () => {
  console.log('🚀 Starting authentication tests...\n');
  
  // First try to register a test user
  await testRegister();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Then try to login with that user
  await testLogin();
  
  console.log('\n🏁 Tests completed!');
};

runTests();
