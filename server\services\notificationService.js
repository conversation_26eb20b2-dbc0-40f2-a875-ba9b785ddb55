const Notification = require("../models/notificationModel");
const User = require("../models/userModel");
const mongoose = require("mongoose");

class NotificationService {
  
  // Create a new exam notification
  static async notifyNewExam(examData) {
    try {
      console.log('📧 Starting new exam notification process for:', examData.name);

      // Get all active users (filter by level and exclude admins)
      const users = await User.find({
        isBlocked: false,
        isAdmin: false,
        level: examData.level // Only notify users of the same level
      }).select('_id name level');

      console.log(`👥 Found ${users.length} users to notify for level: ${examData.level}`);

      if (users.length === 0) {
        console.log('⚠️ No users found to notify');
        return;
      }

      const notifications = users.map(user => ({
        recipient: user._id,
        type: 'new_exam',
        title: '🎯 New Exam Available!',
        message: `A new exam "${examData.name}" has been added for ${examData.subject} - Class ${examData.class}. Test your knowledge now!`,
        relatedEntity: {
          entityType: 'exam',
          entityId: examData._id,
          entityData: {
            name: examData.name,
            subject: examData.subject,
            class: examData.class,
            level: examData.level
          }
        },
        actionUrl: `/quiz/${examData._id}/play`,
        priority: 'medium',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));

      const result = await Notification.insertMany(notifications);
      console.log(`✅ Created ${result.length} new exam notifications successfully`);

    } catch (error) {
      console.error('❌ Error creating new exam notifications:', error);
      throw error;
    }
  }
  
  // Create a new study material notification
  static async notifyNewStudyMaterial(materialData) {
    try {
      const users = await User.find({ 
        isActive: true,
        // Filter by relevant criteria
      }).select('_id');
      
      const notifications = users.map(user => ({
        recipient: user._id,
        type: 'new_study_material',
        title: '📚 New Study Material Added!',
        message: `New study material "${materialData.title}" is now available. Enhance your learning!`,
        relatedEntity: {
          entityType: 'study_material',
          entityId: materialData._id,
          entityData: {
            title: materialData.title,
            subject: materialData.subject,
            type: materialData.type
          }
        },
        actionUrl: `/study/materials/${materialData._id}`,
        priority: 'low',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));
      
      await Notification.insertMany(notifications);
      console.log(`✅ Created ${notifications.length} new study material notifications`);
      
    } catch (error) {
      console.error('Error creating study material notifications:', error);
    }
  }
  
  // Notify when a new forum question is posted
  static async notifyNewForumQuestion(questionData, authorId) {
    try {
      // Get all users except the author
      const users = await User.find({ 
        isActive: true,
        _id: { $ne: authorId }
      }).select('_id');
      
      const notifications = users.map(user => ({
        recipient: user._id,
        sender: authorId,
        type: 'forum_question_posted',
        title: '❓ New Forum Question',
        message: `Someone asked: "${questionData.title}". Share your knowledge!`,
        relatedEntity: {
          entityType: 'forum_question',
          entityId: questionData._id,
          entityData: {
            title: questionData.title,
            subject: questionData.subject
          }
        },
        actionUrl: `/forum/question/${questionData._id}`,
        priority: 'low',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));
      
      await Notification.insertMany(notifications);
      console.log(`✅ Created ${notifications.length} forum question notifications`);
      
    } catch (error) {
      console.error('Error creating forum question notifications:', error);
    }
  }
  
  // Notify when someone answers your forum question
  static async notifyForumAnswerReceived(questionData, answererData, questionAuthorId) {
    try {
      const notification = await Notification.createNotification({
        recipient: questionAuthorId,
        sender: answererData._id,
        type: 'forum_answer_received',
        title: '💡 Your Question Got Answered!',
        message: `${answererData.name} answered your question: "${questionData.title}"`,
        relatedEntity: {
          entityType: 'forum_answer',
          entityId: questionData._id,
          entityData: {
            questionTitle: questionData.title,
            answererName: answererData.name
          }
        },
        actionUrl: `/forum/question/${questionData._id}`,
        priority: 'high',
        channels: {
          inApp: true,
          email: true,
          push: false
        }
      });
      
      console.log(`✅ Created forum answer notification for user ${questionAuthorId}`);
      return notification;
      
    } catch (error) {
      console.error('Error creating forum answer notification:', error);
    }
  }
  
  // Notify about level up
  static async notifyLevelUp(userId, newLevel) {
    try {
      const notification = await Notification.createNotification({
        recipient: userId,
        type: 'level_up',
        title: '🎉 Level Up!',
        message: `Congratulations! You've reached Level ${newLevel}! Keep up the great work!`,
        relatedEntity: {
          entityType: 'user',
          entityId: userId,
          entityData: {
            newLevel: newLevel
          }
        },
        actionUrl: '/profile',
        priority: 'high',
        channels: {
          inApp: true,
          email: false,
          push: true
        }
      });
      
      console.log(`✅ Created level up notification for user ${userId}`);
      return notification;
      
    } catch (error) {
      console.error('Error creating level up notification:', error);
    }
  }
  
  // Get user notifications
  static async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        unreadOnly = false,
        type = null
      } = options;
      
      const query = {
        recipient: userId,
        isArchived: false
      };
      
      if (unreadOnly) {
        query.isRead = false;
      }
      
      if (type) {
        query.type = type;
      }
      
      const notifications = await Notification.find(query)
        .populate('sender', 'name profilePicture')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
      
      const total = await Notification.countDocuments(query);
      const unreadCount = await Notification.countDocuments({
        recipient: userId,
        isRead: false,
        isArchived: false
      });
      
      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        unreadCount
      };
      
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }
  
  // Mark notification as read
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        recipient: userId
      });
      
      if (!notification) {
        throw new Error('Notification not found');
      }
      
      return await notification.markAsRead();
      
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
  
  // Mark all notifications as read
  static async markAllAsRead(userId) {
    try {
      const result = await Notification.updateMany(
        { recipient: userId, isRead: false },
        { isRead: true }
      );

      console.log(`✅ Marked ${result.modifiedCount} notifications as read for user ${userId}`);
      return result;

    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Admin notification methods
  static async sendAdminNotification(data) {
    try {
      const { title, message, recipients, specificUsers, level, class: targetClass, priority, senderId } = data;

      let targetUsers = [];
      let recipientType = recipients;
      let recipientCount = 0;

      // Determine target users based on recipient type
      if (recipients === 'all') {
        targetUsers = await User.find({
          isAdmin: false,
          isBlocked: false
        }).select('_id');
      } else if (recipients === 'level') {
        targetUsers = await User.find({
          level: level,
          isAdmin: false,
          isBlocked: false
        }).select('_id');
      } else if (recipients === 'class') {
        targetUsers = await User.find({
          class: targetClass,
          isAdmin: false,
          isBlocked: false
        }).select('_id');
      } else if (recipients === 'specific') {
        targetUsers = await User.find({
          _id: { $in: specificUsers },
          isAdmin: false,
          isBlocked: false
        }).select('_id');
      }

      recipientCount = targetUsers.length;

      if (recipientCount === 0) {
        throw new Error('No valid recipients found');
      }

      // Create notifications for all target users
      const notifications = targetUsers.map(user => ({
        recipient: user._id,
        sender: senderId,
        type: 'system_announcement',
        title: title,
        message: message,
        priority: priority || 'medium',
        channels: {
          inApp: true,
          email: false,
          push: false
        },
        // Store admin notification metadata
        adminNotification: {
          recipientType: recipientType,
          targetLevel: level,
          targetClass: targetClass,
          recipientCount: recipientCount
        }
      }));

      await Notification.insertMany(notifications);

      console.log(`✅ Created ${recipientCount} admin notifications`);

      return {
        recipientCount,
        recipientType,
        targetLevel: level,
        targetClass: targetClass
      };

    } catch (error) {
      console.error('Error sending admin notification:', error);
      throw error;
    }
  }

  static async getAdminSentNotifications(adminId) {
    try {
      // Get unique admin notifications sent by this admin
      const notifications = await Notification.aggregate([
        {
          $match: {
            sender: new mongoose.Types.ObjectId(adminId),
            type: 'system_announcement'
          }
        },
        {
          $group: {
            _id: {
              title: '$title',
              message: '$message',
              createdAt: '$createdAt',
              priority: '$priority'
            },
            recipientCount: { $sum: 1 },
            recipientType: { $first: '$adminNotification.recipientType' },
            targetLevel: { $first: '$adminNotification.targetLevel' },
            targetClass: { $first: '$adminNotification.targetClass' },
            notificationId: { $first: '$_id' }
          }
        },
        {
          $project: {
            _id: '$notificationId',
            title: '$_id.title',
            message: '$_id.message',
            createdAt: '$_id.createdAt',
            priority: '$_id.priority',
            recipientCount: 1,
            recipientType: 1,
            targetLevel: 1,
            targetClass: 1
          }
        },
        {
          $sort: { createdAt: -1 }
        },
        {
          $limit: 50
        }
      ]);

      return notifications;

    } catch (error) {
      console.error('Error getting admin sent notifications:', error);
      throw error;
    }
  }

  static async deleteAdminNotification(notificationId, adminId) {
    try {
      // Find the notification to get its details
      const sampleNotification = await Notification.findOne({
        _id: notificationId,
        sender: adminId,
        type: 'system_announcement'
      });

      if (!sampleNotification) {
        throw new Error('Notification not found or access denied');
      }

      // Delete all notifications with the same title, message, and sender
      const result = await Notification.deleteMany({
        sender: adminId,
        type: 'system_announcement',
        title: sampleNotification.title,
        message: sampleNotification.message,
        createdAt: sampleNotification.createdAt
      });

      console.log(`✅ Deleted ${result.deletedCount} admin notifications`);
      return result;

    } catch (error) {
      console.error('Error deleting admin notification:', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
