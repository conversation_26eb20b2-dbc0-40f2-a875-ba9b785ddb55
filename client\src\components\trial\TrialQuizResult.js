import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import {
  TbTrophy,
  TbCheck,
  TbX,
  TbClock,
  TbBrain,
  TbArrowRight,
  TbStar,
  TbUsers,
  TbBook,
  TbMessageCircle,
  TbChartBar,
  TbSettings
} from "react-icons/tb";

const TrialQuizResult = ({ result, onTryAnother, onRegister }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getPerformanceMessage = (percentage) => {
    if (percentage >= 90) return {
      message: "Outstanding Performance! 🌟",
      color: "text-purple-600",
      bg: "bg-purple-50",
      gradient: "from-purple-500 to-purple-600"
    };
    if (percentage >= 80) return {
      message: "Excellent Work! 🎉",
      color: "text-green-600",
      bg: "bg-green-50",
      gradient: "from-green-500 to-green-600"
    };
    if (percentage >= 70) return {
      message: "Great Job! 👏",
      color: "text-blue-600",
      bg: "bg-blue-50",
      gradient: "from-blue-500 to-blue-600"
    };
    if (percentage >= 60) return {
      message: "Well Done! ✨",
      color: "text-emerald-600",
      bg: "bg-emerald-50",
      gradient: "from-emerald-500 to-emerald-600"
    };
    if (percentage >= 40) return {
      message: "Good Effort! 💪",
      color: "text-yellow-600",
      bg: "bg-yellow-50",
      gradient: "from-yellow-500 to-yellow-600"
    };
    return {
      message: "Keep Practicing! 📚",
      color: "text-orange-600",
      bg: "bg-orange-50",
      gradient: "from-orange-500 to-orange-600"
    };
  };

  const performance = getPerformanceMessage(result.percentage);
  const isPassed = result.percentage >= 60;

  const premiumFeatures = [
    {
      icon: TbBook,
      title: "Study Materials",
      description: "Access comprehensive study materials, notes, and resources"
    },
    {
      icon: TbBrain,
      title: "AI Assistant",
      description: "Get personalized explanations and study recommendations"
    },
    {
      icon: TbChartBar,
      title: "Ranking System",
      description: "Compete with other students and track your progress"
    },
    {
      icon: TbMessageCircle,
      title: "Forum Access",
      description: "Ask questions and help other students in our community"
    },
    {
      icon: TbUsers,
      title: "Unlimited Quizzes",
      description: "Take as many quizzes as you want across all subjects"
    },
    {
      icon: TbStar,
      title: "Progress Tracking",
      description: "Detailed analytics and performance insights"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Animated Result Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-8 sm:mb-12"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3, type: "spring", stiffness: 200 }}
            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}
            onAnimationComplete={() => setAnimationComplete(true)}
          >
            <TbTrophy className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white" />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
          >
            Quiz Complete! 🎉
          </motion.h1>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}
          >
            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>
              {performance.message}
            </p>
          </motion.div>
        </motion.div>

        {/* Animated Score Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12"
        >
          {/* Score Header */}
          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 1.2, type: "spring" }}
                className="text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2"
              >
                {result.percentage}%
              </motion.div>
              <div className="text-white/90 text-lg sm:text-xl">
                Your Score
              </div>
            </div>
          </div>

          {/* Score Details */}
          <div className="px-6 sm:px-8 lg:px-10 py-8 sm:py-10"
        >
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
              {[
                {
                  label: "Total Questions",
                  value: result.totalQuestions,
                  icon: TbBook,
                  color: "blue",
                  delay: 1.4
                },
                {
                  label: "Correct Answers",
                  value: result.correctAnswers,
                  icon: TbCheck,
                  color: "green",
                  delay: 1.6
                },
                {
                  label: "Wrong Answers",
                  value: result.wrongAnswers,
                  icon: TbX,
                  color: "red",
                  delay: 1.8
                },
                {
                  label: "Time Taken",
                  value: formatTime(result.timeSpent),
                  icon: TbClock,
                  color: "purple",
                  delay: 2.0
                }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: stat.delay }}
                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}
                >
                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Pass/Fail Status */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 2.2 }}
              className="text-center mb-8"
            >
              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${
                isPassed
                  ? 'bg-green-100 text-green-700 border-2 border-green-200'
                  : 'bg-red-100 text-red-700 border-2 border-red-200'
              }`}>
                {isPassed ? (
                  <TbCheck className="w-6 h-6" />
                ) : (
                  <TbX className="w-6 h-6" />
                )}
                <span>
                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}
                </span>
              </div>
            </motion.div>

            {/* Show Details Button */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 2.4 }}
              className="text-center"
            >
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200"
              >
                <TbChartBar className="w-5 h-5" />
                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>
              </button>
            </motion.div>
          </div>
        </motion.div>

        {/* Register Now Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2.6 }}
          className="text-center mb-8"
        >
          <Link to="/register">
            <motion.button
              className="px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <span>Register Now</span>
              <TbArrowRight className="w-6 h-6" />
            </motion.button>
          </Link>
        </motion.div>

        {/* Unlock Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2.8 }}
          className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center">
            <h3 className="text-2xl sm:text-3xl font-bold mb-2">
              🔓 Unlock These Amazing Features
            </h3>
            <p className="text-blue-100 text-lg">
              Join thousands of students already excelling with BrainWave
            </p>
          </div>

          {/* Features Grid */}
          <div className="p-6 sm:p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {premiumFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}
                  className="group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-800">{feature.title}</h4>
                  </div>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  <div className="mt-4 flex items-center text-blue-600 font-medium">
                    <TbStar className="w-5 h-5 mr-2" />
                    <span className="text-sm">Premium Feature</span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Better Quiz Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 3.4 }}
              className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center">
                <TbBrain className="w-6 h-6 mr-2 text-purple-600" />
                Advanced Quiz Features & Maximum Control
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Multiple Subject Selection */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 3.6 }}
                  className="bg-white rounded-xl p-5 shadow-sm border border-purple-100"
                >
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                      <TbBook className="w-5 h-5 text-white" />
                    </div>
                    <h5 className="font-bold text-gray-800">Multiple Subject Selection</h5>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Mix and match subjects in custom quizzes</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Subject-specific performance tracking</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Cross-subject comparison analytics</span>
                    </li>
                  </ul>
                </motion.div>

                {/* Maximum Control */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 3.8 }}
                  className="bg-white rounded-xl p-5 shadow-sm border border-blue-100"
                >
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3">
                      <TbSettings className="w-5 h-5 text-white" />
                    </div>
                    <h5 className="font-bold text-gray-800">Maximum Quiz Control</h5>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Choose question count (5-100 questions)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Select difficulty levels (Easy, Medium, Hard)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Pause and resume quiz sessions</span>
                    </li>
                  </ul>
                </motion.div>
              </div>

              {/* Advanced Features */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 4.0 }}
                className="mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5"
              >
                <h5 className="font-bold text-gray-800 mb-4 text-center">🚀 Advanced Quiz Features</h5>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { icon: "⏱️", title: "Smart Timer", desc: "Adaptive timing" },
                    { icon: "🎯", title: "Targeted Practice", desc: "Weak area focus" },
                    { icon: "📊", title: "Live Analytics", desc: "Real-time insights" },
                    { icon: "🏆", title: "Achievement System", desc: "Unlock rewards" }
                  ].map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}
                      className="text-center p-3 bg-white rounded-lg shadow-sm"
                    >
                      <div className="text-xl mb-1">{feature.icon}</div>
                      <div className="font-semibold text-xs text-gray-800">{feature.title}</div>
                      <div className="text-xs text-gray-600">{feature.desc}</div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>

            {/* Additional Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 3.6 }}
              className="mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-4 text-center">
                🎯 Why Students Choose BrainWave
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { icon: "🚀", title: "Instant Access", desc: "Start learning immediately" },
                  { icon: "📱", title: "Mobile Friendly", desc: "Study anywhere, anytime" },
                  { icon: "🎓", title: "Expert Content", desc: "Created by top educators" },
                  { icon: "🏆", title: "Proven Results", desc: "98% success rate" }
                ].map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}
                    className="text-center p-4 bg-white rounded-xl shadow-sm"
                  >
                    <div className="text-2xl mb-2">{benefit.icon}</div>
                    <div className="font-semibold text-gray-800 mb-1">{benefit.title}</div>
                    <div className="text-sm text-gray-600">{benefit.desc}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 4.2 }}
              className="text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white"
            >
              <h4 className="text-xl font-bold mb-2">Ready to Excel? 🌟</h4>
              <p className="text-blue-100 mb-4">Join BrainWave today and unlock your full potential!</p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                <Link to="/register">
                  <motion.button
                    className="px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>Create Free Account</span>
                    <TbArrowRight className="w-5 h-5" />
                  </motion.button>
                </Link>
                <div className="text-blue-200 text-sm">
                  ✨ No credit card required • Start immediately
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Question Details */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl p-6 mb-8"
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4">Question Review</h3>
            <div className="space-y-4">
              {result.questionResults?.map((q, index) => (
                <div key={index} className={`p-4 rounded-lg border-2 ${
                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                    }`}>
                      {q.isCorrect ? <TbCheck className="w-4 h-4" /> : <TbX className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-800 mb-2">{q.question}</p>
                      <div className="text-sm space-y-1">
                        <p>
                          <span className="text-gray-600">Your answer:</span>
                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                            {q.userAnswer || 'Not answered'}
                          </span>
                        </p>
                        {!q.isCorrect && (
                          <p>
                            <span className="text-gray-600">Correct answer:</span>
                            <span className="ml-2 font-medium text-green-600">{q.correctAnswer}</span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}



        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <button
            onClick={onTryAnother}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Try Another Quiz
          </button>
          
          <Link to="/">
            <button className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium">
              Back to Home
            </button>
          </Link>
        </motion.div>

        {/* Trial Badge */}
        <div className="text-center mt-8">
          <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium">
            🎯 Trial Mode - Register for unlimited access
          </span>
        </div>
      </div>
    </div>
  );
};

export default TrialQuizResult;
