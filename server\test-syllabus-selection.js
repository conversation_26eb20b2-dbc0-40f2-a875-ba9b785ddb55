const mongoose = require('mongoose');
require('dotenv').config();

async function testSyllabusSelection() {
  console.log('🧪 Testing Syllabus Selection Implementation...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Test 1: Check available syllabuses
    console.log('\n1️⃣ Testing getSyllabusesForAI...');
    const allSyllabuses = await Syllabus.find({ isActive: true });
    console.log(`Found ${allSyllabuses.length} active syllabuses:`);
    
    allSyllabuses.forEach((s, i) => {
      console.log(`${i + 1}. "${s.title}"`);
      console.log(`   Level: ${s.level}`);
      console.log(`   Subject: ${s.subject}`);
      console.log(`   Classes: ${s.classes.join(', ')}`);
      console.log(`   Status: ${s.processingStatus}`);
      console.log(`   Quality: ${s.qualityScore || 'N/A'}%`);
      console.log(`   ID: ${s._id}`);
      console.log('');
    });

    // Test 2: Test subjects for level
    console.log('2️⃣ Testing getAvailableSubjects...');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);

    // Test 3: Test AI content retrieval
    if (allSyllabuses.length > 0) {
      const testSyllabus = allSyllabuses[0];
      console.log('\n3️⃣ Testing AI content retrieval...');
      console.log(`Testing with syllabus: ${testSyllabus.title}`);
      
      try {
        const aiTopics = testSyllabus.getTopicsForAI();
        console.log(`AI Topics available: ${Object.keys(aiTopics).length}`);
        console.log('Sample topics:', Object.keys(aiTopics).slice(0, 3));
        
        const extractedText = testSyllabus.extractedText;
        console.log(`Extracted text length: ${extractedText ? extractedText.length : 0} characters`);
        
        if (extractedText) {
          console.log('Sample text:', extractedText.substring(0, 200) + '...');
        }
      } catch (error) {
        console.error('Error testing AI content:', error.message);
      }
    }

    // Test 4: Test AI service integration
    console.log('\n4️⃣ Testing AI Service Integration...');
    const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
    const aiService = new AIQuestionGenerationService();
    
    if (allSyllabuses.length > 0) {
      const testSyllabus = allSyllabuses[0];
      console.log(`Testing getSyllabusData with selected syllabus: ${testSyllabus._id}`);
      
      try {
        const syllabusData = await aiService.getSyllabusData(
          testSyllabus.level,
          testSyllabus.classes[0],
          testSyllabus.subject,
          testSyllabus._id.toString()
        );
        
        console.log('Syllabus data retrieved:');
        console.log(`- Source: ${syllabusData.source}`);
        console.log(`- Topics available: ${syllabusData.topics ? Object.keys(syllabusData.topics).length : 0}`);
        console.log(`- Text length: ${syllabusData.extractedText ? syllabusData.extractedText.length : 0}`);
        console.log(`- Syllabus ID: ${syllabusData.syllabusId}`);
        console.log(`- Syllabus Title: ${syllabusData.syllabusTitle || 'N/A'}`);
      } catch (error) {
        console.error('Error testing AI service:', error.message);
      }
    }

    // Test 5: Simulate API call structure
    console.log('\n5️⃣ Testing API Call Structure...');
    const samplePayload = {
      examId: null,
      questionTypes: ['multiple_choice'],
      subjects: ['science and technology'],
      level: 'primary',
      class: '5',
      difficultyLevels: ['medium'],
      syllabusTopics: [],
      totalQuestions: 1,
      questionDistribution: { multiple_choice: 1, fill_blank: 0, picture_based: 0 },
      selectedSyllabusId: allSyllabuses.length > 0 ? allSyllabuses[0]._id.toString() : null
    };
    
    console.log('Sample API payload:');
    console.log(JSON.stringify(samplePayload, null, 2));
    
    // Test the generation parameters
    console.log('\n6️⃣ Testing Generation Parameters...');
    if (samplePayload.selectedSyllabusId) {
      console.log(`✅ Selected syllabus ID: ${samplePayload.selectedSyllabusId}`);
      console.log(`✅ Level: ${samplePayload.level}`);
      console.log(`✅ Subject: ${samplePayload.subjects[0]}`);
      console.log(`✅ Class: ${samplePayload.class}`);
      console.log('✅ All parameters ready for AI generation');
    } else {
      console.log('⚠️ No syllabus selected - will use fallback');
    }

    console.log('\n🎯 Summary:');
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Active syllabuses: ${allSyllabuses.length}`);
    console.log(`✅ Primary subjects: ${primarySubjects.length}`);
    console.log(`✅ AI service: Ready`);
    console.log(`✅ API structure: Prepared`);
    
    if (allSyllabuses.length > 0) {
      console.log('\n🚀 Ready for testing:');
      console.log('1. Open /admin/ai-questions');
      console.log('2. Click "Auto Generate Exam"');
      console.log('3. Select "Primary" level');
      console.log('4. Choose "science and technology" subject');
      console.log('5. Select syllabus from dropdown');
      console.log('6. Generate questions');
    } else {
      console.log('\n⚠️ No active syllabuses found. Upload and process a PDF first.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testSyllabusSelection().catch(console.error);
}

module.exports = { testSyllabusSelection };
