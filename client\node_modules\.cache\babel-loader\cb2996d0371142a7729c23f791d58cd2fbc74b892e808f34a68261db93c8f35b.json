{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport \"./TrialQuiz.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizPlay = ({\n  quizData,\n  onComplete,\n  onBack\n}) => {\n  _s();\n  const {\n    exam,\n    trialUserInfo\n  } = quizData;\n  const questions = exam.questions || [];\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    playSuccessSound();\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Sound effects for navigation\n  const playNavigationSound = () => {\n    try {\n      const audio = new Audio('/sounds/click.mp3');\n      audio.volume = 0.2;\n      audio.play().catch(() => {});\n    } catch (error) {\n      console.log('Navigation sound not available');\n    }\n  };\n  const playSuccessSound = () => {\n    try {\n      const audio = new Audio('/sounds/success.mp3');\n      audio.volume = 0.3;\n      audio.play().catch(() => {});\n    } catch (error) {\n      console.log('Success sound not available');\n    }\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n  const goToQuestion = index => {\n    playNavigationSound();\n    setCurrentQuestionIndex(index);\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"No Questions Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz doesn't have any questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.type) || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.answerType) || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) === 'object' && (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionA, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionB, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionC, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionD].filter(Boolean);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-xl border-b-2 border-blue-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onBack,\n              className: \"p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-w-0 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate\",\n                children: exam.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base text-gray-600\",\n                  children: exam.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base font-medium text-blue-600\",\n                  children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '16px',\n              flexShrink: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `trial-timer ${timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'}`,\n                children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                  className: \"trial-timer-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: formatTime(timeLeft)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), timeLeft <= 300 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timer-warning-ring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trial-progress-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trial-progress-label\",\n              children: [\"Progress: \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trial-progress-percentage\",\n              children: [Math.round((currentQuestionIndex + 1) / questions.length * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trial-progress-fill\",\n              style: {\n                width: `${(currentQuestionIndex + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:hidden mt-3 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        transition: {\n          duration: 0.4,\n          ease: \"easeOut\"\n        },\n        className: \"trial-question-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trial-question-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-question-number\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trial-question-number-badge\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: currentQuestionIndex + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'rgba(255, 255, 255, 0.9)',\n                  fontSize: '14px'\n                },\n                children: \"Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'white',\n                  fontWeight: '500',\n                  fontSize: '18px'\n                },\n                children: [currentQuestionIndex + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"trial-question-title\",\n            children: currentQuestion.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '32px 40px'\n          },\n          children: [currentQuestion.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '32px',\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQuestion.image,\n              alt: \"Question\",\n              style: {\n                maxWidth: '100%',\n                maxHeight: '400px',\n                borderRadius: '16px',\n                boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-10 sm:mb-12\",\n            children: isMCQ && questionOptions.length > 0 ?\n            /*#__PURE__*/\n            // Multiple Choice Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4 sm:space-y-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800 mb-6\",\n                children: \"Choose your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), questionOptions.map((option, index) => {\n                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(currentQuestion._id, optionLetter),\n                  className: `trial-option ${isSelected ? 'selected' : ''}`,\n                  whileHover: {\n                    scale: isSelected ? 1.01 : 1.005\n                  },\n                  whileTap: {\n                    scale: 0.995\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"trial-option-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"trial-option-letter\",\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        style: {\n                          width: '24px',\n                          height: '24px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 43\n                      }, this) : optionLetter\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"trial-option-text\",\n                      children: option\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Fill-in-the-blank / Free Text Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800\",\n                children: \"Type your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: selectedAnswers[currentQuestion._id] || '',\n                  onChange: e => handleAnswerSelect(currentQuestion._id, e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"trial-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-nav-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToPrevious,\n              disabled: currentQuestionIndex === 0,\n              className: `trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`,\n              whileHover: currentQuestionIndex > 0 ? {\n                scale: 1.02\n              } : {},\n              whileTap: currentQuestionIndex > 0 ? {\n                scale: 0.98\n              } : {},\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: handleSubmitQuiz,\n              disabled: isSubmitting,\n              className: `trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`,\n              whileHover: !isSubmitting ? {\n                scale: 1.02\n              } : {},\n              whileTap: !isSubmitting ? {\n                scale: 0.98\n              } : {},\n              children: [isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trial-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbCheck, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isSubmitting ? 'Submitting...' : 'Submit Quiz'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToNext,\n              className: \"trial-btn trial-btn-primary\",\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, currentQuestionIndex, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\",\n      children: \"Trial Mode\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizPlay, \"poqwUhvGWiexGFrQc9xn7WhRFI8=\");\n_c = TrialQuizPlay;\nexport default TrialQuizPlay;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "TbArrowLeft", "TbArrowRight", "TbClock", "TbCheck", "message", "submitTrialResult", "jsxDEV", "_jsxDEV", "TrialQuizPlay", "quizData", "onComplete", "onBack", "_s", "exam", "trialUserInfo", "questions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedAnswer<PERSON>", "setSelectedAnswers", "timeLeft", "setTimeLeft", "duration", "isSubmitting", "setIsSubmitting", "startTime", "Date", "now", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "questionId", "answer", "playSuccessSound", "timeSpent", "round", "response", "examId", "_id", "answers", "success", "data", "error", "console", "playNavigationSound", "audio", "Audio", "volume", "play", "catch", "log", "goToNext", "length", "goToPrevious", "goToQuestion", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "isLastQuestion", "answeredQuestions", "Object", "keys", "questionType", "type", "answerType", "isMCQ", "isFillBlank", "questionOptions", "Array", "isArray", "options", "values", "optionA", "optionB", "optionC", "optionD", "filter", "Boolean", "name", "subject", "style", "display", "alignItems", "gap", "flexShrink", "position", "width", "div", "initial", "opacity", "y", "animate", "exit", "transition", "ease", "color", "fontSize", "fontWeight", "padding", "image", "marginBottom", "textAlign", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "borderRadius", "boxShadow", "map", "option", "optionLetter", "String", "fromCharCode", "isSelected", "button", "whileHover", "scale", "whileTap", "height", "value", "onChange", "e", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, Tb<PERSON>lock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    playSuccessSound();\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n      \n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Sound effects for navigation\n  const playNavigationSound = () => {\n    try {\n      const audio = new Audio('/sounds/click.mp3');\n      audio.volume = 0.2;\n      audio.play().catch(() => {});\n    } catch (error) {\n      console.log('Navigation sound not available');\n    }\n  };\n\n  const playSuccessSound = () => {\n    try {\n      const audio = new Audio('/sounds/success.mp3');\n      audio.volume = 0.3;\n      audio.play().catch(() => {});\n    } catch (error) {\n      console.log('Success sound not available');\n    }\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    playNavigationSound();\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion?.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Enhanced Header with Progress */}\n      <div className=\"bg-white shadow-xl border-b-2 border-blue-200\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n              <button\n                onClick={onBack}\n                className=\"p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md\"\n              >\n                <TbArrowLeft className=\"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800\" />\n              </button>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate\">{exam.name}</h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-sm sm:text-base text-gray-600\">{exam.subject}</span>\n                  <span className=\"text-gray-400\">•</span>\n                  <span className=\"text-sm sm:text-base font-medium text-blue-600\">\n                    Question {currentQuestionIndex + 1} of {questions.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexShrink: 0 }}>\n              {/* Enhanced Timer with Professional Styling */}\n              <div style={{ position: 'relative' }}>\n                <div className={`trial-timer ${\n                  timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'\n                }`}>\n                  <TbClock className=\"trial-timer-icon\" />\n                  <span>{formatTime(timeLeft)}</span>\n                </div>\n                {/* Warning animation for low time */}\n                {timeLeft <= 300 && (\n                  <div className=\"timer-warning-ring\"></div>\n                )}\n              </div>\n\n              <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {answeredQuestions}/{questions.length} answered\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Progress Bar */}\n          <div className=\"trial-progress-container\">\n            <div className=\"trial-progress-header\">\n              <span className=\"trial-progress-label\">\n                Progress: {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              <span className=\"trial-progress-percentage\">\n                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div className=\"trial-progress-bar\">\n              <div\n                className=\"trial-progress-fill\"\n                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Mobile progress indicator */}\n          <div className=\"sm:hidden mt-3 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\">\n              <span className=\"text-sm font-medium text-gray-700\">\n                {answeredQuestions}/{questions.length} answered\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Question Content */}\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\">\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"trial-question-card\"\n        >\n          {/* Question Header */}\n          <div className=\"trial-question-header\">\n            <div className=\"trial-question-number\">\n              <div className=\"trial-question-number-badge\">\n                <span>{currentQuestionIndex + 1}</span>\n              </div>\n              <div>\n                <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>Question</div>\n                <div style={{ color: 'white', fontWeight: '500', fontSize: '18px' }}>\n                  {currentQuestionIndex + 1} of {questions.length}\n                </div>\n              </div>\n            </div>\n\n            <h2 className=\"trial-question-title\">\n              {currentQuestion.name}\n            </h2>\n          </div>\n\n          {/* Question Content */}\n          <div style={{ padding: '32px 40px' }}>\n            {/* Question Image (if exists) */}\n            {currentQuestion.image && (\n              <div style={{ marginBottom: '32px', textAlign: 'center' }}>\n                <img\n                  src={currentQuestion.image}\n                  alt=\"Question\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    borderRadius: '16px',\n                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* Answer Section */}\n            <div className=\"mb-10 sm:mb-12\">\n              {isMCQ && questionOptions.length > 0 ? (\n                // Multiple Choice Questions\n                <div className=\"space-y-4 sm:space-y-5\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800 mb-6\">\n                    Choose your answer:\n                  </h3>\n                  {questionOptions.map((option, index) => {\n                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n\n                    return (\n                      <motion.button\n                        key={index}\n                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                        className={`trial-option ${isSelected ? 'selected' : ''}`}\n                        whileHover={{ scale: isSelected ? 1.01 : 1.005 }}\n                        whileTap={{ scale: 0.995 }}\n                      >\n                        <div className=\"trial-option-content\">\n                          <div className=\"trial-option-letter\">\n                            {isSelected ? <TbCheck style={{ width: '24px', height: '24px' }} /> : optionLetter}\n                          </div>\n                          <span className=\"trial-option-text\">\n                            {option}\n                          </span>\n                        </div>\n                      </motion.button>\n                    );\n                  })}\n                </div>\n              ) : (\n                // Fill-in-the-blank / Free Text Questions\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800\">\n                    Type your answer:\n                  </h3>\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"trial-input\"\n                    />\n                    <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                      ✏️\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"trial-nav-buttons\">\n              <motion.button\n                onClick={goToPrevious}\n                disabled={currentQuestionIndex === 0}\n                className={`trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`}\n                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}\n                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}\n              >\n                <TbArrowLeft style={{ width: '20px', height: '20px' }} />\n                <span>Previous</span>\n              </motion.button>\n\n              {isLastQuestion ? (\n                <motion.button\n                  onClick={handleSubmitQuiz}\n                  disabled={isSubmitting}\n                  className={`trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`}\n                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n                >\n                  {isSubmitting ? (\n                    <div className=\"trial-spinner\" />\n                  ) : (\n                    <TbCheck style={{ width: '20px', height: '20px' }} />\n                  )}\n                  <span>\n                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\n                  </span>\n                </motion.button>\n              ) : (\n                <motion.button\n                  onClick={goToNext}\n                  className=\"trial-btn trial-btn-primary\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Next Question</span>\n                  <TbArrowRight style={{ width: '20px', height: '20px' }} />\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\">\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAGL,QAAQ;EACxC,MAAMM,SAAS,GAAGF,IAAI,CAACE,SAAS,IAAI,EAAE;EAEtC,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,CAACiB,IAAI,CAACS,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,SAAS,CAAC,GAAG7B,QAAQ,CAAC8B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAExC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIuB,QAAQ,IAAI,CAAC,EAAE;MACjBQ,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAACU,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMa,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDC,gBAAgB,CAAC,CAAC;IAClBzB,kBAAkB,CAACY,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAG9B,WAAW,CAAC,YAAY;IAC/C,IAAIyB,YAAY,EAAE;IAElBC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMqB,SAAS,GAAGT,IAAI,CAACU,KAAK,CAAC,CAACpB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;;MAE/D,MAAMsB,QAAQ,GAAG,MAAM1C,iBAAiB,CAAC;QACvC2C,MAAM,EAAEnC,IAAI,CAACoC,GAAG;QAChBC,OAAO,EAAEhC,eAAe;QACxB2B,SAAS;QACT/B;MACF,CAAC,CAAC;MAEF,IAAIiC,QAAQ,CAACI,OAAO,EAAE;QACpBzC,UAAU,CAACqC,QAAQ,CAACK,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLhD,OAAO,CAACiD,KAAK,CAACN,QAAQ,CAAC3C,OAAO,IAAI,uBAAuB,CAAC;QAC1DoB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjD,OAAO,CAACiD,KAAK,CAAC,yCAAyC,CAAC;MACxD7B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACX,IAAI,CAACoC,GAAG,EAAE/B,eAAe,EAAEJ,aAAa,EAAEW,SAAS,EAAEf,UAAU,EAAEa,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMgC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MAC5CD,KAAK,CAACE,MAAM,GAAG,GAAG;MAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACO,GAAG,CAAC,gCAAgC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMjB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMY,KAAK,GAAG,IAAIC,KAAK,CAAC,qBAAqB,CAAC;MAC9CD,KAAK,CAACE,MAAM,GAAG,GAAG;MAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACO,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI9C,oBAAoB,GAAGD,SAAS,CAACgD,MAAM,GAAG,CAAC,EAAE;MAC/CR,mBAAmB,CAAC,CAAC;MACrBtC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMgD,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhD,oBAAoB,GAAG,CAAC,EAAE;MAC5BuC,mBAAmB,CAAC,CAAC;MACrBtC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMiD,YAAY,GAAIC,KAAK,IAAK;IAC9BX,mBAAmB,CAAC,CAAC;IACrBtC,uBAAuB,CAACiD,KAAK,CAAC;EAChC,CAAC;EAED,IAAInD,SAAS,CAACgD,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACExD,OAAA;MAAK4D,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpH7D,OAAA;QAAK4D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7D,OAAA;UAAI4D,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFjE,OAAA;UAAG4D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3EjE,OAAA;UACEkE,OAAO,EAAE9D,MAAO;UAChBwD,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAG3D,SAAS,CAACC,oBAAoB,CAAC;EACvD,MAAM2D,cAAc,GAAG3D,oBAAoB,KAAKD,SAAS,CAACgD,MAAM,GAAG,CAAC;EACpE,MAAMa,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAAC5D,eAAe,CAAC,CAAC6C,MAAM;;EAE7D;EACA,MAAMgB,YAAY,GAAG,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,MAAIN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,UAAU,KAAI,KAAK;EAClF,MAAMC,KAAK,GAAGH,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,iBAAiB;EACxG,MAAMI,WAAW,GAAGJ,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,mBAAmB,IAAIA,YAAY,KAAK,WAAW;;EAEnH;EACA,IAAIK,eAAe,GAAG,EAAE;EACxB,IAAIF,KAAK,EAAE;IACT,IAAIG,KAAK,CAACC,OAAO,CAACZ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,CAAC,EAAE;MAC3CH,eAAe,GAAGV,eAAe,CAACa,OAAO;IAC3C,CAAC,MAAM,IAAI,QAAOb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,MAAK,QAAQ,IAAI,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,MAAK,IAAI,EAAE;MAC5FH,eAAe,GAAGP,MAAM,CAACW,MAAM,CAACd,eAAe,CAACa,OAAO,CAAC;IAC1D,CAAC,MAAM;MACLH,eAAe,GAAG,CAACV,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,OAAO,EAAEf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgB,OAAO,EAAEhB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiB,OAAO,EAAEjB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkB,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC5I;EACF;EAEA,oBACEvF,OAAA;IAAK4D,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExE7D,OAAA;MAAK4D,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D7D,OAAA;QAAK4D,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE7D,OAAA;UAAK4D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD7D,OAAA;YAAK4D,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtE7D,OAAA;cACEkE,OAAO,EAAE9D,MAAO;cAChBwD,SAAS,EAAC,mGAAmG;cAAAC,QAAA,eAE7G7D,OAAA,CAACP,WAAW;gBAACmE,SAAS,EAAC;cAA+D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACTjE,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7D,OAAA;gBAAI4D,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,EAAEvD,IAAI,CAACkF;cAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9JjE,OAAA;gBAAK4D,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C7D,OAAA;kBAAM4D,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAEvD,IAAI,CAACmF;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1EjE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCjE,OAAA;kBAAM4D,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAAC,WACtD,EAACpD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACgD,MAAM;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YAAK0F,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAE,CAAE;YAAAjC,QAAA,gBAEhF7D,OAAA;cAAK0F,KAAK,EAAE;gBAAEK,QAAQ,EAAE;cAAW,CAAE;cAAAlC,QAAA,gBACnC7D,OAAA;gBAAK4D,SAAS,EAAG,eACf/C,QAAQ,IAAI,EAAE,GAAG,UAAU,GAAGA,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAG,QAC7D,EAAE;gBAAAgD,QAAA,gBACD7D,OAAA,CAACL,OAAO;kBAACiE,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxCjE,OAAA;kBAAA6D,QAAA,EAAOnC,UAAU,CAACb,QAAQ;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EAELpD,QAAQ,IAAI,GAAG,iBACdb,OAAA;gBAAK4D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF7D,OAAA;gBAAM4D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAAC7D,SAAS,CAACgD,MAAM,EAAC,WACxC;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjE,OAAA;UAAK4D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC7D,OAAA;YAAK4D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC7D,OAAA;cAAM4D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,YAC3B,EAACpD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACgD,MAAM;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACPjE,OAAA;cAAM4D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,GACxChC,IAAI,CAACU,KAAK,CAAE,CAAC9B,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAACgD,MAAM,GAAI,GAAG,CAAC,EAAC,GACrE;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNjE,OAAA;YAAK4D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjC7D,OAAA;cACE4D,SAAS,EAAC,qBAAqB;cAC/B8B,KAAK,EAAE;gBAAEM,KAAK,EAAG,GAAG,CAACvF,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAACgD,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjE,OAAA;UAAK4D,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9D7D,OAAA;YAAK4D,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3E7D,OAAA;cAAM4D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAAC7D,SAAS,CAACgD,MAAM,EAAC,WACxC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAKNjE,OAAA;MAAK4D,SAAS,EAAC,8DAA8D;MAAAC,QAAA,eAC3E7D,OAAA,CAACR,MAAM,CAACyG,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BG,UAAU,EAAE;UAAExF,QAAQ,EAAE,GAAG;UAAEyF,IAAI,EAAE;QAAU,CAAE;QAC/C5C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B7D,OAAA;UAAK4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC7D,OAAA;YAAK4D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC7D,OAAA;cAAK4D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1C7D,OAAA;gBAAA6D,QAAA,EAAOpD,oBAAoB,GAAG;cAAC;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACNjE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAK0F,KAAK,EAAE;kBAAEe,KAAK,EAAE,0BAA0B;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAA7C,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnFjE,OAAA;gBAAK0F,KAAK,EAAE;kBAAEe,KAAK,EAAE,OAAO;kBAAEE,UAAU,EAAE,KAAK;kBAAED,QAAQ,EAAE;gBAAO,CAAE;gBAAA7C,QAAA,GACjEpD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACgD,MAAM;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YAAI4D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EACjCM,eAAe,CAACqB;UAAI;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNjE,OAAA;UAAK0F,KAAK,EAAE;YAAEkB,OAAO,EAAE;UAAY,CAAE;UAAA/C,QAAA,GAElCM,eAAe,CAAC0C,KAAK,iBACpB7G,OAAA;YAAK0F,KAAK,EAAE;cAAEoB,YAAY,EAAE,MAAM;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAlD,QAAA,eACxD7D,OAAA;cACEgH,GAAG,EAAE7C,eAAe,CAAC0C,KAAM;cAC3BI,GAAG,EAAC,UAAU;cACdvB,KAAK,EAAE;gBACLwB,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,OAAO;gBAClBC,YAAY,EAAE,MAAM;gBACpBC,SAAS,EAAE;cACb;YAAE;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGDjE,OAAA;YAAK4D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bc,KAAK,IAAIE,eAAe,CAACrB,MAAM,GAAG,CAAC;YAAA;YAClC;YACAxD,OAAA;cAAK4D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7D,OAAA;gBAAI4D,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJY,eAAe,CAACyC,GAAG,CAAC,CAACC,MAAM,EAAE5D,KAAK,KAAK;gBACtC,MAAM6D,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG/D,KAAK,CAAC,CAAC,CAAC;gBACtD,MAAMgE,UAAU,GAAGhH,eAAe,CAACwD,eAAe,CAACzB,GAAG,CAAC,KAAK8E,YAAY;gBAExE,oBACExH,OAAA,CAACR,MAAM,CAACoI,MAAM;kBAEZ1D,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACiC,eAAe,CAACzB,GAAG,EAAE8E,YAAY,CAAE;kBACrE5D,SAAS,EAAG,gBAAe+D,UAAU,GAAG,UAAU,GAAG,EAAG,EAAE;kBAC1DE,UAAU,EAAE;oBAAEC,KAAK,EAAEH,UAAU,GAAG,IAAI,GAAG;kBAAM,CAAE;kBACjDI,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAM,CAAE;kBAAAjE,QAAA,eAE3B7D,OAAA;oBAAK4D,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC7D,OAAA;sBAAK4D,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EACjC8D,UAAU,gBAAG3H,OAAA,CAACJ,OAAO;wBAAC8F,KAAK,EAAE;0BAAEM,KAAK,EAAE,MAAM;0BAAEgC,MAAM,EAAE;wBAAO;sBAAE;wBAAAlE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAGuD;oBAAY;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eACNjE,OAAA;sBAAM4D,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAChC0D;oBAAM;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAbDN,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcG,CAAC;cAEpB,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;YAAA;YAEN;YACAjE,OAAA;cAAK4D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7D,OAAA;gBAAI4D,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAE/D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjE,OAAA;gBAAK4D,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7D,OAAA;kBACEyE,IAAI,EAAC,MAAM;kBACXwD,KAAK,EAAEtH,eAAe,CAACwD,eAAe,CAACzB,GAAG,CAAC,IAAI,EAAG;kBAClDwF,QAAQ,EAAGC,CAAC,IAAKjG,kBAAkB,CAACiC,eAAe,CAACzB,GAAG,EAAEyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACzEI,WAAW,EAAC,0BAA0B;kBACtCzE,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFjE,OAAA;kBAAK4D,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjE,OAAA;YAAK4D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7D,OAAA,CAACR,MAAM,CAACoI,MAAM;cACZ1D,OAAO,EAAET,YAAa;cACtB6E,QAAQ,EAAE7H,oBAAoB,KAAK,CAAE;cACrCmD,SAAS,EAAG,iCAAgCnD,oBAAoB,KAAK,CAAC,GAAG,EAAE,GAAG,EAAG,EAAE;cACnFoH,UAAU,EAAEpH,oBAAoB,GAAG,CAAC,GAAG;gBAAEqH,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAC5DC,QAAQ,EAAEtH,oBAAoB,GAAG,CAAC,GAAG;gBAAEqH,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAAjE,QAAA,gBAE1D7D,OAAA,CAACP,WAAW;gBAACiG,KAAK,EAAE;kBAAEM,KAAK,EAAE,MAAM;kBAAEgC,MAAM,EAAE;gBAAO;cAAE;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDjE,OAAA;gBAAA6D,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEfG,cAAc,gBACbpE,OAAA,CAACR,MAAM,CAACoI,MAAM;cACZ1D,OAAO,EAAE7C,gBAAiB;cAC1BiH,QAAQ,EAAEtH,YAAa;cACvB4C,SAAS,EAAG,aAAY5C,YAAY,GAAG,qBAAqB,GAAG,mBAAoB,EAAE;cACrF6G,UAAU,EAAE,CAAC7G,YAAY,GAAG;gBAAE8G,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cACjDC,QAAQ,EAAE,CAAC/G,YAAY,GAAG;gBAAE8G,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAAjE,QAAA,GAE9C7C,YAAY,gBACXhB,OAAA;gBAAK4D,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjCjE,OAAA,CAACJ,OAAO;gBAAC8F,KAAK,EAAE;kBAAEM,KAAK,EAAE,MAAM;kBAAEgC,MAAM,EAAE;gBAAO;cAAE;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrD,eACDjE,OAAA;gBAAA6D,QAAA,EACG7C,YAAY,GAAG,eAAe,GAAG;cAAa;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,gBAEhBjE,OAAA,CAACR,MAAM,CAACoI,MAAM;cACZ1D,OAAO,EAAEX,QAAS;cAClBK,SAAS,EAAC,6BAA6B;cACvCiE,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAjE,QAAA,gBAE1B7D,OAAA;gBAAA6D,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BjE,OAAA,CAACN,YAAY;gBAACgG,KAAK,EAAE;kBAAEM,KAAK,EAAE,MAAM;kBAAEgC,MAAM,EAAE;gBAAO;cAAE;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA5IDxD,oBAAoB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6If;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNjE,OAAA;MAAK4D,SAAS,EAAC,oGAAoG;MAAAC,QAAA,EAAC;IAEpH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CA3XIJ,aAAa;AAAAsI,EAAA,GAAbtI,aAAa;AA6XnB,eAAeA,aAAa;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}