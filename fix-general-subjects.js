require('dotenv').config();
const mongoose = require('mongoose');
const Exam = require('./server/models/examModel');

// Subject mapping based on category or exam name
const subjectMapping = {
  'Mathematics': 'Mathematics',
  'Math': 'Mathematics', 
  'Science': 'Science and Technology',
  'Science and Technology': 'Science and Technology',
  'English': 'English',
  'Kiswahili': 'Kiswahili',
  'Geography': 'Geography',
  'History': 'History',
  'Physics': 'Physics',
  'Chemistry': 'Chemistry',
  'Biology': 'Biology',
  'Civics': 'Civics',
  'Commerce': 'Commerce',
  'Economics': 'Economics',
  'Computer': 'Computer Studies',
  'Agriculture': 'Agriculture',
  'French': 'French',
  'Religion': 'Religion',
  'Sport': 'Sport and Art',
  'Art': 'Sport and Art',
  'Health': 'Health and Environment',
  'Environment': 'Health and Environment',
  'Moral': 'Civic and Moral',
  'Civic': 'Civic and Moral',
  'Social': 'Social Studies',
  'Studies': 'Social Studies'
};

function determineSubjectFromName(examName, category) {
  const name = examName.toLowerCase();
  const cat = category.toLowerCase();
  
  // Check category first
  for (const [key, subject] of Object.entries(subjectMapping)) {
    if (cat.includes(key.toLowerCase())) {
      return subject;
    }
  }
  
  // Check exam name
  for (const [key, subject] of Object.entries(subjectMapping)) {
    if (name.includes(key.toLowerCase())) {
      return subject;
    }
  }
  
  // Default fallback based on level
  return 'Mathematics'; // Most common subject
}

async function fixGeneralSubjects() {
  try {
    console.log('🔄 Connecting to database...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    // Find all exams with "General" subject
    const generalExams = await Exam.find({ 
      $or: [
        { subject: 'General' },
        { subject: { $exists: false } },
        { subject: null },
        { subject: '' }
      ]
    });

    console.log(`📊 Found ${generalExams.length} exams with General/missing subjects`);

    let updatedCount = 0;
    const updatePromises = [];

    for (const exam of generalExams) {
      const newSubject = determineSubjectFromName(exam.name, exam.category);
      
      console.log(`🔄 Updating: "${exam.name}" (${exam.category}) -> ${newSubject}`);
      
      updatePromises.push(
        Exam.findByIdAndUpdate(exam._id, { 
          subject: newSubject 
        })
      );
      updatedCount++;
    }

    // Execute all updates
    await Promise.all(updatePromises);

    console.log(`✅ Updated ${updatedCount} exams with proper subjects`);

    // Verify the update
    const updatedSubjects = await Exam.distinct('subject');
    console.log('\n📚 Current subjects in database:');
    updatedSubjects.forEach(subject => {
      console.log(`- ${subject}`);
    });

    // Check if any "General" subjects remain
    const remainingGeneral = await Exam.countDocuments({ subject: 'General' });
    console.log(`\n📈 Remaining "General" subjects: ${remainingGeneral}`);
    
    if (remainingGeneral === 0) {
      console.log('🎉 All "General" subjects have been updated!');
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing general subjects:', error);
    process.exit(1);
  }
}

fixGeneralSubjects();
