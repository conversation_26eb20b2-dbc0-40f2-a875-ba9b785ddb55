Write-Host "Starting BrainWave Application Services..." -ForegroundColor Green
Write-Host ""

# Start Server
Write-Host "Starting Server (Backend)..." -ForegroundColor Yellow
Set-Location "C:\Users\<USER>\Desktop\20\New folder\server"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal

# Wait for server to start
Write-Host "Waiting 5 seconds for server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start Client
Write-Host "Starting Client (Frontend)..." -ForegroundColor Yellow
Set-Location "C:\Users\<USER>\Desktop\20\New folder\client"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal

Write-Host ""
Write-Host "Services are starting..." -ForegroundColor Green
Write-Host "Server will be available at: http://localhost:5000" -ForegroundColor Cyan
Write-Host "Client will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
