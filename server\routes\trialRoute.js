const router = require("express").Router();
const Exam = require("../models/examModel");
const Question = require("../models/questionModel");

// Get trial quiz based on level and class (no authentication required)
router.post("/get-trial-quiz", async (req, res) => {
  try {
    const { level, class: userClass } = req.body;

    console.log("🎯 Trial quiz request:", { level, userClass });

    if (!level || !userClass) {
      return res.status(400).send({
        message: "Level and class are required",
        success: false,
      });
    }

    // Find exams matching the user's level and class
    const matchingExams = await Exam.find({
      level: { $in: [level, level.charAt(0).toUpperCase() + level.slice(1)] },
      class: userClass,
      questions: { $exists: true, $not: { $size: 0 } }
    }).populate('questions');

    console.log(`📚 Found ${matchingExams.length} matching exams for level: ${level}, class: ${userClass}`);

    if (matchingExams.length === 0) {
      // Fallback: try to find any exam for the level (ignore class)
      const fallbackExams = await Exam.find({
        level: { $in: [level, level.charAt(0).toUpperCase() + level.slice(1)] },
        questions: { $exists: true, $not: { $size: 0 } }
      }).populate('questions');

      console.log(`🔄 Fallback: Found ${fallbackExams.length} exams for level: ${level}`);

      if (fallbackExams.length === 0) {
        return res.status(404).send({
          message: "No trial quiz available for your level at the moment",
          success: false,
        });
      }

      // Select a random exam from fallback
      const selectedExam = fallbackExams[Math.floor(Math.random() * fallbackExams.length)];
      
      return res.send({
        message: "Trial quiz found (general level match)",
        data: {
          exam: selectedExam,
          isTrialMode: true,
          trialInfo: {
            level,
            class: userClass,
            examLevel: selectedExam.level,
            examClass: selectedExam.class
          }
        },
        success: true,
      });
    }

    // Select a random exam from matching exams
    const selectedExam = matchingExams[Math.floor(Math.random() * matchingExams.length)];

    console.log(`✅ Selected trial exam: ${selectedExam.name} (${selectedExam._id})`);

    // Limit questions for trial (max 5 questions)
    const limitedQuestions = selectedExam.questions.slice(0, 5);
    
    const trialExam = {
      ...selectedExam.toObject(),
      questions: limitedQuestions,
      duration: Math.min(selectedExam.duration, 10), // Max 10 minutes for trial
      totalMarks: limitedQuestions.length,
      passingMarks: Math.ceil(limitedQuestions.length * 0.6), // 60% pass rate
    };

    res.send({
      message: "Trial quiz found successfully",
      data: {
        exam: trialExam,
        isTrialMode: true,
        trialInfo: {
          level,
          class: userClass,
          originalQuestionCount: selectedExam.questions.length,
          trialQuestionCount: limitedQuestions.length,
          originalDuration: selectedExam.duration,
          trialDuration: trialExam.duration
        }
      },
      success: true,
    });

  } catch (error) {
    console.error("❌ Trial quiz error:", error);
    res.status(500).send({
      message: "Error fetching trial quiz",
      data: error.message,
      success: false,
    });
  }
});

// Submit trial quiz results (no authentication required)
router.post("/submit-trial-result", async (req, res) => {
  try {
    const { 
      examId, 
      answers, 
      timeSpent, 
      trialUserInfo 
    } = req.body;

    console.log("📝 Trial result submission:", { 
      examId, 
      answersCount: Object.keys(answers || {}).length,
      timeSpent,
      trialUserInfo 
    });

    if (!examId || !answers) {
      return res.status(400).send({
        message: "Exam ID and answers are required",
        success: false,
      });
    }

    // Get the exam and questions
    const exam = await Exam.findById(examId).populate('questions');
    
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    // Calculate results
    let correctAnswers = 0;
    let totalQuestions = 0;
    const questionResults = [];

    // Limit to first 5 questions for trial
    const trialQuestions = exam.questions.slice(0, 5);

    trialQuestions.forEach((question) => {
      totalQuestions++;
      const userAnswer = answers[question._id];
      const isCorrect = userAnswer === question.correctOption;
      
      if (isCorrect) {
        correctAnswers++;
      }

      questionResults.push({
        questionId: question._id,
        question: question.name,
        userAnswer,
        correctAnswer: question.correctOption,
        isCorrect,
        options: question.options
      });
    });

    const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    const passed = percentage >= 60; // 60% pass rate

    const result = {
      examId,
      examName: exam.name,
      subject: exam.subject,
      totalQuestions,
      correctAnswers,
      wrongAnswers: totalQuestions - correctAnswers,
      percentage,
      passed,
      timeSpent: timeSpent || 0,
      questionResults,
      isTrialResult: true,
      trialUserInfo,
      createdAt: new Date()
    };

    console.log(`✅ Trial result calculated: ${correctAnswers}/${totalQuestions} (${percentage}%) - ${passed ? 'PASSED' : 'FAILED'}`);

    res.send({
      message: "Trial result calculated successfully",
      data: result,
      success: true,
    });

  } catch (error) {
    console.error("❌ Trial result error:", error);
    res.status(500).send({
      message: "Error calculating trial result",
      data: error.message,
      success: false,
    });
  }
});

// Get trial statistics (optional - for showing trial engagement)
router.get("/trial-stats", async (req, res) => {
  try {
    // Get some basic stats about available trial content
    const totalExams = await Exam.countDocuments({
      questions: { $exists: true, $not: { $size: 0 } }
    });

    const examsByLevel = await Exam.aggregate([
      {
        $match: {
          questions: { $exists: true, $not: { $size: 0 } }
        }
      },
      {
        $group: {
          _id: "$level",
          count: { $sum: 1 }
        }
      }
    ]);

    const stats = {
      totalAvailableExams: totalExams,
      examsByLevel: examsByLevel.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      trialFeatures: [
        "Sample quiz experience",
        "Real-time scoring",
        "Instant results",
        "Question explanations"
      ]
    };

    res.send({
      message: "Trial statistics retrieved successfully",
      data: stats,
      success: true,
    });

  } catch (error) {
    console.error("❌ Trial stats error:", error);
    res.status(500).send({
      message: "Error fetching trial statistics",
      data: error.message,
      success: false,
    });
  }
});

module.exports = router;
