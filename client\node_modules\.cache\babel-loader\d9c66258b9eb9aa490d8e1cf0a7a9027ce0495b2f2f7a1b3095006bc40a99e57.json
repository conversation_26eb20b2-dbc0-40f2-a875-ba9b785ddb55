{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbRocket, TbUserPlus, TbStar, TbLogin } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Handle Try for Free modal\n  const handleTryForFree = () => {\n    setShowTryForFreeModal(true);\n  };\n  const handleTryForFreeSubmit = trialData => {\n    // Navigate to trial experience with user data\n    navigate('/trial', {\n      state: {\n        trialUserInfo: trialData\n      }\n    });\n    setShowTryForFreeModal(false);\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home relative min-h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(reviewsSectionRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(contactUsRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), user && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          style: {\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            backgroundSize: '60px 60px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative mb-2 sm:mb-3 md:mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n                className: \"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\",\n                style: {\n                  fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                  letterSpacing: '-0.02em',\n                  lineHeight: '1.1'\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block mr-4\",\n                  initial: {\n                    opacity: 0,\n                    x: -50,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    textShadow: [\"0 0 20px rgba(59, 130, 246, 0.5)\", \"0 0 40px rgba(59, 130, 246, 0.8)\", \"0 0 20px rgba(59, 130, 246, 0.5)\"]\n                  },\n                  transition: {\n                    duration: 1.2,\n                    delay: 0.3,\n                    textShadow: {\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.1,\n                    rotate: [0, -3, 3, 0],\n                    transition: {\n                      duration: 0.4\n                    }\n                  },\n                  style: {\n                    color: '#1f2937',\n                    fontWeight: '900',\n                    textShadow: '0 0 20px rgba(59, 130, 246, 0.5)'\n                  },\n                  children: [\"Study\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -top-2 -right-2 w-3 h-3 rounded-full\",\n                    animate: {\n                      opacity: [0, 1, 0],\n                      scale: [0.5, 1.5, 0.5],\n                      backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                    },\n                    transition: {\n                      duration: 2,\n                      repeat: Infinity,\n                      delay: 1\n                    },\n                    style: {\n                      backgroundColor: '#3b82f6',\n                      boxShadow: '0 0 15px #3b82f6'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block\",\n                  initial: {\n                    opacity: 0,\n                    x: 50,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    y: [0, -3, 0, 3, 0],\n                    textShadow: [\"0 0 20px rgba(16, 185, 129, 0.5)\", \"0 0 40px rgba(16, 185, 129, 0.8)\", \"0 0 20px rgba(16, 185, 129, 0.5)\"]\n                  },\n                  transition: {\n                    duration: 1.2,\n                    delay: 0.6,\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    textShadow: {\n                      duration: 3.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.1,\n                    rotate: [0, 3, -3, 0],\n                    transition: {\n                      duration: 0.4\n                    }\n                  },\n                  style: {\n                    color: '#059669',\n                    fontWeight: '900',\n                    textShadow: '0 0 20px rgba(16, 185, 129, 0.5)'\n                  },\n                  children: [\"Smarter\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute top-0 left-0 w-2 h-2 rounded-full\",\n                    animate: {\n                      opacity: [0, 1, 0],\n                      x: [0, 100, 200, 300, 200, 100, 0],\n                      y: [0, -10, 0, 10, 20, 10, 0],\n                      backgroundColor: ['#10b981', '#34d399', '#10b981']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      delay: 2\n                    },\n                    style: {\n                      backgroundColor: '#10b981',\n                      boxShadow: '0 0 12px #10b981'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\",\n                initial: {\n                  width: 0,\n                  opacity: 0\n                },\n                animate: {\n                  width: '80%',\n                  opacity: 1,\n                  boxShadow: ['0 0 20px rgba(16, 185, 129, 0.5)', '0 0 40px rgba(59, 130, 246, 0.8)', '0 0 20px rgba(16, 185, 129, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  delay: 1.5,\n                  boxShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                style: {\n                  background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                  boxShadow: '0 0 25px rgba(16, 185, 129, 0.6)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute top-1/2 left-1/2 w-4 h-4 rounded-full\",\n                animate: {\n                  rotate: 360,\n                  x: [0, 100, 0, -100, 0],\n                  y: [0, -50, -100, -50, 0],\n                  opacity: [0.3, 1, 0.3]\n                },\n                transition: {\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                },\n                style: {\n                  background: 'linear-gradient(45deg, #3b82f6, #10b981)',\n                  boxShadow: '0 0 15px rgba(59, 130, 246, 0.6)',\n                  transform: 'translate(-50%, -50%)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.8\n              },\n              className: \"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\",\n              style: {\n                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\n                boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\n                border: '2px solid #FFD700'\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\",\n                animate: {\n                  x: ['-100%', '100%']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5 mr-2 text-orange-800 relative z-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-orange-900 relative z-10 font-black\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                animate: {\n                  boxShadow: ['0 0 20px rgba(255, 215, 0, 0.5)', '0 0 40px rgba(255, 215, 0, 0.8)', '0 0 20px rgba(255, 215, 0, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.9\n              },\n              className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\",\n              children: !user ? /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: handleTryForFree,\n                  className: \"group w-full xs:w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbRocket, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5 group-hover:animate-bounce\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 655,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm sm:text-base\",\n                      children: \"Try for Free\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/register\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUserPlus, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 669,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Register Now\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 670,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbLogin, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Login\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/user/hub\",\n                className: \"w-full sm:w-auto\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm sm:text-base\",\n                      children: \"Go to Hub\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"grid grid-cols-3 gap-2 sm:gap-3 md:gap-4 pt-2 sm:pt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-bold text-blue-600\",\n                  children: \"10K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-bold text-purple-600\",\n                  children: \"500+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-bold text-green-600\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative mt-0 lg:mt-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Image1,\n                  alt: \"Students Learning\",\n                  className: \"w-full h-auto rounded-xl sm:rounded-2xl\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                className: \"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-5, 5, -5]\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                className: \"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/80 backdrop-blur-sm rounded-2xl mx-4 sm:mx-6 md:mx-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\",\n            children: [{\n              number: \"15K+\",\n              text: \"Active Students\",\n              icon: TbUsers,\n              color: \"from-blue-500 to-blue-600\"\n            }, {\n              number: \"500+\",\n              text: \"Expert Teachers\",\n              icon: TbSchool,\n              color: \"from-green-500 to-green-600\"\n            }, {\n              number: \"1000+\",\n              text: \"Video Lessons\",\n              icon: TbBook,\n              color: \"from-purple-500 to-purple-600\"\n            }, {\n              number: \"98%\",\n              text: \"Success Rate\",\n              icon: TbTrophy,\n              color: \"from-orange-500 to-orange-600\"\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30,\n                scale: 0.9\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              whileHover: {\n                scale: 1.05,\n                y: -5\n              },\n              className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n                children: stat.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Students Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Hear from thousands of students who have transformed their learning journey with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\",\n          children: [{\n            name: \"Amina Hassan\",\n            class: \"Form 4\",\n            rating: 5,\n            text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\n            avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-pink-400 to-purple-500\"\n          }, {\n            name: \"John Mwalimu\",\n            class: \"Class 7\",\n            rating: 5,\n            text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\n            avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-blue-400 to-indigo-500\"\n          }, {\n            name: \"Fatuma Said\",\n            class: \"Form 2\",\n            rating: 5,\n            text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\n            avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-green-400 to-teal-500\"\n          }].map((review, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-gray-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [...Array(review.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    scale: 0,\n                    rotate: -180\n                  },\n                  whileInView: {\n                    scale: 1,\n                    rotate: 0\n                  },\n                  transition: {\n                    duration: 0.3,\n                    delay: i * 0.1\n                  },\n                  viewport: {\n                    once: true\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 text-yellow-400 drop-shadow-sm\",\n                    style: {\n                      fill: 'currentColor'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 25\n                  }, this)\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400 font-medium\",\n                children: \"Verified Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-6 leading-relaxed font-medium relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -top-2 -left-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"relative z-10\",\n                children: review.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -bottom-4 -right-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative mr-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`,\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: review.avatar,\n                    alt: review.name,\n                    className: \"w-full h-full rounded-full object-cover border-2 border-white\",\n                    onError: e => {\n                      // Fallback to initials if image fails to load\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\",\n                    style: {\n                      display: 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600 font-semibold text-sm\",\n                      children: review.name.split(' ').map(n => n[0]).join('')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900 text-lg\",\n                    children: review.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-blue-500\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 932,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: review.class\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Verified Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: [\"Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"+255 655 285 549\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 139\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4 sm:space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Your Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your full name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your email\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 989,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                rows: 5,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                placeholder: \"Tell us how we can help you...\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCAC Chat on WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                children: \"Quick Contact Options:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.a, {\n                  href: \"https://wa.me/255655285549?text=Hi!%20I%20want%20to%20register%20for%20BrainWave%20platform\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"flex items-center space-x-2 p-3 bg-white rounded-lg border border-green-200 hover:border-green-300 transition-colors duration-200\",\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-600\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1056,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: \"Registration Help\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1060,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Get help signing up\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                  href: \"https://wa.me/255655285549?text=Hello!%20I%20need%20technical%20support%20for%20BrainWave%20platform\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"flex items-center space-x-2 p-3 bg-white rounded-lg border border-blue-200 hover:border-blue-300 transition-colors duration-200\",\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-blue-600\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1074,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: \"Technical Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1078,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Get technical help\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1079,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1077,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n              children: responseMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1085,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 973,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 949,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TryForFreeModal, {\n      isOpen: showTryForFreeModal,\n      onClose: () => setShowTryForFreeModal(false),\n      onSubmit: handleTryForFreeSubmit\n    }, showTryForFreeModal ? 'open' : 'closed', false, {\n      fileName: _jsxFileName,\n      lineNumber: 1095,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0,\n        rotate: -180\n      },\n      animate: {\n        scale: 1,\n        rotate: 0\n      },\n      transition: {\n        duration: 0.5,\n        delay: 1\n      },\n      className: \"fixed bottom-6 right-6 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.a, {\n        href: \"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\",\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"group flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300\",\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-8 h-8 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-green-400 animate-ping opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\",\n          children: [\"Chat with us on WhatsApp\", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"hN9KP/cUnU+OKOjQBZIqmov4Jkw=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useNavigate", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbRocket", "TbUserPlus", "TbStar", "<PERSON>b<PERSON><PERSON><PERSON>", "message", "useSelector", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "TryForFreeModal", "jsxDEV", "_jsxDEV", "Home", "_s", "reviewsSectionRef", "contactUsRef", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "showTryForFreeModal", "setShowTryForFreeModal", "user", "state", "navigate", "handleTryForFree", "handleTryForFreeSubmit", "trialData", "trialUserInfo", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "isAdmin", "size", "showOnlineStatus", "class", "backgroundImage", "backgroundSize", "h1", "lineHeight", "transform", "border", "p", "Fragment", "button", "whileTap", "to", "whileInView", "viewport", "once", "number", "text", "icon", "map", "stat", "index", "rating", "avatar", "bgColor", "review", "fill", "viewBox", "d", "Array", "_", "i", "split", "n", "join", "onSubmit", "type", "onChange", "placeholder", "required", "rows", "disabled", "a", "href", "rel", "includes", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbRocket,\r\n  TbUserPlus,\r\n  TbStar,\r\n  TbLogin\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\r\n\r\nconst Home = () => {\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Handle Try for Free modal\r\n  const handleTryForFree = () => {\r\n    setShowTryForFreeModal(true);\r\n  };\r\n\r\n  const handleTryForFreeSubmit = (trialData) => {\r\n    // Navigate to trial experience with user data\r\n    navigate('/trial', { state: { trialUserInfo: trialData } });\r\n    setShowTryForFreeModal(false);\r\n  };\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home relative min-h-screen overflow-hidden\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Reviews */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item text-sm md:text-base\">Reviews</button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Notifications + User Profile */}\r\n            <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n              {/* Contact Us Button */}\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item text-sm md:text-base\">Contact Us</button>\r\n              </div>\r\n\r\n              {/* Notification Bell */}\r\n              {user && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile Section */}\r\n              {user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* PROFESSIONAL HERO SECTION */}\r\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-5\">\r\n          <div className=\"absolute inset-0\" style={{\r\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\r\n            backgroundSize: '60px 60px'\r\n          }}></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\">\r\n\r\n\r\n            {/* Left Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\"\r\n            >\r\n              {/* Animated Study Smarter Text */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative mb-2 sm:mb-3 md:mb-4\"\r\n              >\r\n                <motion.h1\r\n                  className=\"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\"\r\n                  style={{\r\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                    letterSpacing: '-0.02em',\r\n                    lineHeight: '1.1'\r\n                  }}\r\n                >\r\n                  {/* Study - with amazing effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block mr-4\"\r\n                    initial={{ opacity: 0, x: -50, scale: 0.8 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      textShadow: [\r\n                        \"0 0 20px rgba(59, 130, 246, 0.5)\",\r\n                        \"0 0 40px rgba(59, 130, 246, 0.8)\",\r\n                        \"0 0 20px rgba(59, 130, 246, 0.5)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.2,\r\n                      delay: 0.3,\r\n                      textShadow: {\r\n                        duration: 3,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.1,\r\n                      rotate: [0, -3, 3, 0],\r\n                      transition: { duration: 0.4 }\r\n                    }}\r\n                    style={{\r\n                      color: '#1f2937',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 20px rgba(59, 130, 246, 0.5)'\r\n                    }}\r\n                  >\r\n                    Study\r\n\r\n                    {/* Floating particles around Study */}\r\n                    <motion.div\r\n                      className=\"absolute -top-2 -right-2 w-3 h-3 rounded-full\"\r\n                      animate={{\r\n                        opacity: [0, 1, 0],\r\n                        scale: [0.5, 1.5, 0.5],\r\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                      }}\r\n                      transition={{\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        delay: 1\r\n                      }}\r\n                      style={{\r\n                        backgroundColor: '#3b82f6',\r\n                        boxShadow: '0 0 15px #3b82f6'\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n\r\n                  {/* Smarter - with flowing effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block\"\r\n                    initial={{ opacity: 0, x: 50, scale: 0.8 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      y: [0, -3, 0, 3, 0],\r\n                      textShadow: [\r\n                        \"0 0 20px rgba(16, 185, 129, 0.5)\",\r\n                        \"0 0 40px rgba(16, 185, 129, 0.8)\",\r\n                        \"0 0 20px rgba(16, 185, 129, 0.5)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.2,\r\n                      delay: 0.6,\r\n                      y: {\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      },\r\n                      textShadow: {\r\n                        duration: 3.5,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.1,\r\n                      rotate: [0, 3, -3, 0],\r\n                      transition: { duration: 0.4 }\r\n                    }}\r\n                    style={{\r\n                      color: '#059669',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 20px rgba(16, 185, 129, 0.5)'\r\n                    }}\r\n                  >\r\n                    Smarter\r\n\r\n                    {/* Animated line going around Smarter */}\r\n                    <motion.div\r\n                      className=\"absolute top-0 left-0 w-2 h-2 rounded-full\"\r\n                      animate={{\r\n                        opacity: [0, 1, 0],\r\n                        x: [0, 100, 200, 300, 200, 100, 0],\r\n                        y: [0, -10, 0, 10, 20, 10, 0],\r\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                      }}\r\n                      transition={{\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        delay: 2\r\n                      }}\r\n                      style={{\r\n                        backgroundColor: '#10b981',\r\n                        boxShadow: '0 0 12px #10b981'\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n                </motion.h1>\r\n\r\n                {/* Glowing underline effect */}\r\n                <motion.div\r\n                  className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\"\r\n                  initial={{ width: 0, opacity: 0 }}\r\n                  animate={{\r\n                    width: '80%',\r\n                    opacity: 1,\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)',\r\n                      '0 0 40px rgba(59, 130, 246, 0.8)',\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    delay: 1.5,\r\n                    boxShadow: {\r\n                      duration: 3,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }\r\n                  }}\r\n                  style={{\r\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                    boxShadow: '0 0 25px rgba(16, 185, 129, 0.6)'\r\n                  }}\r\n                />\r\n\r\n                {/* Orbiting elements around the text */}\r\n                <motion.div\r\n                  className=\"absolute top-1/2 left-1/2 w-4 h-4 rounded-full\"\r\n                  animate={{\r\n                    rotate: 360,\r\n                    x: [0, 100, 0, -100, 0],\r\n                    y: [0, -50, -100, -50, 0],\r\n                    opacity: [0.3, 1, 0.3]\r\n                  }}\r\n                  transition={{\r\n                    duration: 8,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\"\r\n                  }}\r\n                  style={{\r\n                    background: 'linear-gradient(45deg, #3b82f6, #10b981)',\r\n                    boxShadow: '0 0 15px rgba(59, 130, 246, 0.6)',\r\n                    transform: 'translate(-50%, -50%)'\r\n                  }}\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Highlighted Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.8 }}\r\n                className=\"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\"\r\n                style={{\r\n                  background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\r\n                  boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\r\n                  border: '2px solid #FFD700'\r\n                }}\r\n              >\r\n                {/* Animated background shimmer */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\"\r\n                  animate={{\r\n                    x: ['-100%', '100%']\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\"\r\n                  }}\r\n                />\r\n\r\n                <TbSchool className=\"w-5 h-5 mr-2 text-orange-800 relative z-10\" />\r\n                <span className=\"text-orange-900 relative z-10 font-black\">\r\n                  #1 Educational Platform in Tanzania\r\n                </span>\r\n\r\n                {/* Glowing border effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 rounded-full\"\r\n                  animate={{\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)',\r\n                      '0 0 40px rgba(255, 215, 0, 0.8)',\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Description */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </motion.p>\r\n\r\n              {/* Action Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.9 }}\r\n                className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                {!user ? (\r\n                  <React.Fragment>\r\n                    {/* Try for Free Button */}\r\n                    <motion.button\r\n                      onClick={handleTryForFree}\r\n                      className=\"group w-full xs:w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbRocket className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:animate-bounce\" />\r\n                        <span className=\"text-sm sm:text-base\">Try for Free</span>\r\n                      </div>\r\n                    </motion.button>\r\n\r\n                    {/* Register and Login Buttons - Super Responsive */}\r\n                    <div className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\">\r\n                      <Link to=\"/register\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbUserPlus className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Register Now</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n\r\n                      <Link to=\"/login\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbLogin className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Login</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n                    </div>\r\n                  </React.Fragment>\r\n                ) : (\r\n                  <Link to=\"/user/hub\" className=\"w-full sm:w-auto\">\r\n                    <motion.button\r\n                      className=\"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbArrowBigRightLinesFilled className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                        <span className=\"text-sm sm:text-base\">Go to Hub</span>\r\n                      </div>\r\n                    </motion.button>\r\n                  </Link>\r\n                )}\r\n              </motion.div>\r\n\r\n              {/* Stats */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"grid grid-cols-3 gap-2 sm:gap-3 md:gap-4 pt-2 sm:pt-3\"\r\n              >\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-xl sm:text-2xl md:text-3xl font-bold text-blue-600\">10K+</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Students</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-xl sm:text-2xl md:text-3xl font-bold text-purple-600\">500+</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Courses</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-xl sm:text-2xl md:text-3xl font-bold text-green-600\">95%</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Success Rate</div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Content - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative mt-0 lg:mt-0\"\r\n            >\r\n              <div className=\"relative\">\r\n                {/* Main Image */}\r\n                <div className=\"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\">\r\n                  <img\r\n                    src={Image1}\r\n                    alt=\"Students Learning\"\r\n                    className=\"w-full h-auto rounded-xl sm:rounded-2xl\"\r\n                    loading=\"lazy\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\r\n                  className=\"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbTrophy className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\r\n                  className=\"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbBook className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [-5, 5, -5] }}\r\n                  transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 }}\r\n                  className=\"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\"\r\n                >\r\n                  <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\" />\r\n                </motion.div>\r\n\r\n                {/* Background Decoration */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"></div>\r\n              </div>\r\n            </motion.div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Section - PART OF HERO SECTION */}\r\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl mx-4 sm:mx-6 md:mx-8\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n        </div>\r\n      </section>\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"bg-gray-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              What Our Students Say\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Hear from thousands of students who have transformed their learning journey with BrainWave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\">\r\n            {[\r\n              {\r\n                name: \"Amina Hassan\",\r\n                class: \"Form 4\",\r\n                rating: 5,\r\n                text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\r\n                avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-pink-400 to-purple-500\"\r\n              },\r\n              {\r\n                name: \"John Mwalimu\",\r\n                class: \"Class 7\",\r\n                rating: 5,\r\n                text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\r\n                avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-blue-400 to-indigo-500\"\r\n              },\r\n              {\r\n                name: \"Fatuma Said\",\r\n                class: \"Form 2\",\r\n                rating: 5,\r\n                text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\r\n                avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-green-400 to-teal-500\"\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\"\r\n              >\r\n                {/* Premium Background Gradient */}\r\n                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`}></div>\r\n\r\n                {/* Floating Quote Icon */}\r\n                <div className=\"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\">\r\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"/>\r\n                  </svg>\r\n                </div>\r\n                {/* Premium Star Rating */}\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    {[...Array(review.rating)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        initial={{ scale: 0, rotate: -180 }}\r\n                        whileInView={{ scale: 1, rotate: 0 }}\r\n                        transition={{ duration: 0.3, delay: i * 0.1 }}\r\n                        viewport={{ once: true }}\r\n                      >\r\n                        <TbStar className=\"w-5 h-5 text-yellow-400 drop-shadow-sm\" style={{ fill: 'currentColor' }} />\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400 font-medium\">Verified Review</div>\r\n                </div>\r\n                {/* Premium Review Text */}\r\n                <p className=\"text-gray-700 mb-6 leading-relaxed font-medium relative\">\r\n                  <span className=\"text-2xl text-blue-200 absolute -top-2 -left-1\">\"</span>\r\n                  <span className=\"relative z-10\">{review.text}</span>\r\n                  <span className=\"text-2xl text-blue-200 absolute -bottom-4 -right-1\">\"</span>\r\n                </p>\r\n                {/* Premium Profile Section */}\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"relative mr-4\">\r\n                    {/* Profile Picture with Premium Border */}\r\n                    <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`}>\r\n                      <img\r\n                        src={review.avatar}\r\n                        alt={review.name}\r\n                        className=\"w-full h-full rounded-full object-cover border-2 border-white\"\r\n                        onError={(e) => {\r\n                          // Fallback to initials if image fails to load\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                      {/* Fallback initials */}\r\n                      <div className=\"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\" style={{display: 'none'}}>\r\n                        <span className=\"text-gray-600 font-semibold text-sm\">\r\n                          {review.name.split(' ').map(n => n[0]).join('')}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Online Status Indicator */}\r\n                    <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"></div>\r\n                  </div>\r\n\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <h4 className=\"font-bold text-gray-900 text-lg\">{review.name}</h4>\r\n                      {/* Verified Badge */}\r\n                      <div className=\"flex items-center\">\r\n                        <svg className=\"w-4 h-4 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                        </svg>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-1\">\r\n                      <span className=\"text-sm font-medium text-gray-600\">{review.class}</span>\r\n                      <span className=\"text-xs text-gray-400\">•</span>\r\n                      <span className=\"text-xs text-gray-500\">Verified Student</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"bg-white\">\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Get in Touch\r\n            </h2>\r\n            <p className=\"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at <strong>+255 655 285 549</strong>.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\r\n              <div className=\"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Your Name\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your full name\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Email Address\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your email\"\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Message\r\n                </label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  rows={5}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"Tell us how we can help you...\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"flex flex-col sm:flex-row gap-4 items-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n\r\n                {/* Enhanced WhatsApp Button */}\r\n                <motion.a\r\n                  href=\"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n                  </svg>\r\n                  <span>💬 Chat on WhatsApp</span>\r\n                </motion.a>\r\n              </div>\r\n\r\n              {/* Alternative Contact Methods */}\r\n              <div className=\"mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200\">\r\n                <h4 className=\"text-sm font-semibold text-gray-700 mb-3\">Quick Contact Options:</h4>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n                  <motion.a\r\n                    href=\"https://wa.me/255655285549?text=Hi!%20I%20want%20to%20register%20for%20BrainWave%20platform\"\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"flex items-center space-x-2 p-3 bg-white rounded-lg border border-green-200 hover:border-green-300 transition-colors duration-200\"\r\n                    whileHover={{ scale: 1.02 }}\r\n                  >\r\n                    <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"text-sm font-medium text-gray-900\">Registration Help</div>\r\n                      <div className=\"text-xs text-gray-500\">Get help signing up</div>\r\n                    </div>\r\n                  </motion.a>\r\n\r\n                  <motion.a\r\n                    href=\"https://wa.me/255655285549?text=Hello!%20I%20need%20technical%20support%20for%20BrainWave%20platform\"\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"flex items-center space-x-2 p-3 bg-white rounded-lg border border-blue-200 hover:border-blue-300 transition-colors duration-200\"\r\n                    whileHover={{ scale: 1.02 }}\r\n                  >\r\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"text-sm font-medium text-gray-900\">Technical Support</div>\r\n                      <div className=\"text-xs text-gray-500\">Get technical help</div>\r\n                    </div>\r\n                  </motion.a>\r\n                </div>\r\n              </div>\r\n              {responseMessage && (\r\n                <div className={`p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>\r\n                  {responseMessage}\r\n                </div>\r\n              )}\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Try for Free Modal */}\r\n      <TryForFreeModal\r\n        key={showTryForFreeModal ? 'open' : 'closed'}\r\n        isOpen={showTryForFreeModal}\r\n        onClose={() => setShowTryForFreeModal(false)}\r\n        onSubmit={handleTryForFreeSubmit}\r\n      />\r\n\r\n      {/* Floating WhatsApp Button */}\r\n      <motion.div\r\n        initial={{ scale: 0, rotate: -180 }}\r\n        animate={{ scale: 1, rotate: 0 }}\r\n        transition={{ duration: 0.5, delay: 1 }}\r\n        className=\"fixed bottom-6 right-6 z-50\"\r\n      >\r\n        <motion.a\r\n          href=\"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"group flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300\"\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          {/* WhatsApp Icon */}\r\n          <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n          </svg>\r\n\r\n          {/* Pulse Animation */}\r\n          <div className=\"absolute inset-0 rounded-full bg-green-400 animate-ping opacity-20\"></div>\r\n\r\n          {/* Tooltip */}\r\n          <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n            Chat with us on WhatsApp\r\n            <div className=\"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"></div>\r\n          </div>\r\n        </motion.a>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,OAAO,QACF,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,iBAAiB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM0B,YAAY,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IAAE8B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEhB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEsC;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGrC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMK,sBAAsB,GAAIC,SAAS,IAAK;IAC5C;IACAH,QAAQ,CAAC,QAAQ,EAAE;MAAED,KAAK,EAAE;QAAEK,aAAa,EAAED;MAAU;IAAE,CAAC,CAAC;IAC3DN,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMQ,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC7B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAG2B;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAM0B,IAAI,GAAG,MAAM3C,SAAS,CAACU,QAAQ,CAAC;MACtC,IAAIiC,IAAI,CAACC,OAAO,EAAE;QAChB/C,OAAO,CAAC+C,OAAO,CAAC,4BAA4B,CAAC;QAC7C3B,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEhB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLoB,kBAAkB,CAAC0B,IAAI,CAAC9C,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd5B,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKyC,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzD1C,OAAA,CAACnB,MAAM,CAAC8D,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K1C,OAAA;QAAKyC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD1C,OAAA;UAAKyC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7E1C,OAAA;YAAKyC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C1C,OAAA;cAAKyC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE1C,OAAA;gBAAQgD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACnB,iBAAiB,CAAE;gBAACsC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpD,OAAA;YAAKyC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtD1C,OAAA;gBACEyC,SAAS,EAAC,wEAAwE;gBAClFiB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF1C,OAAA;kBACE6D,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAAC0B,GAAG,GAAG,8GAA8G;oBAC7H5B,CAAC,CAACE,MAAM,CAAC8B,OAAO,GAAG,MAAM;sBACvB;sBACAhC,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BjC,CAAC,CAACE,MAAM,CAACgC,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpD,OAAA;gBAAKyC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C1C,OAAA;kBAAIyC,SAAS,EAAC,qFAAqF;kBAC/FiB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA5B,QAAA,gBAEJ1C,OAAA,CAACnB,MAAM,CAAC0F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,OAGC,eACA1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;sBACTZ,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdpD,OAAA,CAACnB,MAAM,CAAC0F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVZ,CAAC,EAAE;wBACDW,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,MAGC,eACA1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;sBACTZ,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;kBACTZ,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEb,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPY,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,CAAC;oBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpD,OAAA;gBACEyC,SAAS,EAAC,gEAAgE;gBAC1EiB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,gBAEF1C,OAAA;kBACE6D,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BjC,CAAC,CAACE,MAAM,CAACiD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFpD,OAAA;kBACEyC,SAAS,EAAC,gHAAgH;kBAC1HiB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA3C,QAAA,EACH;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpD,OAAA;gBAAKyC,SAAS,EAAC;cAAyK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNpD,OAAA;YAAKyC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnE1C,OAAA;cAAKyC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE1C,OAAA;gBAAQgD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAClB,YAAY,CAAE;gBAACqC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,EAGLrC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuE,OAAO,kBACrBtF,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAf,QAAA,eAE1C1C,OAAA,CAACJ,gBAAgB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGArC,IAAI,iBACHf,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7C1C,OAAA,CAACH,cAAc;gBACbkB,IAAI,EAAEA,IAAK;gBACXwE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB9B,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFpD,OAAA;gBAAKyC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC1C,OAAA;kBAAKyC,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;gBAAM;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNpD,OAAA;kBAAKyC,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,GAAC,QACzF,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,KAAK;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhBpD,OAAA;MAASyC,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAEjF1C,OAAA;QAAKyC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzC1C,OAAA;UAAKyC,SAAS,EAAC,kBAAkB;UAACiB,KAAK,EAAE;YACvCgC,eAAe,EAAG,kQAAiQ;YACnRC,cAAc,EAAE;UAClB;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpD,OAAA;QAAKyC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7D1C,OAAA;UAAKyC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAI9F1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bf,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAGxG1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAEzC1C,OAAA,CAACnB,MAAM,CAAC+G,EAAE;gBACRnD,SAAS,EAAC,gJAAgJ;gBAC1JiB,KAAK,EAAE;kBACLW,UAAU,EAAE,yDAAyD;kBACrEC,aAAa,EAAE,SAAS;kBACxBuB,UAAU,EAAE;gBACd,CAAE;gBAAAnD,QAAA,gBAGF1C,OAAA,CAACnB,MAAM,CAAC0F,IAAI;kBACV9B,SAAS,EAAC,4BAA4B;kBACtCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE,CAAC,EAAE;oBAAElB,KAAK,EAAE;kBAAI,CAAE;kBAC5CP,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACV0B,CAAC,EAAE,CAAC;oBACJlB,KAAK,EAAE,CAAC;oBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVgB,UAAU,EAAE;sBACVjB,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFC,UAAU,EAAE;oBACVvB,KAAK,EAAE,GAAG;oBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACrBvB,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAC9B,CAAE;kBACFE,KAAK,EAAE;oBACLqB,KAAK,EAAE,SAAS;oBAChBC,UAAU,EAAE,KAAK;oBACjBP,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,GACH,OAGC,eACA1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;oBACTZ,SAAS,EAAC,+CAA+C;oBACzDM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBAClBQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;oBACnD,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE;oBACT,CAAE;oBACFC,KAAK,EAAE;sBACLuB,eAAe,EAAE,SAAS;sBAC1BC,SAAS,EAAE;oBACb;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAGdpD,OAAA,CAACnB,MAAM,CAAC0F,IAAI;kBACV9B,SAAS,EAAC,uBAAuB;kBACjCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE,EAAE;oBAAElB,KAAK,EAAE;kBAAI,CAAE;kBAC3CP,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACV0B,CAAC,EAAE,CAAC;oBACJlB,KAAK,EAAE,CAAC;oBACRT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVZ,CAAC,EAAE;sBACDW,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAC;oBACDH,UAAU,EAAE;sBACVjB,QAAQ,EAAE,GAAG;sBACbkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFC,UAAU,EAAE;oBACVvB,KAAK,EAAE,GAAG;oBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrBvB,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAC9B,CAAE;kBACFE,KAAK,EAAE;oBACLqB,KAAK,EAAE,SAAS;oBAChBC,UAAU,EAAE,KAAK;oBACjBP,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,GACH,SAGC,eACA1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;oBACTZ,SAAS,EAAC,4CAA4C;oBACtDM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;sBAClC3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;sBAC7BoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;oBACnD,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE;oBACT,CAAE;oBACFC,KAAK,EAAE;sBACLuB,eAAe,EAAE,SAAS;sBAC1BC,SAAS,EAAE;oBACb;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGZpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTZ,SAAS,EAAC,yEAAyE;gBACnFG,OAAO,EAAE;kBAAEe,KAAK,EAAE,CAAC;kBAAEb,OAAO,EAAE;gBAAE,CAAE;gBAClCC,OAAO,EAAE;kBACPY,KAAK,EAAE,KAAK;kBACZb,OAAO,EAAE,CAAC;kBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACF3B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,KAAK,EAAE,GAAG;kBACVyB,SAAS,EAAE;oBACT1B,QAAQ,EAAE,CAAC;oBACXkB,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR;gBACF,CAAE;gBACFlB,KAAK,EAAE;kBACLyB,UAAU,EAAE,mDAAmD;kBAC/DD,SAAS,EAAE;gBACb;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTZ,SAAS,EAAC,gDAAgD;gBAC1DM,OAAO,EAAE;kBACP+B,MAAM,EAAE,GAAG;kBACXN,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;kBACvB3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACzBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFS,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFlB,KAAK,EAAE;kBACLyB,UAAU,EAAE,0CAA0C;kBACtDD,SAAS,EAAE,kCAAkC;kBAC7CY,SAAS,EAAE;gBACb;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,iGAAiG;cAC3GiB,KAAK,EAAE;gBACLyB,UAAU,EAAE,gEAAgE;gBAC5ED,SAAS,EAAE,oEAAoE;gBAC/Ea,MAAM,EAAE;cACV,CAAE;cAAArD,QAAA,gBAGF1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTZ,SAAS,EAAC,gFAAgF;gBAC1FM,OAAO,EAAE;kBACPyB,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;gBACrB,CAAE;gBACFjB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEFpD,OAAA,CAACb,QAAQ;gBAACsD,SAAS,EAAC;cAA4C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnEpD,OAAA;gBAAMyC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE3D;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGPpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTZ,SAAS,EAAC,+BAA+B;gBACzCM,OAAO,EAAE;kBACPmC,SAAS,EAAE,CACT,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC;gBAErC,CAAE;gBACF3B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGbpD,OAAA,CAACnB,MAAM,CAACmH,CAAC;cACPpD,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAID;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,wIAAwI;cAAAC,QAAA,EAEjJ,CAAC3B,IAAI,gBACJf,OAAA,CAACxB,KAAK,CAACyH,QAAQ;gBAAAvD,QAAA,gBAEb1C,OAAA,CAACnB,MAAM,CAACqH,MAAM;kBACZlD,OAAO,EAAE9B,gBAAiB;kBAC1BuB,SAAS,EAAC,yPAAyP;kBACnQoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B6C,QAAQ,EAAE;oBAAE7C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,eAE1B1C,OAAA;oBAAKyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD1C,OAAA,CAACZ,QAAQ;sBAACqD,SAAS,EAAC;oBAAkD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzEpD,OAAA;sBAAMyC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAGhBpD,OAAA;kBAAKyC,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBACpF1C,OAAA,CAACrB,IAAI;oBAACyH,EAAE,EAAC,WAAW;oBAAC3D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/C1C,OAAA,CAACnB,MAAM,CAACqH,MAAM;sBACZzD,SAAS,EAAC,yOAAyO;sBACnPoC,UAAU,EAAE;wBAAEvB,KAAK,EAAE;sBAAK,CAAE;sBAC5B6C,QAAQ,EAAE;wBAAE7C,KAAK,EAAE;sBAAK,CAAE;sBAAAZ,QAAA,eAE1B1C,OAAA;wBAAKyC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzD1C,OAAA,CAACX,UAAU;0BAACoD,SAAS,EAAC;wBAAqC;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9DpD,OAAA;0BAAMyC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eAEPpD,OAAA,CAACrB,IAAI;oBAACyH,EAAE,EAAC,QAAQ;oBAAC3D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC5C1C,OAAA,CAACnB,MAAM,CAACqH,MAAM;sBACZzD,SAAS,EAAC,yOAAyO;sBACnPoC,UAAU,EAAE;wBAAEvB,KAAK,EAAE;sBAAK,CAAE;sBAC5B6C,QAAQ,EAAE;wBAAE7C,KAAK,EAAE;sBAAK,CAAE;sBAAAZ,QAAA,eAE1B1C,OAAA;wBAAKyC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzD1C,OAAA,CAACT,OAAO;0BAACkD,SAAS,EAAC;wBAAqC;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3DpD,OAAA;0BAAMyC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAK;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,gBAEjBpD,OAAA,CAACrB,IAAI;gBAACyH,EAAE,EAAC,WAAW;gBAAC3D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/C1C,OAAA,CAACnB,MAAM,CAACqH,MAAM;kBACZzD,SAAS,EAAC,iMAAiM;kBAC3MoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B6C,QAAQ,EAAE;oBAAE7C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,eAE1B1C,OAAA;oBAAKyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD1C,OAAA,CAAClB,0BAA0B;sBAAC2D,SAAS,EAAC;oBAAuB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChEpD,OAAA;sBAAMyC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBAEjE1C,OAAA;gBAAKyC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1C,OAAA;kBAAKyC,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnFpD,OAAA;kBAAKyC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNpD,OAAA;gBAAKyC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1C,OAAA;kBAAKyC,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrFpD,OAAA;kBAAKyC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNpD,OAAA;gBAAKyC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1C,OAAA;kBAAKyC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,EAAC;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnFpD,OAAA;kBAAKyC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAEjC1C,OAAA;cAAKyC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAEvB1C,OAAA;gBAAKyC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7F1C,OAAA;kBACE6D,GAAG,EAAEnE,MAAO;kBACZoE,GAAG,EAAC,mBAAmB;kBACvBrB,SAAS,EAAC,yCAAyC;kBACnDhC,OAAO,EAAC;gBAAM;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE;gBAAY,CAAE;gBACjEnC,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5I1C,OAAA,CAACf,QAAQ;kBAACwD,SAAS,EAAC;gBAAqC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAEbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAE,CAAE;gBAC3EhB,SAAS,EAAC,uIAAuI;gBAAAC,QAAA,eAEjJ1C,OAAA,CAAChB,MAAM;kBAACyD,SAAS,EAAC;gBAAqC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAEbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAE;gBAC5BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAI,CAAE;gBAC7EhB,SAAS,EAAC,4HAA4H;gBAAAC,QAAA,eAEtI1C,OAAA,CAACjB,OAAO;kBAAC0D,SAAS,EAAC;gBAAqC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAGbpD,OAAA;gBAAKyC,SAAS,EAAC;cAAoI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpD,OAAA;QAAKyC,SAAS,EAAC,+DAA+D;QAAAC,QAAA,eAC9E1C,OAAA;UAAKyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwD,WAAW,EAAE;cAAEvD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B8C,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9D,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAEzD,CACC;cAAE8D,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAExH,OAAO;cAAE6F,KAAK,EAAE;YAA4B,CAAC,EAC9F;cAAEyB,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAEvH,QAAQ;cAAE4F,KAAK,EAAE;YAA8B,CAAC,EACjG;cAAEyB,MAAM,EAAE,OAAO;cAAEC,IAAI,EAAE,eAAe;cAAEC,IAAI,EAAE1H,MAAM;cAAE+F,KAAK,EAAE;YAAgC,CAAC,EAChG;cAAEyB,MAAM,EAAE,KAAK;cAAEC,IAAI,EAAE,cAAc;cAAEC,IAAI,EAAEzH,QAAQ;cAAE8F,KAAK,EAAE;YAAgC,CAAC,CAChG,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB7G,OAAA,CAACnB,MAAM,CAACwE,GAAG;cAETT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3C+C,WAAW,EAAE;gBAAEvD,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAE,CAAE;cAC5CC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEoD,KAAK,GAAG;cAAI,CAAE;cAClDP,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB1B,UAAU,EAAE;gBAAEvB,KAAK,EAAE,IAAI;gBAAET,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCJ,SAAS,EAAC,gIAAgI;cAAAC,QAAA,gBAE1I1C,OAAA;gBAAKyC,SAAS,EAAG,gFAA+EmE,IAAI,CAAC7B,KAAM,2FAA2F;gBAAArC,QAAA,eACpM1C,OAAA,CAAC4G,IAAI,CAACF,IAAI;kBAACjE,SAAS,EAAC;gBAAkC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNpD,OAAA;gBAAKyC,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EAAEkE,IAAI,CAACJ;cAAM;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1GpD,OAAA;gBAAKyC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAEkE,IAAI,CAACH;cAAI;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAZvFyD,KAAK;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVpD,OAAA;MAASuB,GAAG,EAAEpB,iBAAkB;MAACsC,SAAS,EAAC,YAAY;MAAAC,QAAA,eACrD1C,OAAA;QAAKyC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwD,WAAW,EAAE;YAAEvD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B8C,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7C1C,OAAA;YAAIyC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpD,OAAA;YAAGyC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbpD,OAAA;UAAKyC,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EACvF,CACC;YACEnC,IAAI,EAAE,cAAc;YACpBkF,KAAK,EAAE,QAAQ;YACfqB,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,+GAA+G;YACrHM,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACEzG,IAAI,EAAE,cAAc;YACpBkF,KAAK,EAAE,SAAS;YAChBqB,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,gGAAgG;YACtGM,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACEzG,IAAI,EAAE,aAAa;YACnBkF,KAAK,EAAE,QAAQ;YACfqB,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,2GAA2G;YACjHM,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,CACF,CAACL,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAClB7G,OAAA,CAACnB,MAAM,CAACwE,GAAG;YAETT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwD,WAAW,EAAE;cAAEvD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEoD,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9D,SAAS,EAAC,6JAA6J;YAAAC,QAAA,gBAGvK1C,OAAA;cAAKyC,SAAS,EAAG,qDAAoDwE,MAAM,CAACD,OAAQ;YAAE;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG7FpD,OAAA;cAAKyC,SAAS,EAAC,0FAA0F;cAAAC,QAAA,eACvG1C,OAAA;gBAAKyC,SAAS,EAAC,uBAAuB;gBAACyE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAzE,QAAA,eAC5E1C,OAAA;kBAAMoH,CAAC,EAAC;gBAAmN;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1N;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAKyC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD1C,OAAA;gBAAKyC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC,CAAC,GAAG2E,KAAK,CAACJ,MAAM,CAACH,MAAM,CAAC,CAAC,CAACH,GAAG,CAAC,CAACW,CAAC,EAAEC,CAAC,kBAClCvH,OAAA,CAACnB,MAAM,CAACwE,GAAG;kBAETT,OAAO,EAAE;oBAAEU,KAAK,EAAE,CAAC;oBAAEwB,MAAM,EAAE,CAAC;kBAAI,CAAE;kBACpCuB,WAAW,EAAE;oBAAE/C,KAAK,EAAE,CAAC;oBAAEwB,MAAM,EAAE;kBAAE,CAAE;kBACrCvB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,KAAK,EAAE8D,CAAC,GAAG;kBAAI,CAAE;kBAC9CjB,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAK,CAAE;kBAAA7D,QAAA,eAEzB1C,OAAA,CAACV,MAAM;oBAACmD,SAAS,EAAC,wCAAwC;oBAACiB,KAAK,EAAE;sBAAEwD,IAAI,EAAE;oBAAe;kBAAE;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GANzFmE,CAAC;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOI,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpD,OAAA;gBAAKyC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAENpD,OAAA;cAAGyC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACpE1C,OAAA;gBAAMyC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEpD,OAAA;gBAAMyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEuE,MAAM,CAACR;cAAI;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDpD,OAAA;gBAAMyC,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eAEJpD,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAE5B1C,OAAA;kBAAKyC,SAAS,EAAG,2CAA0CwE,MAAM,CAACD,OAAQ,QAAQ;kBAAAtE,QAAA,gBAChF1C,OAAA;oBACE6D,GAAG,EAAEoD,MAAM,CAACF,MAAO;oBACnBjD,GAAG,EAAEmD,MAAM,CAAC1G,IAAK;oBACjBkC,SAAS,EAAC,+DAA+D;oBACzEuB,OAAO,EAAG/B,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BjC,CAAC,CAACE,MAAM,CAACiD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEFpD,OAAA;oBAAKyC,SAAS,EAAC,4FAA4F;oBAACiB,KAAK,EAAE;sBAACQ,OAAO,EAAE;oBAAM,CAAE;oBAAAxB,QAAA,eACnI1C,OAAA;sBAAMyC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAClDuE,MAAM,CAAC1G,IAAI,CAACiH,KAAK,CAAC,GAAG,CAAC,CAACb,GAAG,CAACc,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;oBAAC;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpD,OAAA;kBAAKyC,SAAS,EAAC;gBAAqF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eAENpD,OAAA;gBAAKyC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB1C,OAAA;kBAAKyC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1C,OAAA;oBAAIyC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEuE,MAAM,CAAC1G;kBAAI;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAElEpD,OAAA;oBAAKyC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,eAChC1C,OAAA;sBAAKyC,SAAS,EAAC,uBAAuB;sBAACyE,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAzE,QAAA,eAC5E1C,OAAA;wBAAMoH,CAAC,EAAC;sBAA8F;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpD,OAAA;kBAAKyC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C1C,OAAA;oBAAMyC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEuE,MAAM,CAACxB;kBAAK;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzEpD,OAAA;oBAAMyC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDpD,OAAA;oBAAMyC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAlFDyD,KAAK;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmFA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVpD,OAAA;MAASuB,GAAG,EAAEnB,YAAa;MAACqC,SAAS,EAAC,UAAU;MAAAC,QAAA,eAC9C1C,OAAA;QAAKyC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1C,OAAA,CAACnB,MAAM,CAACwE,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwD,WAAW,EAAE;YAAEvD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B8C,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7C1C,OAAA;YAAIyC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE9E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpD,OAAA;YAAGyC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,GAAC,8HAC+C,eAAA1C,OAAA;cAAA0C,QAAA,EAAQ;YAAgB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAC/J;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwD,WAAW,EAAE;YAAEvD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1C6C,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9D,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eAElE1C,OAAA;YAAM2H,QAAQ,EAAEvF,YAAa;YAACK,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAC9D1C,OAAA;cAAKyC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChE1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAOyC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4H,IAAI,EAAC,MAAM;kBACXrH,IAAI,EAAC,MAAM;kBACX2B,KAAK,EAAE7B,QAAQ,CAACE,IAAK;kBACrBsH,QAAQ,EAAE7F,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxHqF,WAAW,EAAC,sBAAsB;kBAClCC,QAAQ;gBAAA;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAOyC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4H,IAAI,EAAC,OAAO;kBACZrH,IAAI,EAAC,OAAO;kBACZ2B,KAAK,EAAE7B,QAAQ,CAACG,KAAM;kBACtBqH,QAAQ,EAAE7F,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxHqF,WAAW,EAAC,kBAAkB;kBAC9BC,QAAQ;gBAAA;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpD,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAOyC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACd2B,KAAK,EAAE7B,QAAQ,CAACb,OAAQ;gBACxBqI,QAAQ,EAAE7F,YAAa;gBACvBgG,IAAI,EAAE,CAAE;gBACRvF,SAAS,EAAC,8GAA8G;gBACxHqF,WAAW,EAAC,gCAAgC;gBAC5CC,QAAQ;cAAA;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpD,OAAA;cAAKyC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC3D1C,OAAA;gBACE4H,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAExH,OAAQ;gBAClBgC,SAAS,EAAC,iMAAiM;gBAAAC,QAAA,EAE1MjC,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAGTpD,OAAA,CAACnB,MAAM,CAACqJ,CAAC;gBACPC,IAAI,EAAC,+IAA+I;gBACpJhG,MAAM,EAAC,QAAQ;gBACfiG,GAAG,EAAC,qBAAqB;gBACzB3F,SAAS,EAAC,sPAAsP;gBAChQoC,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5B6C,QAAQ,EAAE;kBAAE7C,KAAK,EAAE;gBAAK,CAAE;gBAAAZ,QAAA,gBAE1B1C,OAAA;kBAAKyC,SAAS,EAAC,SAAS;kBAACyE,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAzE,QAAA,eAC9D1C,OAAA;oBAAMoH,CAAC,EAAC;kBAAklC;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACNpD,OAAA;kBAAA0C,QAAA,EAAM;gBAAmB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGNpD,OAAA;cAAKyC,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACpE1C,OAAA;gBAAIyC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFpD,OAAA;gBAAKyC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD1C,OAAA,CAACnB,MAAM,CAACqJ,CAAC;kBACPC,IAAI,EAAC,6FAA6F;kBAClGhG,MAAM,EAAC,QAAQ;kBACfiG,GAAG,EAAC,qBAAqB;kBACzB3F,SAAS,EAAC,mIAAmI;kBAC7IoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,gBAE5B1C,OAAA;oBAAKyC,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF1C,OAAA;sBAAKyC,SAAS,EAAC,wBAAwB;sBAACyE,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAzE,QAAA,eAC7E1C,OAAA;wBAAMoH,CAAC,EAAC;sBAAklC;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzlC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNpD,OAAA;oBAAA0C,QAAA,gBACE1C,OAAA;sBAAKyC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1EpD,OAAA;sBAAKyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEXpD,OAAA,CAACnB,MAAM,CAACqJ,CAAC;kBACPC,IAAI,EAAC,sGAAsG;kBAC3GhG,MAAM,EAAC,QAAQ;kBACfiG,GAAG,EAAC,qBAAqB;kBACzB3F,SAAS,EAAC,iIAAiI;kBAC3IoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,gBAE5B1C,OAAA;oBAAKyC,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF1C,OAAA;sBAAKyC,SAAS,EAAC,uBAAuB;sBAACyE,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAzE,QAAA,eAC5E1C,OAAA;wBAAMoH,CAAC,EAAC;sBAAuH;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9H;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNpD,OAAA;oBAAA0C,QAAA,gBACE1C,OAAA;sBAAKyC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1EpD,OAAA;sBAAKyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLzC,eAAe,iBACdX,OAAA;cAAKyC,SAAS,EAAG,kBAAiB9B,eAAe,CAAC0H,QAAQ,CAAC,SAAS,CAAC,GAAG,6BAA6B,GAAG,yBAA0B,EAAE;cAAA3F,QAAA,EACjI/B;YAAe;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpD,OAAA,CAACF,eAAe;MAEdwI,MAAM,EAAEzH,mBAAoB;MAC5B0H,OAAO,EAAEA,CAAA,KAAMzH,sBAAsB,CAAC,KAAK,CAAE;MAC7C6G,QAAQ,EAAExG;IAAuB,GAH5BN,mBAAmB,GAAG,MAAM,GAAG,QAAQ;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAI7C,CAAC,eAGFpD,OAAA,CAACnB,MAAM,CAACwE,GAAG;MACTT,OAAO,EAAE;QAAEU,KAAK,EAAE,CAAC;QAAEwB,MAAM,EAAE,CAAC;MAAI,CAAE;MACpC/B,OAAO,EAAE;QAAEO,KAAK,EAAE,CAAC;QAAEwB,MAAM,EAAE;MAAE,CAAE;MACjCvB,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACxChB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAEvC1C,OAAA,CAACnB,MAAM,CAACqJ,CAAC;QACPC,IAAI,EAAC,uHAAuH;QAC5HhG,MAAM,EAAC,QAAQ;QACfiG,GAAG,EAAC,qBAAqB;QACzB3F,SAAS,EAAC,sJAAsJ;QAChKoC,UAAU,EAAE;UAAEvB,KAAK,EAAE;QAAI,CAAE;QAC3B6C,QAAQ,EAAE;UAAE7C,KAAK,EAAE;QAAK,CAAE;QAAAZ,QAAA,gBAG1B1C,OAAA;UAAKyC,SAAS,EAAC,oBAAoB;UAACyE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAzE,QAAA,eACzE1C,OAAA;YAAMoH,CAAC,EAAC;UAAklC;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzlC,CAAC,eAGNpD,OAAA;UAAKyC,SAAS,EAAC;QAAoE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG1FpD,OAAA;UAAKyC,SAAS,EAAC,8LAA8L;UAAAC,QAAA,GAAC,0BAE5M,eAAA1C,OAAA;YAAKyC,SAAS,EAAC;UAAoF;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAClD,EAAA,CArlCID,IAAI;EAAA,QAOSR,WAAW,EACXb,WAAW;AAAA;AAAA4J,EAAA,GARxBvI,IAAI;AAulCV,eAAeA,IAAI;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}