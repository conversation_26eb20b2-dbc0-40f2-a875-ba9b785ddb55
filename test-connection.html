<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #007BFF;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
        button {
            background: #007BFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 BrainWave Services Connection Test</h1>
        
        <div class="test-section">
            <h3>Frontend (React Client)</h3>
            <p>Expected URL: <strong>http://localhost:3000</strong></p>
            <span id="frontend-status" class="status loading">Testing...</span>
            <button onclick="testFrontend()">Test Frontend</button>
        </div>

        <div class="test-section">
            <h3>Backend (Node.js Server)</h3>
            <p>Expected URL: <strong>http://localhost:5000</strong></p>
            <span id="backend-status" class="status loading">Testing...</span>
            <button onclick="testBackend()">Test Backend</button>
        </div>

        <div class="test-section">
            <h3>Database Connection</h3>
            <p>MongoDB Atlas connection through backend</p>
            <span id="database-status" class="status loading">Testing...</span>
            <button onclick="testDatabase()">Test Database</button>
        </div>

        <div class="test-section">
            <button onclick="testAll()">🚀 Test All Services</button>
            <button onclick="openFrontend()">📱 Open Frontend</button>
            <button onclick="openBackend()">🔧 Open Backend</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function addResult(message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.innerHTML = `<p><strong>${new Date().toLocaleTimeString()}</strong>: ${message}</p>`;
            results.appendChild(div);
        }

        async function testFrontend() {
            updateStatus('frontend-status', 'loading', 'Testing...');
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    updateStatus('frontend-status', 'success', '✅ Frontend Running');
                    addResult('✅ Frontend (React) is running on port 3000');
                } else {
                    updateStatus('frontend-status', 'error', '❌ Frontend Error');
                    addResult('❌ Frontend responded with error: ' + response.status);
                }
            } catch (error) {
                updateStatus('frontend-status', 'error', '❌ Frontend Offline');
                addResult('❌ Frontend connection failed: ' + error.message);
            }
        }

        async function testBackend() {
            updateStatus('backend-status', 'loading', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000');
                if (response.ok) {
                    const text = await response.text();
                    updateStatus('backend-status', 'success', '✅ Backend Running');
                    addResult('✅ Backend (Node.js) is running on port 5000. Response: ' + text);
                } else {
                    updateStatus('backend-status', 'error', '❌ Backend Error');
                    addResult('❌ Backend responded with error: ' + response.status);
                }
            } catch (error) {
                updateStatus('backend-status', 'error', '❌ Backend Offline');
                addResult('❌ Backend connection failed: ' + error.message);
            }
        }

        async function testDatabase() {
            updateStatus('database-status', 'loading', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/api/users/test-db');
                if (response.ok) {
                    updateStatus('database-status', 'success', '✅ Database Connected');
                    addResult('✅ Database (MongoDB) connection successful');
                } else {
                    updateStatus('database-status', 'error', '❌ Database Error');
                    addResult('❌ Database test failed: ' + response.status);
                }
            } catch (error) {
                updateStatus('database-status', 'error', '❌ Database Offline');
                addResult('❌ Database connection test failed: ' + error.message);
            }
        }

        async function testAll() {
            addResult('🚀 Starting comprehensive service test...');
            await testFrontend();
            await testBackend();
            await testDatabase();
            addResult('✅ All tests completed!');
        }

        function openFrontend() {
            window.open('http://localhost:3000', '_blank');
        }

        function openBackend() {
            window.open('http://localhost:5000', '_blank');
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(testAll, 1000);
        };
    </script>
</body>
</html>
