{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport \"./TrialQuiz.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizPlay = ({\n  quizData,\n  onComplete,\n  onBack\n}) => {\n  _s();\n  const {\n    exam,\n    trialUserInfo\n  } = quizData;\n  const questions = exam.questions || [];\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    playSuccessSound();\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n    playSubmitSound(); // Play submit sound\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Enhanced Sound effects for navigation\n  const playNavigationSound = () => {\n    try {\n      // Create a simple click sound using Web Audio API\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);\n      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.1);\n    } catch (error) {\n      console.log('Navigation sound not available');\n    }\n  };\n  const playSuccessSound = () => {\n    try {\n      // Create a pleasant success sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5\n      oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G5\n\n      gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n    } catch (error) {\n      console.log('Success sound not available');\n    }\n  };\n  const playSubmitSound = () => {\n    try {\n      // Create a completion sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4\n      oscillator.frequency.setValueAtTime(554, audioContext.currentTime + 0.15); // C#5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.3); // E5\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime + 0.45); // A5\n\n      gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.6);\n    } catch (error) {\n      console.log('Submit sound not available');\n    }\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n  const goToQuestion = index => {\n    playNavigationSound();\n    setCurrentQuestionIndex(index);\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"No Questions Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz doesn't have any questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.type) || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.answerType) || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n  const isDiagram = questionType === 'diagram' || questionType === 'Diagram' || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.image);\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) === 'object' && (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionA, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionB, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionC, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionD].filter(Boolean);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"trial-background\",\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)',\n      width: '100%',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"trial-header\",\n      style: {\n        background: 'white',\n        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n        borderBottom: '2px solid #e0e7ff',\n        width: '100%',\n        position: 'sticky',\n        top: 0,\n        zIndex: 100\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"trial-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: 'clamp(12px, 3vw, 20px)',\n            flexWrap: 'wrap',\n            gap: 'clamp(8px, 2vw, 16px)',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              minWidth: 0,\n              flex: 1,\n              maxWidth: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onBack,\n              style: {\n                padding: 'clamp(8px, 2vw, 12px)',\n                background: 'rgba(59, 130, 246, 0.1)',\n                border: 'none',\n                borderRadius: 'clamp(8px, 2vw, 12px)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                minWidth: '40px',\n                minHeight: '40px'\n              },\n              onMouseEnter: e => e.target.style.background = 'rgba(59, 130, 246, 0.2)',\n              onMouseLeave: e => e.target.style.background = 'rgba(59, 130, 246, 0.1)',\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                style: {\n                  width: 'clamp(18px, 4vw, 24px)',\n                  height: 'clamp(18px, 4vw, 24px)',\n                  color: '#2563eb'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                minWidth: 0,\n                flex: 1,\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  fontSize: 'clamp(16px, 4vw, 24px)',\n                  fontWeight: 'bold',\n                  background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  margin: 0,\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  lineHeight: 1.2\n                },\n                children: exam.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base text-gray-600\",\n                  children: exam.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base font-medium text-blue-600\",\n                  children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              flexShrink: 0,\n              flexWrap: 'wrap',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `trial-timer ${timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'}`,\n                children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                  className: \"trial-timer-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: formatTime(timeLeft)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), timeLeft <= 300 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timer-warning-ring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trial-progress-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trial-progress-label\",\n              children: [\"Progress: \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trial-progress-percentage\",\n              children: [Math.round((currentQuestionIndex + 1) / questions.length * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trial-progress-fill\",\n              style: {\n                width: `${(currentQuestionIndex + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:hidden mt-3 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"trial-container\",\n      style: {\n        paddingTop: 'clamp(16px, 4vw, 32px)',\n        paddingBottom: 'clamp(16px, 4vw, 32px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        transition: {\n          duration: 0.4,\n          ease: \"easeOut\"\n        },\n        className: \"trial-question-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trial-question-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-question-number\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trial-question-number-badge\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: currentQuestionIndex + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'rgba(255, 255, 255, 0.9)',\n                  fontSize: '14px'\n                },\n                children: \"Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'white',\n                  fontWeight: '500',\n                  fontSize: '18px'\n                },\n                children: [currentQuestionIndex + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"trial-question-title\",\n            children: currentQuestion.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trial-content\",\n          style: {\n            padding: 'clamp(16px, 4vw, 40px)',\n            width: '100%',\n            boxSizing: 'border-box'\n          },\n          children: [currentQuestion.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '32px',\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQuestion.image,\n              alt: \"Question\",\n              style: {\n                maxWidth: '100%',\n                maxHeight: '400px',\n                borderRadius: '16px',\n                boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-10 sm:mb-12\",\n            children: isMCQ && questionOptions.length > 0 ?\n            /*#__PURE__*/\n            // Multiple Choice Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4 sm:space-y-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800 mb-6\",\n                children: \"Choose your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), questionOptions.map((option, index) => {\n                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(currentQuestion._id, optionLetter),\n                  className: `trial-option ${isSelected ? 'selected' : ''}`,\n                  whileHover: {\n                    scale: isSelected ? 1.01 : 1.005\n                  },\n                  whileTap: {\n                    scale: 0.995\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"trial-option-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"trial-option-letter\",\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        style: {\n                          width: '24px',\n                          height: '24px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 43\n                      }, this) : optionLetter\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"trial-option-text\",\n                      children: option\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this) : isFillBlank ?\n            /*#__PURE__*/\n            // Fill-in-the-blank / Free Text Questions\n            _jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '20px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '24px'\n                },\n                children: \"\\uD83D\\uDCAD Type your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: selectedAnswers[currentQuestion._id] || '',\n                  onChange: e => handleAnswerSelect(currentQuestion._id, e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"trial-input\",\n                  style: {\n                    paddingRight: '60px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    right: '20px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    fontSize: '24px',\n                    color: '#9ca3af'\n                  },\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '12px',\n                  fontSize: '14px',\n                  color: '#6b7280',\n                  fontStyle: 'italic'\n                },\n                children: \"\\uD83D\\uDCA1 Tip: Be specific and clear in your answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this) : isDiagram ?\n            /*#__PURE__*/\n            // Diagram Questions\n            _jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '20px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '24px'\n                },\n                children: \"\\uD83D\\uDD0D Study the diagram and answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: selectedAnswers[currentQuestion._id] || '',\n                  onChange: e => handleAnswerSelect(currentQuestion._id, e.target.value),\n                  placeholder: \"Analyze the diagram and type your answer...\",\n                  className: \"trial-input\",\n                  style: {\n                    paddingRight: '60px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    right: '20px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    fontSize: '24px',\n                    color: '#9ca3af'\n                  },\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '12px',\n                  fontSize: '14px',\n                  color: '#6b7280',\n                  fontStyle: 'italic'\n                },\n                children: \"\\uD83C\\uDFAF Tip: Look carefully at all parts of the diagram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Default fallback\n            _jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '20px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '24px'\n                },\n                children: \"\\uD83D\\uDCDD Provide your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: selectedAnswers[currentQuestion._id] || '',\n                  onChange: e => handleAnswerSelect(currentQuestion._id, e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"trial-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trial-nav-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToPrevious,\n              disabled: currentQuestionIndex === 0,\n              className: `trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`,\n              whileHover: currentQuestionIndex > 0 ? {\n                scale: 1.02\n              } : {},\n              whileTap: currentQuestionIndex > 0 ? {\n                scale: 0.98\n              } : {},\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: handleSubmitQuiz,\n              disabled: isSubmitting,\n              className: `trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`,\n              whileHover: !isSubmitting ? {\n                scale: 1.02\n              } : {},\n              whileTap: !isSubmitting ? {\n                scale: 0.98\n              } : {},\n              children: [isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trial-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbCheck, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isSubmitting ? 'Submitting...' : 'Submit Quiz'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToNext,\n              className: \"trial-btn trial-btn-primary\",\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                style: {\n                  width: '20px',\n                  height: '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)]\n      }, currentQuestionIndex, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\",\n      children: \"Trial Mode\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizPlay, \"poqwUhvGWiexGFrQc9xn7WhRFI8=\");\n_c = TrialQuizPlay;\nexport default TrialQuizPlay;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "TbArrowLeft", "TbArrowRight", "TbClock", "TbCheck", "message", "submitTrialResult", "jsxDEV", "_jsxDEV", "TrialQuizPlay", "quizData", "onComplete", "onBack", "_s", "exam", "trialUserInfo", "questions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedAnswer<PERSON>", "setSelectedAnswers", "timeLeft", "setTimeLeft", "duration", "isSubmitting", "setIsSubmitting", "startTime", "Date", "now", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "questionId", "answer", "playSuccessSound", "playSubmitSound", "timeSpent", "round", "response", "examId", "_id", "answers", "success", "data", "error", "console", "playNavigationSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "log", "goToNext", "length", "goToPrevious", "goToQuestion", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "isLastQuestion", "answeredQuestions", "Object", "keys", "questionType", "type", "answerType", "isMCQ", "isFillBlank", "isDiagram", "image", "questionOptions", "Array", "isArray", "options", "values", "optionA", "optionB", "optionC", "optionD", "filter", "Boolean", "style", "minHeight", "background", "width", "overflow", "boxShadow", "borderBottom", "position", "top", "zIndex", "display", "alignItems", "justifyContent", "marginBottom", "flexWrap", "gap", "min<PERSON><PERSON><PERSON>", "flex", "max<PERSON><PERSON><PERSON>", "padding", "border", "borderRadius", "cursor", "transition", "onMouseEnter", "e", "target", "onMouseLeave", "height", "color", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "margin", "textOverflow", "whiteSpace", "lineHeight", "name", "subject", "flexShrink", "paddingTop", "paddingBottom", "div", "initial", "opacity", "y", "animate", "exit", "ease", "boxSizing", "textAlign", "src", "alt", "maxHeight", "map", "option", "optionLetter", "String", "fromCharCode", "isSelected", "button", "whileHover", "scale", "whileTap", "value", "onChange", "placeholder", "paddingRight", "right", "transform", "marginTop", "fontStyle", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, Tb<PERSON>lock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    playSuccessSound();\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    playSubmitSound(); // Play submit sound\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Enhanced Sound effects for navigation\n  const playNavigationSound = () => {\n    try {\n      // Create a simple click sound using Web Audio API\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);\n\n      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.1);\n    } catch (error) {\n      console.log('Navigation sound not available');\n    }\n  };\n\n  const playSuccessSound = () => {\n    try {\n      // Create a pleasant success sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5\n      oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G5\n\n      gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n    } catch (error) {\n      console.log('Success sound not available');\n    }\n  };\n\n  const playSubmitSound = () => {\n    try {\n      // Create a completion sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4\n      oscillator.frequency.setValueAtTime(554, audioContext.currentTime + 0.15); // C#5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.3); // E5\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime + 0.45); // A5\n\n      gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.6);\n    } catch (error) {\n      console.log('Submit sound not available');\n    }\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    playNavigationSound();\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n  const isDiagram = questionType === 'diagram' || questionType === 'Diagram' || currentQuestion?.image;\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion?.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);\n    }\n  }\n\n  return (\n    <div className=\"trial-background\" style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)',\n      width: '100%',\n      overflow: 'hidden'\n    }}>\n      {/* Enhanced Header with Progress */}\n      <div className=\"trial-header\" style={{\n        background: 'white',\n        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n        borderBottom: '2px solid #e0e7ff',\n        width: '100%',\n        position: 'sticky',\n        top: 0,\n        zIndex: 100\n      }}>\n        <div className=\"trial-container\">\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: 'clamp(12px, 3vw, 20px)',\n            flexWrap: 'wrap',\n            gap: 'clamp(8px, 2vw, 16px)',\n            width: '100%'\n          }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              minWidth: 0,\n              flex: 1,\n              maxWidth: '100%'\n            }}>\n              <button\n                onClick={onBack}\n                style={{\n                  padding: 'clamp(8px, 2vw, 12px)',\n                  background: 'rgba(59, 130, 246, 0.1)',\n                  border: 'none',\n                  borderRadius: 'clamp(8px, 2vw, 12px)',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  minWidth: '40px',\n                  minHeight: '40px'\n                }}\n                onMouseEnter={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.2)'}\n                onMouseLeave={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.1)'}\n              >\n                <TbArrowLeft style={{\n                  width: 'clamp(18px, 4vw, 24px)',\n                  height: 'clamp(18px, 4vw, 24px)',\n                  color: '#2563eb'\n                }} />\n              </button>\n              <div style={{ minWidth: 0, flex: 1, overflow: 'hidden' }}>\n                <h1 style={{\n                  fontSize: 'clamp(16px, 4vw, 24px)',\n                  fontWeight: 'bold',\n                  background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  margin: 0,\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  lineHeight: 1.2\n                }}>{exam.name}</h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-sm sm:text-base text-gray-600\">{exam.subject}</span>\n                  <span className=\"text-gray-400\">•</span>\n                  <span className=\"text-sm sm:text-base font-medium text-blue-600\">\n                    Question {currentQuestionIndex + 1} of {questions.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              flexShrink: 0,\n              flexWrap: 'wrap',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Enhanced Timer with Professional Styling */}\n              <div style={{ position: 'relative' }}>\n                <div className={`trial-timer ${\n                  timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'\n                }`}>\n                  <TbClock className=\"trial-timer-icon\" />\n                  <span>{formatTime(timeLeft)}</span>\n                </div>\n                {/* Warning animation for low time */}\n                {timeLeft <= 300 && (\n                  <div className=\"timer-warning-ring\"></div>\n                )}\n              </div>\n\n              <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {answeredQuestions}/{questions.length} answered\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Progress Bar */}\n          <div className=\"trial-progress-container\">\n            <div className=\"trial-progress-header\">\n              <span className=\"trial-progress-label\">\n                Progress: {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              <span className=\"trial-progress-percentage\">\n                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div className=\"trial-progress-bar\">\n              <div\n                className=\"trial-progress-fill\"\n                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Mobile progress indicator */}\n          <div className=\"sm:hidden mt-3 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\">\n              <span className=\"text-sm font-medium text-gray-700\">\n                {answeredQuestions}/{questions.length} answered\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Question Content */}\n      <div className=\"trial-container\" style={{\n        paddingTop: 'clamp(16px, 4vw, 32px)',\n        paddingBottom: 'clamp(16px, 4vw, 32px)'\n      }}>\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"trial-question-card\"\n        >\n          {/* Question Header */}\n          <div className=\"trial-question-header\">\n            <div className=\"trial-question-number\">\n              <div className=\"trial-question-number-badge\">\n                <span>{currentQuestionIndex + 1}</span>\n              </div>\n              <div>\n                <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>Question</div>\n                <div style={{ color: 'white', fontWeight: '500', fontSize: '18px' }}>\n                  {currentQuestionIndex + 1} of {questions.length}\n                </div>\n              </div>\n            </div>\n\n            <h2 className=\"trial-question-title\">\n              {currentQuestion.name}\n            </h2>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"trial-content\" style={{\n            padding: 'clamp(16px, 4vw, 40px)',\n            width: '100%',\n            boxSizing: 'border-box'\n          }}>\n            {/* Question Image (if exists) */}\n            {currentQuestion.image && (\n              <div style={{ marginBottom: '32px', textAlign: 'center' }}>\n                <img\n                  src={currentQuestion.image}\n                  alt=\"Question\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    borderRadius: '16px',\n                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* Answer Section */}\n            <div className=\"mb-10 sm:mb-12\">\n              {isMCQ && questionOptions.length > 0 ? (\n                // Multiple Choice Questions\n                <div className=\"space-y-4 sm:space-y-5\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800 mb-6\">\n                    Choose your answer:\n                  </h3>\n                  {questionOptions.map((option, index) => {\n                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n\n                    return (\n                      <motion.button\n                        key={index}\n                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                        className={`trial-option ${isSelected ? 'selected' : ''}`}\n                        whileHover={{ scale: isSelected ? 1.01 : 1.005 }}\n                        whileTap={{ scale: 0.995 }}\n                      >\n                        <div className=\"trial-option-content\">\n                          <div className=\"trial-option-letter\">\n                            {isSelected ? <TbCheck style={{ width: '24px', height: '24px' }} /> : optionLetter}\n                          </div>\n                          <span className=\"trial-option-text\">\n                            {option}\n                          </span>\n                        </div>\n                      </motion.button>\n                    );\n                  })}\n                </div>\n              ) : isFillBlank ? (\n                // Fill-in-the-blank / Free Text Questions\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    💭 Type your answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"trial-input\"\n                      style={{ paddingRight: '60px' }}\n                    />\n                    <div style={{\n                      position: 'absolute',\n                      right: '20px',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      fontSize: '24px',\n                      color: '#9ca3af'\n                    }}>\n                      ✏️\n                    </div>\n                  </div>\n                  <div style={{\n                    marginTop: '12px',\n                    fontSize: '14px',\n                    color: '#6b7280',\n                    fontStyle: 'italic'\n                  }}>\n                    💡 Tip: Be specific and clear in your answer\n                  </div>\n                </div>\n              ) : isDiagram ? (\n                // Diagram Questions\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    🔍 Study the diagram and answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Analyze the diagram and type your answer...\"\n                      className=\"trial-input\"\n                      style={{ paddingRight: '60px' }}\n                    />\n                    <div style={{\n                      position: 'absolute',\n                      right: '20px',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      fontSize: '24px',\n                      color: '#9ca3af'\n                    }}>\n                      📊\n                    </div>\n                  </div>\n                  <div style={{\n                    marginTop: '12px',\n                    fontSize: '14px',\n                    color: '#6b7280',\n                    fontStyle: 'italic'\n                  }}>\n                    🎯 Tip: Look carefully at all parts of the diagram\n                  </div>\n                </div>\n              ) : (\n                // Default fallback\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    📝 Provide your answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"trial-input\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"trial-nav-buttons\">\n              <motion.button\n                onClick={goToPrevious}\n                disabled={currentQuestionIndex === 0}\n                className={`trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`}\n                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}\n                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}\n              >\n                <TbArrowLeft style={{ width: '20px', height: '20px' }} />\n                <span>Previous</span>\n              </motion.button>\n\n              {isLastQuestion ? (\n                <motion.button\n                  onClick={handleSubmitQuiz}\n                  disabled={isSubmitting}\n                  className={`trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`}\n                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n                >\n                  {isSubmitting ? (\n                    <div className=\"trial-spinner\" />\n                  ) : (\n                    <TbCheck style={{ width: '20px', height: '20px' }} />\n                  )}\n                  <span>\n                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\n                  </span>\n                </motion.button>\n              ) : (\n                <motion.button\n                  onClick={goToNext}\n                  className=\"trial-btn trial-btn-primary\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Next Question</span>\n                  <TbArrowRight style={{ width: '20px', height: '20px' }} />\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\">\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAGL,QAAQ;EACxC,MAAMM,SAAS,GAAGF,IAAI,CAACE,SAAS,IAAI,EAAE;EAEtC,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,CAACiB,IAAI,CAACS,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,SAAS,CAAC,GAAG7B,QAAQ,CAAC8B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAExC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIuB,QAAQ,IAAI,CAAC,EAAE;MACjBQ,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAACU,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMa,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDC,gBAAgB,CAAC,CAAC;IAClBzB,kBAAkB,CAACY,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAG9B,WAAW,CAAC,YAAY;IAC/C,IAAIyB,YAAY,EAAE;IAElBsB,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBrB,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMsB,SAAS,GAAGV,IAAI,CAACW,KAAK,CAAC,CAACrB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;;MAE/D,MAAMuB,QAAQ,GAAG,MAAM3C,iBAAiB,CAAC;QACvC4C,MAAM,EAAEpC,IAAI,CAACqC,GAAG;QAChBC,OAAO,EAAEjC,eAAe;QACxB4B,SAAS;QACThC;MACF,CAAC,CAAC;MAEF,IAAIkC,QAAQ,CAACI,OAAO,EAAE;QACpB1C,UAAU,CAACsC,QAAQ,CAACK,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLjD,OAAO,CAACkD,KAAK,CAACN,QAAQ,CAAC5C,OAAO,IAAI,uBAAuB,CAAC;QAC1DoB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDlD,OAAO,CAACkD,KAAK,CAAC,yCAAyC,CAAC;MACxD9B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACX,IAAI,CAACqC,GAAG,EAAEhC,eAAe,EAAEJ,aAAa,EAAEW,SAAS,EAAEf,UAAU,EAAEa,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,CAAC,CAAC;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACR,YAAY,CAACS,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC;MAClER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;MAEtFN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC;MAC3DN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;MAEhFR,UAAU,CAACW,KAAK,CAACf,YAAY,CAACY,WAAW,CAAC;MAC1CR,UAAU,CAACY,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IACjD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACmB,GAAG,CAAC,gCAAgC,CAAC;IAC/C;EACF,CAAC;EAED,MAAM9B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF;MACA,MAAMa,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,CAAC,CAAC;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACR,YAAY,CAACS,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC,CAAC,CAAC;MACpER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAC1ER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;;MAE1EN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,EAAEX,YAAY,CAACY,WAAW,CAAC;MAC5DN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;MAEhFR,UAAU,CAACW,KAAK,CAACf,YAAY,CAACY,WAAW,CAAC;MAC1CR,UAAU,CAACY,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IACjD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACmB,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC;EAED,MAAM7B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI;MACF;MACA,MAAMY,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,CAAC,CAAC;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACR,YAAY,CAACS,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC,CAAC,CAAC;MACpER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC;MAC3ER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAC1ER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC;;MAE3EN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC;MAC3DN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;MAEhFR,UAAU,CAACW,KAAK,CAACf,YAAY,CAACY,WAAW,CAAC;MAC1CR,UAAU,CAACY,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IACjD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACmB,GAAG,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI3D,oBAAoB,GAAGD,SAAS,CAAC6D,MAAM,GAAG,CAAC,EAAE;MAC/CpB,mBAAmB,CAAC,CAAC;MACrBvC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAM6D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7D,oBAAoB,GAAG,CAAC,EAAE;MAC5BwC,mBAAmB,CAAC,CAAC;MACrBvC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAM8D,YAAY,GAAIC,KAAK,IAAK;IAC9BvB,mBAAmB,CAAC,CAAC;IACrBvC,uBAAuB,CAAC8D,KAAK,CAAC;EAChC,CAAC;EAED,IAAIhE,SAAS,CAAC6D,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACErE,OAAA;MAAKyE,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpH1E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1E,OAAA;UAAIyE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF9E,OAAA;UAAGyE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3E9E,OAAA;UACE+E,OAAO,EAAE3E,MAAO;UAChBqE,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGxE,SAAS,CAACC,oBAAoB,CAAC;EACvD,MAAMwE,cAAc,GAAGxE,oBAAoB,KAAKD,SAAS,CAAC6D,MAAM,GAAG,CAAC;EACpE,MAAMa,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACzE,eAAe,CAAC,CAAC0D,MAAM;;EAE7D;EACA,MAAMgB,YAAY,GAAG,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,MAAIN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,UAAU,KAAI,KAAK;EAClF,MAAMC,KAAK,GAAGH,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,iBAAiB;EACxG,MAAMI,WAAW,GAAGJ,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,mBAAmB,IAAIA,YAAY,KAAK,WAAW;EACnH,MAAMK,SAAS,GAAGL,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,SAAS,KAAIL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEW,KAAK;;EAEpG;EACA,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIJ,KAAK,EAAE;IACT,IAAIK,KAAK,CAACC,OAAO,CAACd,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,OAAO,CAAC,EAAE;MAC3CH,eAAe,GAAGZ,eAAe,CAACe,OAAO;IAC3C,CAAC,MAAM,IAAI,QAAOf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,OAAO,MAAK,QAAQ,IAAI,CAAAf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,OAAO,MAAK,IAAI,EAAE;MAC5FH,eAAe,GAAGT,MAAM,CAACa,MAAM,CAAChB,eAAe,CAACe,OAAO,CAAC;IAC1D,CAAC,MAAM;MACLH,eAAe,GAAG,CAACZ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiB,OAAO,EAAEjB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkB,OAAO,EAAElB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmB,OAAO,EAAEnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC5I;EACF;EAEA,oBACEtG,OAAA;IAAKyE,SAAS,EAAC,kBAAkB;IAAC8B,KAAK,EAAE;MACvCC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE;IACZ,CAAE;IAAAjC,QAAA,gBAEA1E,OAAA;MAAKyE,SAAS,EAAC,cAAc;MAAC8B,KAAK,EAAE;QACnCE,UAAU,EAAE,OAAO;QACnBG,SAAS,EAAE,gCAAgC;QAC3CC,YAAY,EAAE,mBAAmB;QACjCH,KAAK,EAAE,MAAM;QACbI,QAAQ,EAAE,QAAQ;QAClBC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACV,CAAE;MAAAtC,QAAA,eACA1E,OAAA;QAAKyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1E,OAAA;UAAKuG,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BC,YAAY,EAAE,wBAAwB;YACtCC,QAAQ,EAAE,MAAM;YAChBC,GAAG,EAAE,uBAAuB;YAC5BZ,KAAK,EAAE;UACT,CAAE;UAAAhC,QAAA,gBACA1E,OAAA;YAAKuG,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,uBAAuB;cAC5BC,QAAQ,EAAE,CAAC;cACXC,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE;YACZ,CAAE;YAAA/C,QAAA,gBACA1E,OAAA;cACE+E,OAAO,EAAE3E,MAAO;cAChBmG,KAAK,EAAE;gBACLmB,OAAO,EAAE,uBAAuB;gBAChCjB,UAAU,EAAE,yBAAyB;gBACrCkB,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,uBAAuB;gBACrCC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,eAAe;gBAC3Bb,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBI,QAAQ,EAAE,MAAM;gBAChBf,SAAS,EAAE;cACb,CAAE;cACFuB,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAACE,UAAU,GAAG,yBAA0B;cAC3EyB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAACE,UAAU,GAAG,yBAA0B;cAAA/B,QAAA,eAE3E1E,OAAA,CAACP,WAAW;gBAAC8G,KAAK,EAAE;kBAClBG,KAAK,EAAE,wBAAwB;kBAC/ByB,MAAM,EAAE,wBAAwB;kBAChCC,KAAK,EAAE;gBACT;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACT9E,OAAA;cAAKuG,KAAK,EAAE;gBAAEgB,QAAQ,EAAE,CAAC;gBAAEC,IAAI,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACvD1E,OAAA;gBAAIuG,KAAK,EAAE;kBACT8B,QAAQ,EAAE,wBAAwB;kBAClCC,UAAU,EAAE,MAAM;kBAClB7B,UAAU,EAAE,mDAAmD;kBAC/D8B,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,MAAM,EAAE,CAAC;kBACT9B,QAAQ,EAAE,QAAQ;kBAClB+B,YAAY,EAAE,UAAU;kBACxBC,UAAU,EAAE,QAAQ;kBACpBC,UAAU,EAAE;gBACd,CAAE;gBAAAlE,QAAA,EAAEpE,IAAI,CAACuI;cAAI;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnB9E,OAAA;gBAAKyE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C1E,OAAA;kBAAMyE,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAEpE,IAAI,CAACwI;gBAAO;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1E9E,OAAA;kBAAMyE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC9E,OAAA;kBAAMyE,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAAC,WACtD,EAACjE,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAAC6D,MAAM;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9E,OAAA;YAAKuG,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,uBAAuB;cAC5ByB,UAAU,EAAE,CAAC;cACb1B,QAAQ,EAAE,MAAM;cAChBF,cAAc,EAAE;YAClB,CAAE;YAAAzC,QAAA,gBAEA1E,OAAA;cAAKuG,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAW,CAAE;cAAApC,QAAA,gBACnC1E,OAAA;gBAAKyE,SAAS,EAAG,eACf5D,QAAQ,IAAI,EAAE,GAAG,UAAU,GAAGA,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAG,QAC7D,EAAE;gBAAA6D,QAAA,gBACD1E,OAAA,CAACL,OAAO;kBAAC8E,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxC9E,OAAA;kBAAA0E,QAAA,EAAOhD,UAAU,CAACb,QAAQ;gBAAC;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EAELjE,QAAQ,IAAI,GAAG,iBACdb,OAAA;gBAAKyE,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN9E,OAAA;cAAKyE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF1E,OAAA;gBAAMyE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAAC1E,SAAS,CAAC6D,MAAM,EAAC,WACxC;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9E,OAAA;UAAKyE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC1E,OAAA;YAAKyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC1E,OAAA;cAAMyE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,YAC3B,EAACjE,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAAC6D,MAAM;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACP9E,OAAA;cAAMyE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,GACxC7C,IAAI,CAACW,KAAK,CAAE,CAAC/B,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAAC6D,MAAM,GAAI,GAAG,CAAC,EAAC,GACrE;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9E,OAAA;YAAKyE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjC1E,OAAA;cACEyE,SAAS,EAAC,qBAAqB;cAC/B8B,KAAK,EAAE;gBAAEG,KAAK,EAAG,GAAG,CAACjG,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAAC6D,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9E,OAAA;UAAKyE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9D1E,OAAA;YAAKyE,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3E1E,OAAA;cAAMyE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAAC1E,SAAS,CAAC6D,MAAM,EAAC,WACxC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAKN9E,OAAA;MAAKyE,SAAS,EAAC,iBAAiB;MAAC8B,KAAK,EAAE;QACtCyC,UAAU,EAAE,wBAAwB;QACpCC,aAAa,EAAE;MACjB,CAAE;MAAAvE,QAAA,eACA1E,OAAA,CAACR,MAAM,CAAC0J,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BvB,UAAU,EAAE;UAAE/G,QAAQ,EAAE,GAAG;UAAEyI,IAAI,EAAE;QAAU,CAAE;QAC/C/E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B1E,OAAA;UAAKyE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC1E,OAAA;YAAKyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC1E,OAAA;cAAKyE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1C1E,OAAA;gBAAA0E,QAAA,EAAOjE,oBAAoB,GAAG;cAAC;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN9E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAKuG,KAAK,EAAE;kBAAE6B,KAAK,EAAE,0BAA0B;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAA3D,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnF9E,OAAA;gBAAKuG,KAAK,EAAE;kBAAE6B,KAAK,EAAE,OAAO;kBAAEE,UAAU,EAAE,KAAK;kBAAED,QAAQ,EAAE;gBAAO,CAAE;gBAAA3D,QAAA,GACjEjE,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAAC6D,MAAM;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9E,OAAA;YAAIyE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EACjCM,eAAe,CAAC6D;UAAI;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGN9E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAC8B,KAAK,EAAE;YACpCmB,OAAO,EAAE,wBAAwB;YACjChB,KAAK,EAAE,MAAM;YACb+C,SAAS,EAAE;UACb,CAAE;UAAA/E,QAAA,GAECM,eAAe,CAACW,KAAK,iBACpB3F,OAAA;YAAKuG,KAAK,EAAE;cAAEa,YAAY,EAAE,MAAM;cAAEsC,SAAS,EAAE;YAAS,CAAE;YAAAhF,QAAA,eACxD1E,OAAA;cACE2J,GAAG,EAAE3E,eAAe,CAACW,KAAM;cAC3BiE,GAAG,EAAC,UAAU;cACdrD,KAAK,EAAE;gBACLkB,QAAQ,EAAE,MAAM;gBAChBoC,SAAS,EAAE,OAAO;gBAClBjC,YAAY,EAAE,MAAM;gBACpBhB,SAAS,EAAE;cACb;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGD9E,OAAA;YAAKyE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bc,KAAK,IAAII,eAAe,CAACvB,MAAM,GAAG,CAAC;YAAA;YAClC;YACArE,OAAA;cAAKyE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC1E,OAAA;gBAAIyE,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJc,eAAe,CAACkE,GAAG,CAAC,CAACC,MAAM,EAAEvF,KAAK,KAAK;gBACtC,MAAMwF,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG1F,KAAK,CAAC,CAAC,CAAC;gBACtD,MAAM2F,UAAU,GAAGxJ,eAAe,CAACqE,eAAe,CAACrC,GAAG,CAAC,KAAKqH,YAAY;gBAExE,oBACEhK,OAAA,CAACR,MAAM,CAAC4K,MAAM;kBAEZrF,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC8C,eAAe,CAACrC,GAAG,EAAEqH,YAAY,CAAE;kBACrEvF,SAAS,EAAG,gBAAe0F,UAAU,GAAG,UAAU,GAAG,EAAG,EAAE;kBAC1DE,UAAU,EAAE;oBAAEC,KAAK,EAAEH,UAAU,GAAG,IAAI,GAAG;kBAAM,CAAE;kBACjDI,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAM,CAAE;kBAAA5F,QAAA,eAE3B1E,OAAA;oBAAKyE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnC1E,OAAA;sBAAKyE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EACjCyF,UAAU,gBAAGnK,OAAA,CAACJ,OAAO;wBAAC2G,KAAK,EAAE;0BAAEG,KAAK,EAAE,MAAM;0BAAEyB,MAAM,EAAE;wBAAO;sBAAE;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAGkF;oBAAY;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eACN9E,OAAA;sBAAMyE,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAChCqF;oBAAM;sBAAApF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAbDN,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcG,CAAC;cAEpB,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,GACJW,WAAW;YAAA;YACb;YACAzF,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAIuG,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAEhB,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAE5F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,gBACnC1E,OAAA;kBACEsF,IAAI,EAAC,MAAM;kBACXkF,KAAK,EAAE7J,eAAe,CAACqE,eAAe,CAACrC,GAAG,CAAC,IAAI,EAAG;kBAClD8H,QAAQ,EAAGzC,CAAC,IAAK9F,kBAAkB,CAAC8C,eAAe,CAACrC,GAAG,EAAEqF,CAAC,CAACC,MAAM,CAACuC,KAAK,CAAE;kBACzEE,WAAW,EAAC,0BAA0B;kBACtCjG,SAAS,EAAC,aAAa;kBACvB8B,KAAK,EAAE;oBAAEoE,YAAY,EAAE;kBAAO;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACF9E,OAAA;kBAAKuG,KAAK,EAAE;oBACVO,QAAQ,EAAE,UAAU;oBACpB8D,KAAK,EAAE,MAAM;oBACb7D,GAAG,EAAE,KAAK;oBACV8D,SAAS,EAAE,kBAAkB;oBAC7BxC,QAAQ,EAAE,MAAM;oBAChBD,KAAK,EAAE;kBACT,CAAE;kBAAA1D,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9E,OAAA;gBAAKuG,KAAK,EAAE;kBACVuE,SAAS,EAAE,MAAM;kBACjBzC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB2C,SAAS,EAAE;gBACb,CAAE;gBAAArG,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJY,SAAS;YAAA;YACX;YACA1F,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAIuG,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAEhB,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAE5F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,gBACnC1E,OAAA;kBACEsF,IAAI,EAAC,MAAM;kBACXkF,KAAK,EAAE7J,eAAe,CAACqE,eAAe,CAACrC,GAAG,CAAC,IAAI,EAAG;kBAClD8H,QAAQ,EAAGzC,CAAC,IAAK9F,kBAAkB,CAAC8C,eAAe,CAACrC,GAAG,EAAEqF,CAAC,CAACC,MAAM,CAACuC,KAAK,CAAE;kBACzEE,WAAW,EAAC,6CAA6C;kBACzDjG,SAAS,EAAC,aAAa;kBACvB8B,KAAK,EAAE;oBAAEoE,YAAY,EAAE;kBAAO;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACF9E,OAAA;kBAAKuG,KAAK,EAAE;oBACVO,QAAQ,EAAE,UAAU;oBACpB8D,KAAK,EAAE,MAAM;oBACb7D,GAAG,EAAE,KAAK;oBACV8D,SAAS,EAAE,kBAAkB;oBAC7BxC,QAAQ,EAAE,MAAM;oBAChBD,KAAK,EAAE;kBACT,CAAE;kBAAA1D,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9E,OAAA;gBAAKuG,KAAK,EAAE;kBACVuE,SAAS,EAAE,MAAM;kBACjBzC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB2C,SAAS,EAAE;gBACb,CAAE;gBAAArG,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACA9E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAIuG,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAEhB,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAE5F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,eACnC1E,OAAA;kBACEsF,IAAI,EAAC,MAAM;kBACXkF,KAAK,EAAE7J,eAAe,CAACqE,eAAe,CAACrC,GAAG,CAAC,IAAI,EAAG;kBAClD8H,QAAQ,EAAGzC,CAAC,IAAK9F,kBAAkB,CAAC8C,eAAe,CAACrC,GAAG,EAAEqF,CAAC,CAACC,MAAM,CAACuC,KAAK,CAAE;kBACzEE,WAAW,EAAC,0BAA0B;kBACtCjG,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9E,OAAA;YAAKyE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1E,OAAA,CAACR,MAAM,CAAC4K,MAAM;cACZrF,OAAO,EAAET,YAAa;cACtB0G,QAAQ,EAAEvK,oBAAoB,KAAK,CAAE;cACrCgE,SAAS,EAAG,iCAAgChE,oBAAoB,KAAK,CAAC,GAAG,EAAE,GAAG,EAAG,EAAE;cACnF4J,UAAU,EAAE5J,oBAAoB,GAAG,CAAC,GAAG;gBAAE6J,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAC5DC,QAAQ,EAAE9J,oBAAoB,GAAG,CAAC,GAAG;gBAAE6J,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAA5F,QAAA,gBAE1D1E,OAAA,CAACP,WAAW;gBAAC8G,KAAK,EAAE;kBAAEG,KAAK,EAAE,MAAM;kBAAEyB,MAAM,EAAE;gBAAO;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD9E,OAAA;gBAAA0E,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEfG,cAAc,gBACbjF,OAAA,CAACR,MAAM,CAAC4K,MAAM;cACZrF,OAAO,EAAE1D,gBAAiB;cAC1B2J,QAAQ,EAAEhK,YAAa;cACvByD,SAAS,EAAG,aAAYzD,YAAY,GAAG,qBAAqB,GAAG,mBAAoB,EAAE;cACrFqJ,UAAU,EAAE,CAACrJ,YAAY,GAAG;gBAAEsJ,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cACjDC,QAAQ,EAAE,CAACvJ,YAAY,GAAG;gBAAEsJ,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAA5F,QAAA,GAE9C1D,YAAY,gBACXhB,OAAA;gBAAKyE,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjC9E,OAAA,CAACJ,OAAO;gBAAC2G,KAAK,EAAE;kBAAEG,KAAK,EAAE,MAAM;kBAAEyB,MAAM,EAAE;gBAAO;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrD,eACD9E,OAAA;gBAAA0E,QAAA,EACG1D,YAAY,GAAG,eAAe,GAAG;cAAa;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,gBAEhB9E,OAAA,CAACR,MAAM,CAAC4K,MAAM;cACZrF,OAAO,EAAEX,QAAS;cAClBK,SAAS,EAAC,6BAA6B;cACvC4F,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAA5F,QAAA,gBAE1B1E,OAAA;gBAAA0E,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B9E,OAAA,CAACN,YAAY;gBAAC6G,KAAK,EAAE;kBAAEG,KAAK,EAAE,MAAM;kBAAEyB,MAAM,EAAE;gBAAO;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAnNDrE,oBAAoB;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoNf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,oGAAoG;MAAAC,QAAA,EAAC;IAEpH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CA3jBIJ,aAAa;AAAAgL,EAAA,GAAbhL,aAAa;AA6jBnB,eAAeA,aAAa;AAAC,IAAAgL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}