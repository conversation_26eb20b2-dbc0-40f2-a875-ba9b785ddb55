const axios = require('axios');

// Test Quiz Navigation Functionality
async function testQuizNavigation() {
  console.log('🎯 Testing Quiz Navigation Functionality...\n');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // 1. Test trial quiz to verify navigation works
    console.log('1️⃣ Testing trial quiz navigation...');
    const trialResponse = await axios.post(`${baseURL}/trial/get-trial-quiz`, {
      level: 'primary',
      class: '3'
    });
    
    if (trialResponse.data.success && trialResponse.data.data) {
      const exam = trialResponse.data.data.exam;
      console.log('✅ Trial quiz found:', exam.name);
      console.log('📝 Questions:', exam.questions?.length || 0);
      
      if (exam.questions && exam.questions.length > 1) {
        console.log('✅ Multiple questions available for navigation testing');
        console.log('🔍 Navigation test scenarios:');
        console.log('  - Previous button should be disabled on first question');
        console.log('  - Next button should show "Next" for questions 1-' + (exam.questions.length - 1));
        console.log('  - Next button should show "Finish Quiz" on last question');
        console.log('  - Question navigation grid should show all', exam.questions.length, 'questions');
        
        // Test question structure for rendering
        exam.questions.forEach((q, index) => {
          const hasValidStructure = q && (q.name || q.question) && q._id;
          console.log(`  Question ${index + 1}: ${hasValidStructure ? '✅ Valid' : '❌ Invalid'} structure`);
          
          if (!hasValidStructure) {
            console.log(`    - Missing: ${!q._id ? 'ID ' : ''}${!(q.name || q.question) ? 'Text ' : ''}`);
          }
        });
      } else {
        console.log('⚠️ Only one question available - limited navigation testing');
      }
    }
    
    // 2. Test server health to ensure backend is working
    console.log('\n2️⃣ Testing server connectivity...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ Server health:', healthResponse.data.status);
    
    console.log('\n🎉 Quiz navigation test completed!');
    console.log('\n📋 Navigation Features to Test in Browser:');
    console.log('✅ Previous/Next buttons visibility');
    console.log('✅ Question navigation grid');
    console.log('✅ Progress indicators');
    console.log('✅ Question type badges');
    console.log('✅ Answer state tracking');
    console.log('✅ Mobile responsiveness');
    
    console.log('\n🔗 Test URLs:');
    console.log('- Home: http://localhost:3000');
    console.log('- Trial Quiz: http://localhost:3000/trial');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testQuizNavigation();
