require('dotenv').config();
const mongoose = require('mongoose');
const Exam = require('./server/models/examModel');

async function updateExamDurations() {
  try {
    console.log('🔄 Connecting to database...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    // Update all exams to have 3-minute duration (180 seconds)
    const result = await Exam.updateMany(
      {}, // Update all exams
      { 
        $set: { 
          duration: 180 // 3 minutes in seconds
        } 
      }
    );

    console.log(`✅ Updated ${result.modifiedCount} exams to 3-minute duration`);

    // Verify the update
    const sampleExams = await Exam.find({}).limit(5).select('name duration');
    console.log('\n📊 Sample updated exams:');
    sampleExams.forEach(exam => {
      console.log(`- ${exam.name}: ${exam.duration} seconds (${exam.duration/60} minutes)`);
    });

    // Get total count
    const totalExams = await Exam.countDocuments();
    console.log(`\n📈 Total exams in database: ${totalExams}`);
    console.log('🎉 All exams now have 3-minute duration!');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating exam durations:', error);
    process.exit(1);
  }
}

updateExamDurations();
