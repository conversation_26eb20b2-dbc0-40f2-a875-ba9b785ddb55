/* ===== RESPONSIVE REGISTER PAGE ===== */

.register-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
}

.register-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);
    z-index: 0;
}

.register-card {
    width: 100%;
    max-width: 480px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

.register-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* ===== HEADER SECTION ===== */
.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: block;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.register-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);
}

.verification-icon {
    width: 64px;
    height: 64px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.verification-icon i {
    font-size: 1.5rem;
    color: #007BFF;
}

.register-title {
    font-size: 1.875rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.register-subtitle {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* ===== OTP VERIFICATION STYLING ===== */
.otp-instructions {
    margin-bottom: 2rem;
    width: 100%;
}

.otp-info-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #bae6fd;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.otp-info-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #0c4a6e;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.otp-info-text {
    color: #0369a1;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.otp-steps {
    margin-bottom: 1rem;
}

.otp-step {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0.5rem;
    border: 1px solid rgba(14, 165, 233, 0.2);
}

.step-number {
    width: 1.5rem;
    height: 1.5rem;
    background: #0ea5e9;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-text {
    color: #0c4a6e;
    font-size: 0.875rem;
    font-weight: 500;
}

.otp-help {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.5rem;
    padding: 0.75rem;
    border: 1px solid rgba(14, 165, 233, 0.3);
}

.help-text {
    color: #0369a1;
    font-size: 0.8rem;
    margin: 0;
    line-height: 1.4;
}

.otp-input {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: 0.25rem;
}

/* ===== RESEND SECTION STYLING ===== */
.resend-section {
    margin-top: 1.5rem;
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
}

.resend-text {
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.resend-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.resend-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.resend-btn:disabled {
    background: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* ===== FORM STYLING ===== */
.register-form {
    margin-bottom: 1.5rem;
}

.register-form .ant-form-item {
    margin-bottom: 1.25rem;
}

.register-form .ant-form-item-label > label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #f9fafb;
    color: #1f2937;
    font-family: inherit;
    line-height: 1.5;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

.form-help-text {
    margin-top: 0.375rem;
    font-size: 0.75rem;
    color: #6b7280;
    font-style: italic;
    line-height: 1.4;
}

/* ===== VALIDATION STATES ===== */
/* Valid/Filled field styling with green border and checkmark */
.ant-form-item-has-success .form-input,
.ant-form-item-has-success .ant-select-selector {
    border-color: #52c41a !important;
    border-width: 2px !important;
    background: #f6ffed !important;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.15) !important;
    position: relative;
    transition: all 0.3s ease;
}

.ant-form-item-has-success .form-input:focus,
.ant-form-item-has-success .ant-select-selector:focus {
    border-color: #52c41a !important;
    background: #ffffff !important;
    box-shadow: 0 0 0 3px rgba(82, 196, 26, 0.25) !important;
    transform: translateY(-1px);
}

/* Green checkmark icon for valid fields */
.ant-form-item-has-success .ant-form-item-control {
    position: relative;
}

.ant-form-item-has-success .ant-form-item-control::after {
    content: "✓";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #52c41a;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    z-index: 10;
    background: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

/* Error field styling with red border */
.ant-form-item-has-error .form-input,
.ant-form-item-has-error .ant-select-selector {
    border-color: #ff4d4f !important;
    border-width: 2px !important;
    background: #fff2f0 !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.15) !important;
    transition: all 0.3s ease;
}

.ant-form-item-has-error .form-input:focus,
.ant-form-item-has-error .ant-select-selector:focus {
    border-color: #ff4d4f !important;
    background: #ffffff !important;
    box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.25) !important;
    transform: translateY(-1px);
}

/* Default state - neutral styling */
.ant-form-item:not(.ant-form-item-has-error):not(.ant-form-item-has-success) .form-input,
.ant-form-item:not(.ant-form-item-has-error):not(.ant-form-item-has-success) .ant-select-selector {
    border-color: #d1d5db !important;
    border-width: 1px !important;
    background: #ffffff !important;
    transition: all 0.3s ease;
}

/* Red X icon for error fields */
.ant-form-item-has-error .ant-form-item-control::after {
    content: "✗";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff4d4f;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    z-index: 10;
    background: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

/* Special handling for select fields */
.ant-form-item-has-success .ant-select .ant-select-selector::after,
.ant-form-item-has-error .ant-select .ant-select-selector::after {
    display: none; /* Hide for select fields as they have their own arrow */
}

/* Adjust padding for inputs to make room for checkmark */
.ant-form-item-has-success .form-input,
.ant-form-item-has-error .form-input {
    padding-right: 45px !important;
}

/* ===== PHONE NUMBER HELP SECTION ===== */
.phone-help-section {
    margin-top: 0.5rem;
}

.form-help-text {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.otp-input {
    text-align: center;
    font-size: 1.125rem;
    letter-spacing: 0.1em;
    font-weight: 600;
}

.register-btn {
    width: 100%;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
    margin-top: 0.5rem;
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);
}

.register-btn:active {
    transform: translateY(0);
}

.register-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}



/* ===== FOOTER SECTION ===== */
.register-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.register-footer p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.register-link {
    color: #007BFF;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.register-link:hover {
    color: #0056D2;
    text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .register-container {
        padding: 0.75rem;
        align-items: flex-start;
        padding-top: 1rem;
    }

    .register-card {
        max-width: 100%;
        padding: 1.5rem;
        border-radius: 1rem;
        margin: 0;
        max-height: 95vh;
    }

    .register-logo {
        width: 64px;
        height: 64px;
        margin-bottom: 1rem;
    }

    .verification-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 1rem;
    }

    .verification-icon i {
        font-size: 1.25rem;
    }

    .register-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .register-subtitle {
        font-size: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .register-form .ant-form-item {
        margin-bottom: 1rem;
    }

    .form-input {
        padding: 1rem;
        font-size: 1rem;
        border-radius: 0.5rem;
    }

    .register-btn {
        padding: 1.125rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.5rem;
        width: 100%;
        min-height: 48px; /* Touch-friendly minimum */
        font-weight: 600;
    }

    .register-footer {
        padding-top: 1.25rem;
    }

    .register-footer p {
        font-size: 0.8rem;
    }

    .form-help-text {
        font-size: 0.7rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .register-container {
        padding: 1rem;
    }

    .register-card {
        max-width: 500px;
        padding: 2rem;
    }

    .register-logo {
        width: 72px;
        height: 72px;
    }

    .verification-icon {
        width: 60px;
        height: 60px;
    }

    .register-title {
        font-size: 1.75rem;
    }

    .form-input {
        padding: 0.9375rem 1rem;
        font-size: 0.9375rem;
    }

    .register-btn {
        padding: 1.0625rem 1.5rem;
        font-size: 0.9375rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .register-card {
        max-width: 520px;
        padding: 2.5rem;
    }

    .register-logo {
        width: 84px;
        height: 84px;
    }

    .verification-icon {
        width: 68px;
        height: 68px;
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .register-card {
        max-width: 540px;
        padding: 3rem;
    }

    .register-logo {
        width: 96px;
        height: 96px;
    }

    .verification-icon {
        width: 72px;
        height: 72px;
    }

    .register-title {
        font-size: 2rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .register-container {
        padding: 0.75rem;
        align-items: center;
    }

    .register-card {
        padding: 1.5rem;
        max-height: 85vh;
    }

    .register-logo {
        width: 56px;
        height: 56px;
        margin-bottom: 0.75rem;
    }

    .verification-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0.75rem;
    }

    .register-title {
        font-size: 1.375rem;
        margin-bottom: 0.375rem;
    }

    .register-subtitle {
        margin-bottom: 1.25rem;
    }

    .register-form .ant-form-item {
        margin-bottom: 0.875rem;
    }
}

/* Very Small Screens */
@media (max-width: 320px) {
    .register-container {
        padding: 0.5rem;
    }

    .register-card {
        padding: 1rem;
        border-radius: 0.75rem;
    }

    .register-logo {
        width: 56px;
        height: 56px;
    }

    .verification-icon {
        width: 48px;
        height: 48px;
    }

    .register-title {
        font-size: 1.25rem;
    }

    .form-input {
        padding: 0.875rem;
        font-size: 0.875rem;
    }

    .register-btn {
        padding: 1rem;
        font-size: 0.875rem;
    }
}