const mongoose = require("mongoose");
require("dotenv").config();

async function inspectQuestionData() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");

    // Find a standalone generation with questions
    const standaloneGeneration = await AIQuestionGeneration.findOne({
      examId: null,
      generationStatus: "completed",
      "generatedQuestions.0": { $exists: true }
    });

    if (!standaloneGeneration) {
      console.log("❌ No standalone generation with questions found");
      return;
    }

    console.log(`📋 Inspecting generation: ${standaloneGeneration._id}`);
    console.log(`Questions count: ${standaloneGeneration.generatedQuestions.length}`);

    // Inspect the first question
    const firstQuestion = standaloneGeneration.generatedQuestions[0];
    console.log("\n📝 First question structure:");
    console.log(JSON.stringify(firstQuestion, null, 2));

    // Check if it has generatedContent
    if (firstQuestion.generatedContent) {
      console.log("\n📝 Generated content structure:");
      console.log(JSON.stringify(firstQuestion.generatedContent, null, 2));
    }

    await mongoose.disconnect();
    console.log("\n✅ Inspection completed");

  } catch (error) {
    console.error("❌ Error:", error);
    await mongoose.disconnect();
  }
}

inspectQuestionData();
