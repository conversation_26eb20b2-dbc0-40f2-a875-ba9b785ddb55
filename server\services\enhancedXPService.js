const User = require('../models/userModel');
const Report = require('../models/reportModel');
const XPTransaction = require('../models/xpTransactionModel');

class EnhancedXPService {
  constructor() {
    // Base XP values based on actual performance
    this.baseXPValues = {
      quizCompletion: 10,        // Base XP for completing any quiz
      correctAnswer: 5,          // XP per correct answer
      perfectScore: 25,          // Bonus for 100% score
      firstAttemptPass: 15,      // Bonus for passing on first attempt
      improvementBonus: 10,      // Bonus for improving score on retake
    };

    // Streak multipliers (realistic values)
    this.streakMultipliers = {
      quiz: {
        5: 1.1,    // 10% bonus for 5-quiz streak
        10: 1.2,   // 20% bonus for 10-quiz streak
        20: 1.3,   // 30% bonus for 20-quiz streak
        50: 1.5,   // 50% bonus for 50-quiz streak
      },
      login: {
        7: 1.05,   // 5% bonus for 7-day login streak
        30: 1.1,   // 10% bonus for 30-day login streak
        100: 1.2,  // 20% bonus for 100-day login streak
      },
      perfectScore: {
        3: 1.15,   // 15% bonus for 3 perfect scores in a row
        5: 1.25,   // 25% bonus for 5 perfect scores in a row
        10: 1.4,   // 40% bonus for 10 perfect scores in a row
      }
    };

    // Subject mastery bonuses
    this.masteryBonuses = {
      beginner: 1.0,     // 0-60% average
      intermediate: 1.1, // 60-75% average
      advanced: 1.2,     // 75-85% average
      expert: 1.3,       // 85-95% average
      master: 1.5,       // 95%+ average
    };

    // Consistency bonuses
    this.consistencyBonuses = {
      daily: 5,          // Bonus for daily activity
      weekly: 20,        // Bonus for weekly consistency
      monthly: 50,       // Bonus for monthly consistency
    };
  }

  /**
   * Calculate XP for quiz completion based on real performance data
   */
  async calculateQuizXP({
    userId,
    examData,
    result,
    timeSpent,
    isFirstAttempt = true,
    previousScore = null
  }) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      const { score, verdict, correctAnswers = [], wrongAnswers = [] } = result;
      const totalQuestions = correctAnswers.length + wrongAnswers.length;
      const correctCount = correctAnswers.length;
      const scorePercentage = score || Math.round((correctCount / totalQuestions) * 100);

      // Base XP calculation
      let totalXP = 0;
      let breakdown = {
        baseCompletion: this.baseXPValues.quizCompletion,
        correctAnswers: correctCount * this.baseXPValues.correctAnswer,
        perfectScore: 0,
        firstAttemptBonus: 0,
        improvementBonus: 0,
        streakBonus: 0,
        consistencyBonus: 0,
        masteryBonus: 0,
      };

      // Base XP
      totalXP += breakdown.baseCompletion + breakdown.correctAnswers;

      // Perfect score bonus
      if (scorePercentage === 100) {
        breakdown.perfectScore = this.baseXPValues.perfectScore;
        totalXP += breakdown.perfectScore;
      }

      // First attempt pass bonus
      if (isFirstAttempt && verdict === 'Pass') {
        breakdown.firstAttemptBonus = this.baseXPValues.firstAttemptPass;
        totalXP += breakdown.firstAttemptBonus;
      }

      // Improvement bonus (if retaking and improved)
      if (!isFirstAttempt && previousScore && scorePercentage > previousScore) {
        const improvement = scorePercentage - previousScore;
        breakdown.improvementBonus = Math.round(improvement * 0.5); // 0.5 XP per % improvement
        totalXP += breakdown.improvementBonus;
      }

      // Streak bonuses
      const streakBonus = this.calculateStreakBonus(user);
      breakdown.streakBonus = streakBonus;
      totalXP += streakBonus;

      // Subject mastery bonus
      const masteryBonus = await this.calculateMasteryBonus(userId, examData.subject, scorePercentage);
      breakdown.masteryBonus = masteryBonus;
      totalXP += masteryBonus;

      // Consistency bonus
      const consistencyBonus = await this.calculateConsistencyBonus(userId);
      breakdown.consistencyBonus = consistencyBonus;
      totalXP += consistencyBonus;

      // Apply final multipliers based on streaks
      const finalMultiplier = this.getFinalMultiplier(user);
      const finalXP = Math.round(totalXP * finalMultiplier);

      return {
        xpAwarded: finalXP,
        breakdown: {
          ...breakdown,
          finalMultiplier: finalMultiplier,
          totalBeforeMultiplier: totalXP,
        },
        metadata: {
          scorePercentage,
          verdict,
          correctCount,
          totalQuestions,
          isFirstAttempt,
          subject: examData.subject,
          difficulty: examData.difficulty || 'medium',
        }
      };

    } catch (error) {
      console.error('Error calculating quiz XP:', error);
      throw error;
    }
  }

  /**
   * Calculate streak bonus based on current streaks
   */
  calculateStreakBonus(user) {
    let bonus = 0;
    const activity = user.activityTracking || {};

    // Quiz completion streak bonus
    const quizStreak = activity.quizCompletionStreak || user.currentStreak || 0;
    if (quizStreak >= 5) {
      const multiplier = this.getStreakMultiplier('quiz', quizStreak);
      bonus += Math.round(this.baseXPValues.quizCompletion * (multiplier - 1) * 10);
    }

    // Perfect score streak bonus
    const perfectStreak = activity.perfectScoreStreak || 0;
    if (perfectStreak >= 3) {
      const multiplier = this.getStreakMultiplier('perfectScore', perfectStreak);
      bonus += Math.round(this.baseXPValues.perfectScore * (multiplier - 1));
    }

    // Login streak bonus
    const loginStreak = activity.dailyLoginStreak || 0;
    if (loginStreak >= 7) {
      const multiplier = this.getStreakMultiplier('login', loginStreak);
      bonus += Math.round(this.consistencyBonuses.daily * (multiplier - 1) * 7);
    }

    return bonus;
  }

  /**
   * Calculate subject mastery bonus
   */
  async calculateMasteryBonus(userId, subject, currentScore) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.activityTracking) return 0;

      const subjectPerf = user.activityTracking.subjectPerformance?.find(
        s => s.subject === subject
      );

      if (!subjectPerf || subjectPerf.quizzesTaken < 3) return 0; // Need at least 3 quizzes for mastery

      const avgScore = subjectPerf.averageScore;
      let masteryLevel = 'beginner';

      if (avgScore >= 95) masteryLevel = 'master';
      else if (avgScore >= 85) masteryLevel = 'expert';
      else if (avgScore >= 75) masteryLevel = 'advanced';
      else if (avgScore >= 60) masteryLevel = 'intermediate';

      const multiplier = this.masteryBonuses[masteryLevel];
      return Math.round(currentScore * 0.2 * multiplier); // 20% of score as bonus, multiplied by mastery

    } catch (error) {
      console.error('Error calculating mastery bonus:', error);
      return 0;
    }
  }

  /**
   * Calculate consistency bonus
   */
  async calculateConsistencyBonus(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.activityTracking) return 0;

      let bonus = 0;
      const activity = user.activityTracking;

      // Daily consistency (logged in today)
      const today = new Date().toDateString();
      const lastLogin = new Date(activity.lastLoginDate).toDateString();
      if (today === lastLogin) {
        bonus += this.consistencyBonuses.daily;
      }

      // Weekly consistency (at least 3 quizzes this week)
      if (activity.weeklyStats?.quizzesThisWeek >= 3) {
        bonus += this.consistencyBonuses.weekly;
      }

      // Monthly consistency (at least 10 quizzes this month)
      if (activity.monthlyStats?.quizzesThisMonth >= 10) {
        bonus += this.consistencyBonuses.monthly;
      }

      return bonus;

    } catch (error) {
      console.error('Error calculating consistency bonus:', error);
      return 0;
    }
  }

  /**
   * Get streak multiplier for given type and count
   */
  getStreakMultiplier(type, count) {
    const multipliers = this.streakMultipliers[type];
    if (!multipliers) return 1.0;

    // Find the highest applicable multiplier
    let applicableMultiplier = 1.0;
    for (const [threshold, multiplier] of Object.entries(multipliers)) {
      if (count >= parseInt(threshold)) {
        applicableMultiplier = multiplier;
      }
    }

    return applicableMultiplier;
  }

  /**
   * Get final multiplier based on overall user performance
   */
  getFinalMultiplier(user) {
    let multiplier = 1.0;
    const activity = user.activityTracking || {};

    // High-performing user bonus
    if (user.averageScore >= 90) multiplier += 0.1;
    else if (user.averageScore >= 80) multiplier += 0.05;

    // Long-term engagement bonus
    if (user.totalQuizzesTaken >= 100) multiplier += 0.1;
    else if (user.totalQuizzesTaken >= 50) multiplier += 0.05;

    // Login consistency bonus
    if (activity.dailyLoginStreak >= 30) multiplier += 0.1;
    else if (activity.dailyLoginStreak >= 7) multiplier += 0.05;

    return Math.min(multiplier, 2.0); // Cap at 2x multiplier
  }

  /**
   * Deduct XP for failed quiz or inactivity
   */
  async deductXP({
    userId,
    xpAmount,
    reason,
    sourceId = null,
    sourceModel = null,
    metadata = {}
  }) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      // Ensure we don't go below 0 XP
      const actualDeduction = Math.min(xpAmount, user.totalXP || 0);

      // Update user XP
      user.totalXP = Math.max(0, (user.totalXP || 0) - actualDeduction);

      // Update XP stats
      if (!user.xpStats) user.xpStats = {};
      user.xpStats.lastXPGain = new Date();

      await user.save();

      // Create transaction record
      const transaction = new XPTransaction({
        user: userId,
        xpAmount: -actualDeduction, // Negative for deduction
        transactionType: 'penalty',
        sourceId,
        sourceModel,
        breakdown: {
          penalty: actualDeduction,
          reason: reason
        },
        metadata: {
          ...metadata,
          penaltyReason: reason,
          originalAmount: xpAmount,
          actualDeduction: actualDeduction
        }
      });

      await transaction.save();

      console.log(`❌ Deducted ${actualDeduction} XP from user ${userId} for ${reason}`);

      return {
        success: true,
        xpDeducted: actualDeduction,
        newTotalXP: user.totalXP,
        transaction: transaction._id
      };

    } catch (error) {
      console.error('Error deducting XP:', error);
      throw error;
    }
  }

  /**
   * Award XP to user and update all tracking data
   */
  async awardXP({
    userId,
    xpAmount,
    transactionType,
    sourceId = null,
    sourceModel = null,
    breakdown = {},
    quizData = {},
    metadata = {}
  }) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      // Store user state before transaction
      const userStateBefore = {
        levelBefore: user.currentLevel,
        xpBefore: user.totalXP,
        streakBefore: user.currentStreak,
      };

      // Update user XP
      user.totalXP = (user.totalXP || 0) + xpAmount;
      user.lifetimeXP = (user.lifetimeXP || 0) + xpAmount;
      user.seasonXP = (user.seasonXP || 0) + xpAmount;

      // Update XP stats
      if (!user.xpStats) user.xpStats = {};
      user.xpStats.lastXPGain = new Date();
      user.xpStats.averageXPPerQuiz = user.totalQuizzesTaken > 0 ? 
        Math.round(user.totalXP / user.totalQuizzesTaken) : xpAmount;

      if (xpAmount > (user.xpStats.bestXPGain || 0)) {
        user.xpStats.bestXPGain = xpAmount;
      }

      // Update XP breakdown tracking
      if (transactionType === 'quiz_completion') {
        user.xpStats.xpFromQuizzes = (user.xpStats.xpFromQuizzes || 0) + (breakdown.baseCompletion || 0) + (breakdown.correctAnswers || 0);
        user.xpStats.xpFromStreaks = (user.xpStats.xpFromStreaks || 0) + (breakdown.streakBonus || 0);
        user.xpStats.xpFromConsistency = (user.xpStats.xpFromConsistency || 0) + (breakdown.consistencyBonus || 0);
      }

      // Check for level up
      const levelUpResult = this.checkLevelUp(user);
      if (levelUpResult.levelUp) {
        user.currentLevel = levelUpResult.newLevel;
        user.xpToNextLevel = levelUpResult.xpToNextLevel;
        
        // Add to level history
        if (!user.levelHistory) user.levelHistory = [];
        user.levelHistory.push({
          level: levelUpResult.newLevel,
          reachedAt: new Date(),
          xpAtLevel: user.totalXP
        });
      }

      // Save user using findByIdAndUpdate to avoid validation issues
      await User.findByIdAndUpdate(userId, {
        totalXP: user.totalXP,
        lifetimeXP: user.lifetimeXP,
        seasonXP: user.seasonXP,
        currentLevel: user.currentLevel,
        xpToNextLevel: user.xpToNextLevel,
        xpStats: user.xpStats,
        levelHistory: user.levelHistory
      }, { runValidators: false });

      // Create XP transaction record
      const transaction = new XPTransaction({
        user: userId,
        xpAmount: xpAmount,
        transactionType: transactionType,
        sourceId: sourceId,
        sourceModel: sourceModel,
        breakdown: breakdown,
        quizData: quizData,
        userStateAtTransaction: {
          ...userStateBefore,
          levelAfter: user.currentLevel,
          xpAfter: user.totalXP,
          streakAfter: user.currentStreak,
        },
        season: user.currentSeason,
        metadata: metadata
      });

      await transaction.save();

      return {
        success: true,
        xpAwarded: xpAmount,
        newTotalXP: user.totalXP,
        levelUp: levelUpResult.levelUp,
        newLevel: user.currentLevel,
        xpToNextLevel: user.xpToNextLevel,
        transaction: transaction
      };

    } catch (error) {
      console.error('Error awarding XP:', error);
      throw error;
    }
  }

  /**
   * Check if user should level up
   */
  checkLevelUp(user) {
    const currentLevel = user.currentLevel || 1;
    const currentXP = user.totalXP || 0;
    
    // Progressive XP requirements: Level 1: 100, Level 2: 250, Level 3: 450, etc.
    const getXPRequiredForLevel = (level) => {
      if (level <= 1) return 0;
      return Math.round(100 * level * (level - 1) / 2 + 50 * (level - 1));
    };

    let newLevel = currentLevel;
    while (currentXP >= getXPRequiredForLevel(newLevel + 1)) {
      newLevel++;
    }

    const levelUp = newLevel > currentLevel;
    const xpToNextLevel = getXPRequiredForLevel(newLevel + 1) - currentXP;

    return {
      levelUp,
      newLevel,
      xpToNextLevel: Math.max(0, xpToNextLevel)
    };
  }
}

module.exports = new EnhancedXPService();
