{"name": "st-joseph-kibada-quiz-app", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm i", "xp:init": "node scripts/runXPInitialization.js", "xp:migrate": "node scripts/migrateUsersToXP.js", "xp:test": "node scripts/testXPSystem.js", "xp:setup": "npm run xp:init && npm run xp:migrate && npm run xp:test"}, "author": "", "license": "ISC", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/storage": "^7.14.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "chalk": "^4.1.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.3", "framer-motion": "^12.19.2", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.9.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "openai": "^5.8.2", "pdf-parse": "^1.1.1", "react-modal": "^3.16.3", "sharp": "^0.34.2", "uuid": "^11.0.3"}}