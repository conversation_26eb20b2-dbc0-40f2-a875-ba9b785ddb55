const axios = require('axios');

async function testQuizAPI() {
  try {
    console.log('🔍 Testing Quiz API endpoints...\n');
    
    // Test 1: Get all exams
    console.log('1. Testing get-all-exams endpoint...');
    try {
      const examsResponse = await axios.post('http://localhost:5000/api/exams/get-all-exams');
      console.log('✅ Get all exams successful');
      console.log('📊 Number of exams found:', examsResponse.data.data?.length || 0);
      
      if (examsResponse.data.data && examsResponse.data.data.length > 0) {
        const firstExam = examsResponse.data.data[0];
        console.log('📝 First exam details:');
        console.log('   - ID:', firstExam._id);
        console.log('   - Name:', firstExam.name);
        console.log('   - Level:', firstExam.level);
        console.log('   - Class:', firstExam.class);
        console.log('   - Questions count:', firstExam.questions?.length || 0);
        
        // Test 2: Get specific exam by ID
        console.log('\n2. Testing get-exam-by-id endpoint...');
        try {
          const examResponse = await axios.post('http://localhost:5000/api/exams/get-exam-by-id', {
            examId: firstExam._id
          });
          console.log('✅ Get exam by ID successful');
          console.log('📝 Exam loaded with', examResponse.data.data.questions?.length || 0, 'questions');
          
          if (examResponse.data.data.questions && examResponse.data.data.questions.length > 0) {
            const firstQuestion = examResponse.data.data.questions[0];
            console.log('🔍 First question preview:');
            console.log('   - Type:', firstQuestion.type || firstQuestion.answerType);
            console.log('   - Has options:', !!firstQuestion.options);
            console.log('   - Correct answer:', firstQuestion.correctAnswer || firstQuestion.correctOption);
          }
          
        } catch (examError) {
          console.log('❌ Error getting specific exam:', examError.response?.data?.message || examError.message);
        }
        
      } else {
        console.log('⚠️ No exams found in database');
        console.log('💡 You may need to create some exams first');
      }
      
    } catch (examsError) {
      console.log('❌ Error getting exams:', examsError.response?.data?.message || examsError.message);
    }
    
    // Test 3: Check server health
    console.log('\n3. Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server health check successful');
      console.log('📊 Server status:', healthResponse.data);
    } catch (healthError) {
      console.log('❌ Server health check failed:', healthError.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testQuizAPI();
