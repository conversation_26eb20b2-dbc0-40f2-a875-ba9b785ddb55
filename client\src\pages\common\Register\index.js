import { Form, message, Input, Select, Divider } from "antd";
import React, { useState } from "react";
import "./index.css";
import { Link, useNavigate } from "react-router-dom";
import { registerUser, sendOTP, registerWithGoogle } from "../../../apicalls/users";
import Logo from "../../../assets/logo.png";
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';

const { Option } = Select;

function Register() {
  const [verification, setVerification] = useState(false);
  const [data, setData] = useState("");
  const [otp, setOTP] = useState("");
  const [loading, setLoading] = useState(false);
  const [schoolType, setSchoolType] = useState("");
  const navigate = useNavigate();

  const onFinish = async (values) => {
    try {
      const response = await registerUser(values);
      if (response.success) {
        message.success(response.message);
        navigate("/login");
      } else {
        message.error(response.message);
        setVerification(false);
      }
    } catch (error) {
      message.error(error.message);
      setVerification(false);
    }
  };

  const verifyUser = async (values) => {
    if (values.otp === otp) {
      onFinish(data);
    } else {
      message.error("Invalid OTP");
    }
  };

  const generateOTP = async (formData) => {
    if (!formData.name || !formData.email || !formData.password) {
      message.error("Please fill all fields!");
      return;
    }
    setLoading(true);
    try {
      const response = await sendOTP(formData);
      if (response.success) {
        message.success(response.message);
        setData(formData);
        setOTP(response.data);
        setVerification(true);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    setLoading(false);
  };

  // Handle Google OAuth success
  const handleGoogleSuccess = async (credentialResponse) => {
    setLoading(true);
    try {
      const response = await registerWithGoogle({
        credential: credentialResponse.credential
      });

      if (response.success) {
        message.success("Registration successful with Google!");
        navigate("/login");
      } else {
        message.error(response.message || "Google registration failed");
      }
    } catch (error) {
      console.error("Google registration error:", error);
      message.error("Failed to register with Google. Please try again.");
    }
    setLoading(false);
  };

  // Handle Google OAuth error
  const handleGoogleError = () => {
    message.error("Google registration was cancelled or failed");
  };

  return (
    <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID || "your-google-client-id"}>
      <div className="register-container">
        <div className="register-card">
        {verification ? (
          <div>
            <div className="register-header">
              <div className="verification-icon">
                <i className="ri-shield-check-line"></i>
              </div>
              <h1 className="register-title">Verify Your Email</h1>
              <p className="register-subtitle">Enter the OTP sent to your email</p>
            </div>

            <Form layout="vertical" onFinish={verifyUser} className="register-form">
              <Form.Item name="otp" label="OTP Code" rules={[{ required: true, message: "Please enter the OTP!" }]}>
                <Input
                  type="number"
                  className="form-input otp-input"
                  placeholder="Enter 6-digit OTP"
                  maxLength={6}
                />
              </Form.Item>

              <button type="submit" className="register-btn">
                Verify & Complete Registration
              </button>
            </Form>
          </div>
        ) : (
          <div>
            <div className="register-header">
              <img src={Logo} alt="BrainWave Logo" className="register-logo" />
              <h1 className="register-title">Create Account</h1>
              <p className="register-subtitle">Join thousands of students learning with BrainWave</p>
            </div>

            {/* Google OAuth Section - Temporarily disabled for testing */}
            {process.env.REACT_APP_GOOGLE_CLIENT_ID && process.env.REACT_APP_GOOGLE_CLIENT_ID !== 'your-google-client-id-here' && (
              <div className="google-auth-section">
                <GoogleLogin
                  onSuccess={handleGoogleSuccess}
                  onError={handleGoogleError}
                  useOneTap={false}
                  theme="outline"
                  size="large"
                  text="continue_with"
                  shape="rectangular"
                  logo_alignment="left"
                  width="100%"
                />

                <Divider className="auth-divider">
                  <span className="divider-text">or continue with email</span>
                </Divider>
              </div>
            )}

            <Form layout="vertical" onFinish={generateOTP} className="register-form">
              <Form.Item name="name" label="Full Name" rules={[{ required: true, message: "Please enter your name!" }]}>
                <Input
                  type="text"
                  className="form-input"
                  placeholder="Enter your full name"
                  autoComplete="name"
                />
              </Form.Item>

              <Form.Item name="school" label="School" rules={[{ required: true, message: "Please enter your school!" }]}>
                <Input
                  type="text"
                  className="form-input"
                  placeholder="Enter your school name"
                  autoComplete="organization"
                />
              </Form.Item>

              <Form.Item name="level" label="Education Level" rules={[{ required: true, message: "Please select your level!" }]}>
                <Select
                  onChange={(value) => setSchoolType(value)}
                  className="form-input"
                  placeholder="Select Education Level"
                >
                  <Option value="Primary">Primary Education</Option>
                  <Option value="Secondary">Secondary Education</Option>
                  <Option value="Advance">Advanced Level</Option>
                </Select>
              </Form.Item>

              <Form.Item name="class" label="Class" rules={[{ required: true, message: "Please select your class!" }]}>
                <Select className="form-input" placeholder="Select Your Class">
                  {schoolType === "Primary" && [1, 2, 3, 4, 5, 6, 7].map((i) => (
                    <Option key={i} value={i}>{`Class ${i}`}</Option>
                  ))}
                  {schoolType === "Secondary" && [1, 2, 3, 4].map((i) => (
                    <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>
                  ))}
                  {schoolType === "Advance" && [5, 6].map((i) => (
                    <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="email"
                label="Email Address"
                rules={[
                  { required: true, message: "Please enter your email!" },
                  { type: "email", message: "Please enter a valid email address!" },
                  {
                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    message: "Please enter a properly formatted email address!"
                  }
                ]}
              >
                <Input
                  type="email"
                  className="form-input"
                  placeholder="Enter your email address (e.g., <EMAIL>)"
                  autoComplete="email"
                />
                <p className="form-help-text">We'll send important updates to this email</p>
              </Form.Item>

              <Form.Item
                name="phoneNumber"
                label="Phone Number"
                rules={[
                  { required: true, message: "Please enter your phone number!" },
                  { pattern: /^\d{10}$/, message: "Phone number must be exactly 10 digits!" },
                ]}
              >
                <Input
                  type="tel"
                  maxLength={10}
                  className="form-input"
                  placeholder="Enter 10-digit phone number"
                  autoComplete="tel"
                />
                <p className="form-help-text">Used for payment verification</p>
              </Form.Item>

              <Form.Item
                name="password"
                label="Password"
                rules={[
                  { required: true, message: "Please enter your password!" },
                  { min: 8, message: "Password must be at least 8 characters long!" },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                    message: "Password must contain uppercase, lowercase, number and special character!"
                  }
                ]}
              >
                <Input.Password
                  className="form-input"
                  placeholder="Create a strong password (min 8 characters)"
                  autoComplete="new-password"
                />
                <p className="form-help-text">Must include: uppercase, lowercase, number, and special character</p>
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="Retype Password"
                dependencies={['password']}
                rules={[
                  { required: true, message: "Please retype your password!" },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('The two passwords do not match!'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  className="form-input"
                  placeholder="Retype your password to confirm"
                  autoComplete="new-password"
                />
                <p className="form-help-text">Must match the password above</p>
              </Form.Item>

              <Form.Item>
                <button type="submit" className="register-btn" disabled={loading}>
                  {loading ? "Creating Account..." : "Create Account"}
                </button>
              </Form.Item>
            </Form>

            <div className="register-footer">
              <p>
                Already have an account? {" "}
                <Link to="/login" className="register-link">
                  Sign In
                </Link>
              </p>
            </div>
          </div>
        )}
        </div>
      </div>
    </GoogleOAuthProvider>
  );
}

export default Register;
