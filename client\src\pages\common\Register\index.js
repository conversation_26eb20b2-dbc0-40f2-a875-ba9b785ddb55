import { Form, message, Input, Select } from "antd";
import React, { useState } from "react";
import "./index.css";
import { Link, useNavigate } from "react-router-dom";
import { registerUser, sendOTP } from "../../../apicalls/users";
import Logo from "../../../assets/logo.png";

const { Option } = Select;

function Register() {
  const [verification, setVerification] = useState(false);
  const [data, setData] = useState("");
  const [otp, setOTP] = useState("");
  const [loading, setLoading] = useState(false);
  const [schoolType, setSchoolType] = useState("");
  const [formValues, setFormValues] = useState({});
  const navigate = useNavigate();

  // Phone number validation helper
  const validatePhoneNumber = (phone) => {
    const phoneRegex = /^0[67]\d{8}$/;
    return phoneRegex.test(phone);
  };

  const onFinish = async (values) => {
    try {
      const response = await registerUser(values);
      if (response.success) {
        message.success({
          content: response.message,
          duration: 6,
          style: { marginTop: '20px' }
        });
        // Add a small delay to let user see the success message
        setTimeout(() => {
          navigate("/login");
        }, 1500);
      } else {
        showUserFriendlyError({ response }, "Registration failed");
        setVerification(false);
      }
    } catch (error) {
      console.error("Registration error:", error);
      showUserFriendlyError(error, "Registration failed. Please try again.");
      setVerification(false);
    }
  };

  const verifyUser = async (values) => {
    if (!values.otp?.trim()) {
      message.error("🔢 Please enter the verification code");
      return;
    }

    if (values.otp.length !== 6) {
      message.error("🔢 Verification code must be 6 digits");
      return;
    }

    if (values.otp === otp) {
      message.loading("✅ Verifying your code...", 1);
      setTimeout(() => {
        onFinish(data);
      }, 1000);
    } else {
      message.error({
        content: "❌ The verification code is incorrect. Please check your email and try again.",
        duration: 5,
        style: { marginTop: '20px' }
      });
    }
  };

  // Enhanced error handling function
  const showUserFriendlyError = (error, defaultMessage) => {
    const errorMessage = error.response?.data?.message || error.message || defaultMessage;
    const errorType = error.response?.data?.errorType;

    // Show different message styles based on error type
    if (errorType === "EMAIL_EXISTS" || errorType === "PHONE_EXISTS") {
      message.warning({
        content: errorMessage,
        duration: 6,
        style: { marginTop: '20px' }
      });
    } else if (errorType === "INVALID_EMAIL" || errorType === "INVALID_PHONE") {
      message.error({
        content: errorMessage,
        duration: 5,
        style: { marginTop: '20px' }
      });
    } else {
      message.error({
        content: errorMessage,
        duration: 4,
        style: { marginTop: '20px' }
      });
    }
  };

  const generateOTP = async (formData) => {
    console.log("🚀 Form submitted with data:", formData);

    // Enhanced validation with user-friendly messages
    if (!formData.name?.trim()) {
      message.error("👤 Please enter your full name");
      return;
    }
    if (!formData.school?.trim()) {
      message.error("🏫 Please enter your school name");
      return;
    }
    if (!formData.level?.trim()) {
      message.error("🎓 Please select your education level");
      return;
    }
    if (!formData.class) {
      message.error("📝 Please select your class/form");
      return;
    }
    if (!formData.email?.trim()) {
      message.error("📧 Please enter your email address");
      return;
    }
    if (!formData.phoneNumber?.trim()) {
      message.error("📱 Please enter your phone number");
      return;
    }
    if (!formData.password?.trim()) {
      message.error("🔒 Please create a password");
      return;
    }
    if (!formData.confirmPassword?.trim()) {
      message.error("🔒 Please confirm your password");
      return;
    }
    if (formData.password !== formData.confirmPassword) {
      message.error("🔒 Passwords do not match. Please check and try again.");
      return;
    }

    console.log("✅ All validations passed, proceeding with OTP generation...");

    setLoading(true);
    try {
      const response = await sendOTP(formData);
      if (response.success) {
        message.success({
          content: response.message,
          duration: 5,
          style: { marginTop: '20px' }
        });
        setData(formData);
        setOTP(response.data);
        setVerification(true);
      } else {
        showUserFriendlyError({ response }, "Failed to send verification code");
      }
    } catch (error) {
      console.error("OTP generation error:", error);
      showUserFriendlyError(error, "Something went wrong. Please check your information and try again.");
    }
    setLoading(false);
  };



  return (
    <div className="register-container">
      <div className="register-card">
        {verification ? (
          <div>
            <div className="register-header">
              <img src={Logo} alt="BrainWave Logo" className="register-logo" />
              <h1 className="register-title">Verify Your Email</h1>
              <p className="register-subtitle">We've sent a verification code to your email</p>
            </div>

            {/* OTP Instructions */}
            <div className="otp-instructions">
              <div className="otp-info-card">
                <h3 className="otp-info-title">📧 Check Your Email</h3>
                <p className="otp-info-text">
                  We've sent a <strong>6-digit verification code</strong> to your email address.
                </p>
                <div className="otp-steps">
                  <div className="otp-step">
                    <span className="step-number">1</span>
                    <span className="step-text">Open your email app or website</span>
                  </div>
                  <div className="otp-step">
                    <span className="step-number">2</span>
                    <span className="step-text">Look for an email from BrainWave</span>
                  </div>
                  <div className="otp-step">
                    <span className="step-number">3</span>
                    <span className="step-text">Copy the 6-digit code from the email</span>
                  </div>
                  <div className="otp-step">
                    <span className="step-number">4</span>
                    <span className="step-text">Enter the code in the box below</span>
                  </div>
                </div>
                <div className="otp-help">
                  <p className="help-text">
                    <strong>💡 Can't find the email?</strong> Check your spam/junk folder or wait a few minutes for delivery.
                  </p>
                </div>
              </div>
            </div>

            <Form layout="vertical" onFinish={verifyUser} className="register-form">
              <Form.Item name="otp" label="Verification Code" rules={[{ required: true, message: "Please enter the OTP!" }]}>
                <Input
                  type="number"
                  className="form-input otp-input"
                  placeholder="Enter 6-digit code (e.g., 123456)"
                  maxLength={6}
                />
                <p className="form-help-text">Enter the verification code from your email</p>
              </Form.Item>

              <button type="submit" className="register-btn" disabled={loading}>
                {loading ? "⏳ Verifying..." : "✅ Verify & Complete Registration"}
              </button>

              <div className="resend-section">
                <p className="resend-text">Didn't receive the code?</p>
                <button
                  type="button"
                  className="resend-btn"
                  onClick={() => generateOTP(data)}
                  disabled={loading}
                >
                  📧 Resend Verification Code
                </button>
              </div>
            </Form>
          </div>
        ) : (
          <div>
            <div className="register-header">
              <img src={Logo} alt="BrainWave Logo" className="register-logo" />
              <h1 className="register-title">Create Account</h1>
              <p className="register-subtitle">Join thousands of students learning with BrainWave</p>
            </div>



            <Form
              layout="vertical"
              onFinish={generateOTP}
              className="register-form"
              onValuesChange={(changedValues, allValues) => {
                console.log("📝 Form values changed:", allValues);
                setFormValues(allValues);
              }}
            >


              <Form.Item
                name="name"
                label="Full Name"
                hasFeedback
                rules={[
                  { required: true, message: "👤 Please enter your full name" },
                  { min: 2, message: "👤 Name must be at least 2 characters long" },
                  { max: 50, message: "👤 Name must be less than 50 characters" },
                  {
                    pattern: /^[a-zA-Z\s]+$/,
                    message: "👤 Name should only contain letters and spaces"
                  }
                ]}
              >
                <Input
                  type="text"
                  className="form-input"
                  placeholder="Enter your full name (e.g., John Doe)"
                  autoComplete="name"
                />
                <p className="form-help-text">Enter your first and last name as you'd like it to appear</p>
              </Form.Item>

              <Form.Item
                name="school"
                label="School Name"
                hasFeedback
                rules={[
                  { required: true, message: "🏫 Please enter your school name" },
                  { min: 3, message: "🏫 School name must be at least 3 characters long" },
                  { max: 100, message: "🏫 School name must be less than 100 characters" }
                ]}
              >
                <Input
                  type="text"
                  className="form-input"
                  placeholder="Enter your school name (e.g., Dar es Salaam Secondary School)"
                  autoComplete="organization"
                />
                <p className="form-help-text">Enter the full name of your current school</p>
              </Form.Item>

              <Form.Item
                name="level"
                label="Education Level"
                hasFeedback
                rules={[{ required: true, message: "🎓 Please select your education level" }]}
              >
                <Select
                  onChange={(value) => setSchoolType(value)}
                  className="form-input"
                  placeholder="Choose your current education level"
                >
                  <Option value="Primary">🎒 Primary Education (Classes 1-7)</Option>
                  <Option value="Secondary">📚 Secondary Education (Forms 1-4)</Option>
                  <Option value="Advance">🎓 Advanced Level (Forms 5-6)</Option>
                </Select>
                <p className="form-help-text">Select the education level you are currently studying</p>
              </Form.Item>

              <Form.Item
                name="class"
                label="Class/Form"
                hasFeedback
                rules={[{ required: true, message: "📝 Please select your class or form" }]}
              >
                <Select
                  className="form-input"
                  placeholder={schoolType ? "Select your class/form" : "Please select education level first"}
                  disabled={!schoolType}
                >
                  {schoolType === "Primary" && [1, 2, 3, 4, 5, 6, 7].map((i) => (
                    <Option key={i} value={i}>{`📚 Class ${i}`}</Option>
                  ))}
                  {schoolType === "Secondary" && [1, 2, 3, 4].map((i) => (
                    <Option key={i} value={`Form-${i}`}>{`📖 Form ${i}`}</Option>
                  ))}
                  {schoolType === "Advance" && [5, 6].map((i) => (
                    <Option key={i} value={`Form-${i}`}>{`🎓 Form ${i}`}</Option>
                  ))}
                </Select>
                <p className="form-help-text">
                  {!schoolType && "Please select your education level first"}
                  {schoolType === "Primary" && "Select your current class (1-7)"}
                  {schoolType === "Secondary" && "Select your current form (1-4)"}
                  {schoolType === "Advance" && "Select your current form (5-6)"}
                </p>
              </Form.Item>

              <Form.Item
                name="email"
                label="Email Address"
                hasFeedback
                rules={[
                  { required: true, message: "📧 Please enter your email!" },
                  { type: "email", message: "📧 Please enter a valid email address!" },
                  {
                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    message: "📧 Please enter a properly formatted email address!"
                  }
                ]}
              >
                <Input
                  type="email"
                  className="form-input"
                  placeholder="Enter your email address (e.g., <EMAIL>)"
                  autoComplete="email"
                />
                <p className="form-help-text">We'll send important updates to this email</p>
              </Form.Item>

              <Form.Item
                name="phoneNumber"
                label="Phone Number"
                hasFeedback
                rules={[
                  { required: true, message: "📱 Please enter your phone number" },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve();
                      if (validatePhoneNumber(value)) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error("📱 Phone number must start with 06 or 07 and be 10 digits (e.g., 0712345678)"));
                    }
                  },
                ]}
              >
                <Input
                  type="tel"
                  className="form-input"
                  placeholder="Enter mobile number (e.g., 0712345678 or 0612345678)"
                  autoComplete="tel"
                  maxLength={10}
                />
                <div className="phone-help-section">
                  <p className="form-help-text">💳 Enter your mobile number for payment processing (M-Pesa, Tigo Pesa, Airtel Money)</p>
                  <div className="phone-examples">
                    <p className="phone-example-title">✅ Valid formats:</p>
                    <div className="phone-examples-grid">
                      <span className="phone-example">0712345678</span>
                      <span className="phone-example">0612345678</span>
                    </div>
                    <p className="phone-note">📞 Must start with <strong>06</strong> or <strong>07</strong> and be exactly 10 digits</p>
                    <p className="phone-note">📧 Note: Email verification code will be sent to your email, not phone</p>
                  </div>
                </div>
              </Form.Item>

              <Form.Item
                name="password"
                label="Password"
                hasFeedback
                rules={[
                  { required: true, message: "🔒 Please enter your password!" },
                  { min: 8, message: "🔒 Password must be at least 8 characters long!" },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                    message: "🔒 Password must contain uppercase, lowercase, number and special character!"
                  }
                ]}
              >
                <Input.Password
                  className="form-input"
                  placeholder="Create a strong password (min 8 characters)"
                  autoComplete="new-password"
                />
                <p className="form-help-text">Must include: uppercase, lowercase, number, and special character</p>
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="Retype Password"
                hasFeedback
                dependencies={['password']}
                rules={[
                  { required: true, message: "🔒 Please retype your password!" },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('🔒 The two passwords do not match!'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  className="form-input"
                  placeholder="Retype your password to confirm"
                  autoComplete="new-password"
                />
                <p className="form-help-text">Must match the password above</p>
              </Form.Item>

              <Form.Item>
                <button
                  type="submit"
                  className="register-btn"
                  disabled={loading}
                  onClick={() => console.log("🔘 Create Account button clicked")}
                >
                  {loading ? (
                    <>
                      <span className="loading-spinner"></span>
                      Creating Account...
                    </>
                  ) : (
                    <>
                      🚀 Create Account
                    </>
                  )}
                </button>
              </Form.Item>
            </Form>

            <div className="register-footer">
              <p>
                Already have an account? {" "}
                <Link to="/login" className="register-link">
                  Sign In
                </Link>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Register;
