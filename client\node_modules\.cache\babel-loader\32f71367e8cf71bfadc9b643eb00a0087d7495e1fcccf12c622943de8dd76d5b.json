{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbTrophy, TbCheck, TbX, TbClock, TbBrain, TbArrowRight, TbStar, TbUsers, TbBook, TbMessageCircle, TbChartBar, TbSettings } from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport \"./TrialQuiz.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizResult = ({\n  result,\n  onTryAnother,\n  onRegister\n}) => {\n  _s();\n  var _result$questionResul;\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n  const [showFailAnimation, setShowFailAnimation] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: {\n            y: 0.6\n          }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Enhanced failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setShowFailAnimation(true);\n\n        // Play failure sound\n        try {\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          // Create a descending failure sound\n          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);\n          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);\n          oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);\n          gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 1);\n        } catch (error) {\n          console.log('Failure sound not available');\n        }\n        setTimeout(() => {\n          setShowFlash(false);\n          setShowFailAnimation(false);\n        }, 800);\n      }, 1000);\n    }\n  }, [result.percentage]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n  const getPerformanceMessage = percentage => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n  const premiumFeatures = [{\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Access comprehensive study materials, notes, and resources\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with other students and track your progress\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\"\n  }, {\n    icon: TbUsers,\n    title: \"Unlimited Quizzes\",\n    description: \"Take as many quizzes as you want across all subjects\"\n  }, {\n    icon: TbStar,\n    title: \"Progress Tracking\",\n    description: \"Detailed analytics and performance insights\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"trial-background\",\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)',\n      position: 'relative'\n    },\n    children: [showFlash && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flash-animation ${result.percentage >= 60 ? 'flash-success' : 'flash-failure'}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"trial-container\",\n      style: {\n        position: 'relative',\n        zIndex: 10,\n        padding: 'clamp(16px, 4vw, 40px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        className: \"text-center mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.3,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: `inline-flex items-center justify-center rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg ${showFailAnimation ? 'trial-fail-shake' : ''}`,\n          style: {\n            width: 'clamp(64px, 15vw, 112px)',\n            height: 'clamp(64px, 15vw, 112px)'\n          },\n          onAnimationComplete: () => setAnimationComplete(true),\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"text-white\",\n            style: {\n              width: 'clamp(32px, 8vw, 56px)',\n              height: 'clamp(32px, 8vw, 56px)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          className: `font-bold text-gray-900 mb-4 ${showFailAnimation ? 'trial-fail-glow' : ''}`,\n          style: {\n            fontSize: 'clamp(24px, 6vw, 48px)'\n          },\n          children: \"Quiz Complete! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.8\n          },\n          className: `inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`,\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-xl sm:text-2xl font-bold ${performance.color}`,\n            children: performance.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.6,\n          delay: 1.0\n        },\n        className: \"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                duration: 0.8,\n                delay: 1.2,\n                type: \"spring\"\n              },\n              className: `font-bold text-white mb-2 ${showFailAnimation ? 'trial-fail-bounce' : ''}`,\n              style: {\n                fontSize: 'clamp(48px, 12vw, 96px)'\n              },\n              children: [result.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/90 text-lg sm:text-xl\",\n              children: \"Your Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\",\n            children: [{\n              label: \"Total Questions\",\n              value: result.totalQuestions,\n              icon: TbBook,\n              color: \"blue\",\n              delay: 1.4\n            }, {\n              label: \"Correct Answers\",\n              value: result.correctAnswers,\n              icon: TbCheck,\n              color: \"green\",\n              delay: 1.6\n            }, {\n              label: \"Wrong Answers\",\n              value: result.wrongAnswers,\n              icon: TbX,\n              color: \"red\",\n              delay: 1.8\n            }, {\n              label: \"Time Taken\",\n              value: formatTime(result.timeSpent),\n              icon: TbClock,\n              color: \"purple\",\n              delay: 2.0\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: stat.delay\n              },\n              className: `p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: `w-6 h-6 text-${stat.color}-600`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`,\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.2\n            },\n            className: \"text-center mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${isPassed ? 'bg-green-100 text-green-700 border-2 border-green-200' : 'bg-red-100 text-red-700 border-2 border-red-200'}`,\n              children: [isPassed ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.4\n            },\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetails(!showDetails),\n              className: \"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: showDetails ? 'Hide Question Summary' : 'View Question Summary'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 2.6\n        },\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\",\n            whileHover: {\n              scale: 1.05,\n              y: -2\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 2.8\n        },\n        className: \"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl sm:text-3xl font-bold mb-2\",\n            children: \"\\uD83D\\uDD13 Unlock These Amazing Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Join thousands of students already excelling with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 3.0 + 0.1 * index\n              },\n              className: \"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-gray-800\",\n                  children: feature.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center text-blue-600 font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-5 h-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"Premium Feature\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 3.4\n            },\n            className: \"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-6 h-6 mr-2 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), \"Advanced Quiz Features & Maximum Control\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 3.6\n                },\n                className: \"bg-white rounded-xl p-5 shadow-sm border border-purple-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(TbBook, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-bold text-gray-800\",\n                    children: \"Multiple Subject Selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-gray-600 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Choose from \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"15+ subjects\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 41\n                      }, this), \" across all levels\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Mix and match subjects in custom quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Subject-specific performance tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Cross-subject comparison analytics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 3.8\n                },\n                className: \"bg-white rounded-xl p-5 shadow-sm border border-blue-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(TbSettings, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-bold text-gray-800\",\n                    children: \"Maximum Quiz Control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-gray-600 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Set custom \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"time limits\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 40\n                      }, this), \" (5-180 minutes)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose question count (5-100 questions)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Select difficulty levels (Easy, Medium, Hard)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Pause and resume quiz sessions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 4.0\n              },\n              className: \"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-bold text-gray-800 mb-4 text-center\",\n                children: \"\\uD83D\\uDE80 Advanced Quiz Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [{\n                  icon: \"⏱️\",\n                  title: \"Smart Timer\",\n                  desc: \"Adaptive timing\"\n                }, {\n                  icon: \"🎯\",\n                  title: \"Targeted Practice\",\n                  desc: \"Weak area focus\"\n                }, {\n                  icon: \"📊\",\n                  title: \"Live Analytics\",\n                  desc: \"Real-time insights\"\n                }, {\n                  icon: \"🏆\",\n                  title: \"Achievement System\",\n                  desc: \"Unlock rewards\"\n                }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    duration: 0.3,\n                    delay: 4.2 + 0.1 * index\n                  },\n                  className: \"text-center p-3 bg-white rounded-lg shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xl mb-1\",\n                    children: feature.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-xs text-gray-800\",\n                    children: feature.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: feature.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 3.6\n            },\n            className: \"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n              children: \"\\uD83C\\uDFAF Why Students Choose BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n              children: [{\n                icon: \"🚀\",\n                title: \"Instant Access\",\n                desc: \"Start learning immediately\"\n              }, {\n                icon: \"📱\",\n                title: \"Mobile Friendly\",\n                desc: \"Study anywhere, anytime\"\n              }, {\n                icon: \"🎓\",\n                title: \"Expert Content\",\n                desc: \"Created by top educators\"\n              }, {\n                icon: \"🏆\",\n                title: \"Proven Results\",\n                desc: \"98% success rate\"\n              }].map((benefit, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.4,\n                  delay: 3.8 + 0.1 * index\n                },\n                className: \"text-center p-4 bg-white rounded-xl shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl mb-2\",\n                  children: benefit.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-gray-800 mb-1\",\n                  children: benefit.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: benefit.desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 4.2\n            },\n            className: \"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold mb-2\",\n              children: \"Ready to Excel? \\uD83C\\uDF1F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 mb-4\",\n              children: \"Join BrainWave today and unlock your full potential!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-3 justify-center items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Create Free Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-200 text-sm\",\n                children: \"\\u2728 No credit card required \\u2022 Start immediately\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: \"auto\"\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-800 mb-4\",\n          children: \"Question Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: (_result$questionResul = result.questionResults) === null || _result$questionResul === void 0 ? void 0 : _result$questionResul.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg border-2 ${q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center ${q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                children: q.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 38\n                }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800 mb-2\",\n                  children: q.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Your answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`,\n                      children: q.userAnswer || 'Not answered'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 25\n                  }, this), !q.isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Correct answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 font-medium text-green-600\",\n                      children: q.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onTryAnother,\n          className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n          children: \"Try Another Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",\n            children: \"Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\",\n          children: \"\\uD83C\\uDFAF Trial Mode - Register for unlimited access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizResult, \"Iq8oqC1KXd0V/mWq3xNjG7JY8qE=\");\n_c = TrialQuizResult;\nexport default TrialQuizResult;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "TbTrophy", "TbCheck", "TbX", "TbClock", "TbBrain", "TbArrowRight", "TbStar", "TbUsers", "TbBook", "TbMessageCircle", "TbChartBar", "TbSettings", "confetti", "jsxDEV", "_jsxDEV", "TrialQuizResult", "result", "onTryAnother", "onRegister", "_s", "_result$questionResul", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "showFlash", "setShowFlash", "showFailAnimation", "setShowFailAnimation", "isPassed", "percentage", "setTimeout", "particleCount", "spread", "origin", "y", "audio", "Audio", "volume", "play", "catch", "error", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getPerformanceMessage", "message", "color", "bg", "gradient", "performance", "premiumFeatures", "icon", "title", "description", "className", "style", "minHeight", "background", "position", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "zIndex", "padding", "div", "initial", "opacity", "animate", "transition", "duration", "ease", "scale", "delay", "type", "stiffness", "width", "height", "onAnimationComplete", "h1", "fontSize", "split", "label", "value", "totalQuestions", "correctAnswers", "wrongAnswers", "timeSpent", "map", "stat", "index", "onClick", "to", "button", "whileHover", "whileTap", "feature", "x", "desc", "benefit", "questionResults", "q", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport {\n  TbTrophy,\n  TbCheck,\n  TbX,\n  TbClock,\n  TbBrain,\n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar,\n  TbSettings\n} from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n  const [showFailAnimation, setShowFailAnimation] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: { y: 0.6 }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Enhanced failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setShowFailAnimation(true);\n\n        // Play failure sound\n        try {\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          // Create a descending failure sound\n          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);\n          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);\n          oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);\n\n          gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);\n\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 1);\n        } catch (error) {\n          console.log('Failure sound not available');\n        }\n\n        setTimeout(() => {\n          setShowFlash(false);\n          setShowFailAnimation(false);\n        }, 800);\n      }, 1000);\n    }\n  }, [result.percentage]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div className=\"trial-background\" style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)',\n      position: 'relative'\n    }}>\n      {/* Flash Animation Overlay */}\n      {showFlash && (\n        <div\n          className={`flash-animation ${\n            result.percentage >= 60 ? 'flash-success' : 'flash-failure'\n          }`}\n        />\n      )}\n\n      <div className=\"trial-container\" style={{\n        position: 'relative',\n        zIndex: 10,\n        padding: 'clamp(16px, 4vw, 40px)'\n      }}>\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg ${showFailAnimation ? 'trial-fail-shake' : ''}`}\n            style={{\n              width: 'clamp(64px, 15vw, 112px)',\n              height: 'clamp(64px, 15vw, 112px)'\n            }}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy\n              className=\"text-white\"\n              style={{\n                width: 'clamp(32px, 8vw, 56px)',\n                height: 'clamp(32px, 8vw, 56px)'\n              }}\n            />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className={`font-bold text-gray-900 mb-4 ${showFailAnimation ? 'trial-fail-glow' : ''}`}\n            style={{ fontSize: 'clamp(24px, 6vw, 48px)' }}\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className={`font-bold text-white mb-2 ${showFailAnimation ? 'trial-fail-bounce' : ''}`}\n                style={{ fontSize: 'clamp(48px, 12vw, 96px)' }}\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-blue-600 font-medium\">\n                    <TbStar className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Premium Feature</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Better Quiz Features */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.4 }}\n              className=\"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\">\n                <TbBrain className=\"w-6 h-6 mr-2 text-purple-600\" />\n                Advanced Quiz Features & Maximum Control\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Multiple Subject Selection */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.6 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-purple-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbBook className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Multiple Subject Selection</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Mix and match subjects in custom quizzes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Subject-specific performance tracking</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Cross-subject comparison analytics</span>\n                    </li>\n                  </ul>\n                </motion.div>\n\n                {/* Maximum Control */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.8 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-blue-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbSettings className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Maximum Quiz Control</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Choose question count (5-100 questions)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Select difficulty levels (Easy, Medium, Hard)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Pause and resume quiz sessions</span>\n                    </li>\n                  </ul>\n                </motion.div>\n              </div>\n\n              {/* Advanced Features */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 4.0 }}\n                className=\"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\"\n              >\n                <h5 className=\"font-bold text-gray-800 mb-4 text-center\">🚀 Advanced Quiz Features</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {[\n                    { icon: \"⏱️\", title: \"Smart Timer\", desc: \"Adaptive timing\" },\n                    { icon: \"🎯\", title: \"Targeted Practice\", desc: \"Weak area focus\" },\n                    { icon: \"📊\", title: \"Live Analytics\", desc: \"Real-time insights\" },\n                    { icon: \"🏆\", title: \"Achievement System\", desc: \"Unlock rewards\" }\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}\n                      className=\"text-center p-3 bg-white rounded-lg shadow-sm\"\n                    >\n                      <div className=\"text-xl mb-1\">{feature.icon}</div>\n                      <div className=\"font-semibold text-xs text-gray-800\">{feature.title}</div>\n                      <div className=\"text-xs text-gray-600\">{feature.desc}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            </motion.div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,EACfC,UAAU,EACVC,UAAU,QACL,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgC,QAAQ,GAAGb,MAAM,CAACc,UAAU,IAAI,EAAE;IAExC,IAAID,QAAQ,EAAE;MACZ;MACAE,UAAU,CAAC,MAAM;QACfnB,QAAQ,CAAC;UACPoB,aAAa,EAAE,GAAG;UAClBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAI;QACnB,CAAC,CAAC;;QAEF;QACA,IAAI;UACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,kBAAkB,CAAC;UAC3CD,KAAK,CAACE,MAAM,GAAG,GAAG;UAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACpC;;QAEA;QACAjB,YAAY,CAAC,IAAI,CAAC;QAClBK,UAAU,CAAC,MAAML,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL;MACAK,UAAU,CAAC,MAAM;QACf;QACAL,YAAY,CAAC,IAAI,CAAC;QAClBE,oBAAoB,CAAC,IAAI,CAAC;;QAE1B;QACA,IAAI;UACF,MAAMgB,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAC7E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;UAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,CAAC,CAAC;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAACR,YAAY,CAACS,WAAW,CAAC;;UAE1C;UACAL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC;UAClER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;UACtFR,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAEb,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;UAEpFN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,EAAEX,YAAY,CAACY,WAAW,CAAC;UAC5DN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAEb,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;UAE9ER,UAAU,CAACW,KAAK,CAACf,YAAY,CAACY,WAAW,CAAC;UAC1CR,UAAU,CAACY,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;QAEAZ,UAAU,CAAC,MAAM;UACfL,YAAY,CAAC,KAAK,CAAC;UACnBE,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACZ,MAAM,CAACc,UAAU,CAAC,CAAC;EAEvB,MAAM+B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,KAAIG,gBAAiB,GAAE;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAIrC,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE,gBAAgB;MACvBC,EAAE,EAAE,aAAa;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,eAAe;MACxBC,KAAK,EAAE,eAAe;MACtBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,cAAc;MACvBC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE,eAAe;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAGL,qBAAqB,CAACnD,MAAM,CAACc,UAAU,CAAC;EAC5D,MAAMD,QAAQ,GAAGb,MAAM,CAACc,UAAU,IAAI,EAAE;EAExC,MAAM2C,eAAe,GAAG,CACtB;IACEC,IAAI,EAAElE,MAAM;IACZmE,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEtE,OAAO;IACbuE,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhE,UAAU;IAChBiE,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEjE,eAAe;IACrBkE,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEnE,OAAO;IACboE,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEpE,MAAM;IACZqE,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE9D,OAAA;IAAK+D,SAAS,EAAC,kBAAkB;IAACC,KAAK,EAAE;MACvCC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,GAECzD,SAAS,iBACRX,OAAA;MACE+D,SAAS,EAAG,mBACV7D,MAAM,CAACc,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,eAC7C;IAAE;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACF,eAEDxE,OAAA;MAAK+D,SAAS,EAAC,iBAAiB;MAACC,KAAK,EAAE;QACtCG,QAAQ,EAAE,UAAU;QACpBM,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE;MACX,CAAE;MAAAN,QAAA,gBAEApE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCyD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAE,CAAE;QAC9B0D,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAC/ClB,SAAS,EAAC,2BAA2B;QAAAK,QAAA,gBAErCpE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;UACTC,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBJ,OAAO,EAAE;YAAEI,KAAK,EAAE;UAAE,CAAE;UACtBH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE,GAAG;YAAEC,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC1EtB,SAAS,EAAG,yEAAwEL,WAAW,CAACD,QAAS,mBAAkB5C,iBAAiB,GAAG,kBAAkB,GAAG,EAAG,EAAE;UACzKmD,KAAK,EAAE;YACLsB,KAAK,EAAE,0BAA0B;YACjCC,MAAM,EAAE;UACV,CAAE;UACFC,mBAAmB,EAAEA,CAAA,KAAM9E,oBAAoB,CAAC,IAAI,CAAE;UAAA0D,QAAA,eAEtDpE,OAAA,CAACd,QAAQ;YACP6E,SAAS,EAAC,YAAY;YACtBC,KAAK,EAAE;cACLsB,KAAK,EAAE,wBAAwB;cAC/BC,MAAM,EAAE;YACV;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbxE,OAAA,CAAChB,MAAM,CAACyG,EAAE;UACRb,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CpB,SAAS,EAAG,gCAA+BlD,iBAAiB,GAAG,iBAAiB,GAAG,EAAG,EAAE;UACxFmD,KAAK,EAAE;YAAE0B,QAAQ,EAAE;UAAyB,CAAE;UAAAtB,QAAA,EAC/C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAExD,CAAC,EAAE;UAAG,CAAE;UAC/ByD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAExD,CAAC,EAAE;UAAE,CAAE;UAC9B0D,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CpB,SAAS,EAAG,uCAAsCL,WAAW,CAACF,EAAG,oBAAmBE,WAAW,CAACH,KAAK,CAACoC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM;UAAAvB,QAAA,eAE1HpE,OAAA;YAAG+D,SAAS,EAAG,iCAAgCL,WAAW,CAACH,KAAM,EAAE;YAAAa,QAAA,EAChEV,WAAW,CAACJ;UAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,KAAK,EAAE;QAAI,CAAE;QACpCJ,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEK,KAAK,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,sFAAsF;QAAAK,QAAA,gBAGhGpE,OAAA;UAAK+D,SAAS,EAAG,oBAAmBL,WAAW,CAACD,QAAS,qCAAqC;UAAAW,QAAA,eAC5FpE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAK,QAAA,gBAC1BpE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;cACTC,OAAO,EAAE;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACtBJ,OAAO,EAAE;gBAAEI,KAAK,EAAE;cAAE,CAAE;cACtBH,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC1DrB,SAAS,EAAG,6BAA4BlD,iBAAiB,GAAG,mBAAmB,GAAG,EAAG,EAAE;cACvFmD,KAAK,EAAE;gBAAE0B,QAAQ,EAAE;cAA0B,CAAE;cAAAtB,QAAA,GAE9ClE,MAAM,CAACc,UAAU,EAAC,GACrB;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA;cAAK+D,SAAS,EAAC,kCAAkC;cAAAK,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAK+D,SAAS,EAAC,qCAAqC;UAAAK,QAAA,gBAElDpE,OAAA;YAAK+D,SAAS,EAAC,qDAAqD;YAAAK,QAAA,EACjE,CACC;cACEwB,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAE3F,MAAM,CAAC4F,cAAc;cAC5BlC,IAAI,EAAElE,MAAM;cACZ6D,KAAK,EAAE,MAAM;cACb4B,KAAK,EAAE;YACT,CAAC,EACD;cACES,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAE3F,MAAM,CAAC6F,cAAc;cAC5BnC,IAAI,EAAEzE,OAAO;cACboE,KAAK,EAAE,OAAO;cACd4B,KAAK,EAAE;YACT,CAAC,EACD;cACES,KAAK,EAAE,eAAe;cACtBC,KAAK,EAAE3F,MAAM,CAAC8F,YAAY;cAC1BpC,IAAI,EAAExE,GAAG;cACTmE,KAAK,EAAE,KAAK;cACZ4B,KAAK,EAAE;YACT,CAAC,EACD;cACES,KAAK,EAAE,YAAY;cACnBC,KAAK,EAAE9C,UAAU,CAAC7C,MAAM,CAAC+F,SAAS,CAAC;cACnCrC,IAAI,EAAEvE,OAAO;cACbkE,KAAK,EAAE,QAAQ;cACf4B,KAAK,EAAE;YACT,CAAC,CACF,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBpG,OAAA,CAAChB,MAAM,CAAC2F,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAG,CAAE;cAC/ByD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAE,CAAE;cAC9B0D,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAEgB,IAAI,CAAChB;cAAM,CAAE;cACjDpB,SAAS,EAAG,iBAAgBoC,IAAI,CAAC5C,KAAM,iCAAgC4C,IAAI,CAAC5C,KAAM,kBAAkB;cAAAa,QAAA,gBAEpGpE,OAAA;gBAAK+D,SAAS,EAAG,6BAA4BoC,IAAI,CAAC5C,KAAM,kDAAkD;gBAAAa,QAAA,eACxGpE,OAAA,CAACmG,IAAI,CAACvC,IAAI;kBAACG,SAAS,EAAG,gBAAeoC,IAAI,CAAC5C,KAAM;gBAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNxE,OAAA;gBAAK+D,SAAS,EAAG,uCAAsCoC,IAAI,CAAC5C,KAAM,WAAW;gBAAAa,QAAA,EAC1E+B,IAAI,CAACN;cAAK;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACNxE,OAAA;gBAAK+D,SAAS,EAAC,mCAAmC;gBAAAK,QAAA,EAC/C+B,IAAI,CAACP;cAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAdD4B,KAAK;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEK,KAAK,EAAE;YAAI,CAAE;YACpCJ,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEK,KAAK,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,kBAAkB;YAAAK,QAAA,eAE5BpE,OAAA;cAAK+D,SAAS,EAAG,kFACfhD,QAAQ,GACJ,uDAAuD,GACvD,iDACL,EAAE;cAAAqD,QAAA,GACArD,QAAQ,gBACPf,OAAA,CAACb,OAAO;gBAAC4E,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/BxE,OAAA,CAACZ,GAAG;gBAAC2E,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC3B,eACDxE,OAAA;gBAAAoE,QAAA,EACGrD,QAAQ,GAAG,iCAAiC,GAAG;cAAsC;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,aAAa;YAAAK,QAAA,eAEvBpE,OAAA;cACEqG,OAAO,EAAEA,CAAA,KAAM7F,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CwD,SAAS,EAAC,6IAA6I;cAAAK,QAAA,gBAEvJpE,OAAA,CAACJ,UAAU;gBAACmE,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCxE,OAAA;gBAAAoE,QAAA,EAAO7D,WAAW,GAAG,uBAAuB,GAAG;cAAuB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAG,CAAE;QAC/ByD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAE,CAAE;QAC9B0D,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,kBAAkB;QAAAK,QAAA,eAE5BpE,OAAA,CAACf,IAAI;UAACqH,EAAE,EAAC,WAAW;UAAAlC,QAAA,eAClBpE,OAAA,CAAChB,MAAM,CAACuH,MAAM;YACZxC,SAAS,EAAC,mOAAmO;YAC7OyC,UAAU,EAAE;cAAEtB,KAAK,EAAE,IAAI;cAAE7D,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCoF,QAAQ,EAAE;cAAEvB,KAAK,EAAE;YAAK,CAAE;YAAAd,QAAA,gBAE1BpE,OAAA;cAAAoE,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBxE,OAAA,CAACT,YAAY;cAACwE,SAAS,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAG,CAAE;QAC/ByD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAE,CAAE;QAC9B0D,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,4EAA4E;QAAAK,QAAA,gBAGtFpE,OAAA;UAAK+D,SAAS,EAAC,uFAAuF;UAAAK,QAAA,gBACpGpE,OAAA;YAAI+D,SAAS,EAAC,qCAAqC;YAAAK,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxE,OAAA;YAAG+D,SAAS,EAAC,uBAAuB;YAAAK,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNxE,OAAA;UAAK+D,SAAS,EAAC,YAAY;UAAAK,QAAA,gBACzBpE,OAAA;YAAK+D,SAAS,EAAC,sDAAsD;YAAAK,QAAA,EAClET,eAAe,CAACuC,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAClCpG,OAAA,CAAChB,MAAM,CAAC2F,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAG,CAAE;cAC/ByD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAE,CAAE;cAC9B0D,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGiB;cAAO,CAAE;cAC1DrC,SAAS,EAAC,sIAAsI;cAAAK,QAAA,gBAEhJpE,OAAA;gBAAK+D,SAAS,EAAC,kCAAkC;gBAAAK,QAAA,gBAC/CpE,OAAA;kBAAK+D,SAAS,EAAC,4JAA4J;kBAAAK,QAAA,eACzKpE,OAAA,CAAC0G,OAAO,CAAC9C,IAAI;oBAACG,SAAS,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNxE,OAAA;kBAAI+D,SAAS,EAAC,iCAAiC;kBAAAK,QAAA,EAAEsC,OAAO,CAAC7C;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNxE,OAAA;gBAAG+D,SAAS,EAAC,+BAA+B;gBAAAK,QAAA,EAAEsC,OAAO,CAAC5C;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtExE,OAAA;gBAAK+D,SAAS,EAAC,kDAAkD;gBAAAK,QAAA,gBAC/DpE,OAAA,CAACR,MAAM;kBAACuE,SAAS,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCxE,OAAA;kBAAM+D,SAAS,EAAC,SAAS;kBAAAK,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA,GAhBD4B,KAAK;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAG,CAAE;YAC/ByD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAE,CAAE;YAC9B0D,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,0FAA0F;YAAAK,QAAA,gBAEpGpE,OAAA;cAAI+D,SAAS,EAAC,mFAAmF;cAAAK,QAAA,gBAC/FpE,OAAA,CAACV,OAAO;gBAACyE,SAAS,EAAC;cAA8B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4CAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAK+D,SAAS,EAAC,uCAAuC;cAAAK,QAAA,gBAEpDpE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAE8B,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChC7B,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAE8B,CAAC,EAAE;gBAAE,CAAE;gBAC9B5B,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC1CpB,SAAS,EAAC,4DAA4D;gBAAAK,QAAA,gBAEtEpE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAK,QAAA,gBACrCpE,OAAA;oBAAK+D,SAAS,EAAC,yGAAyG;oBAAAK,QAAA,eACtHpE,OAAA,CAACN,MAAM;sBAACqE,SAAS,EAAC;oBAAoB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNxE,OAAA;oBAAI+D,SAAS,EAAC,yBAAyB;oBAAAK,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACNxE,OAAA;kBAAI+D,SAAS,EAAC,iCAAiC;kBAAAK,QAAA,gBAC7CpE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAoE,QAAA,GAAM,cAAY,eAAApE,OAAA;wBAAAoE,QAAA,EAAQ;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,sBAAkB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAAwC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAAqC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAE8B,CAAC,EAAE;gBAAG,CAAE;gBAC/B7B,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAE8B,CAAC,EAAE;gBAAE,CAAE;gBAC9B5B,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC1CpB,SAAS,EAAC,0DAA0D;gBAAAK,QAAA,gBAEpEpE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAK,QAAA,gBACrCpE,OAAA;oBAAK+D,SAAS,EAAC,wGAAwG;oBAAAK,QAAA,eACrHpE,OAAA,CAACH,UAAU;sBAACkE,SAAS,EAAC;oBAAoB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNxE,OAAA;oBAAI+D,SAAS,EAAC,yBAAyB;oBAAAK,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACNxE,OAAA;kBAAI+D,SAAS,EAAC,iCAAiC;kBAAAK,QAAA,gBAC7CpE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,oBAAoB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAoE,QAAA,GAAM,aAAW,eAAApE,OAAA;wBAAAoE,QAAA,EAAQ;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,oBAAgB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,oBAAoB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAAuC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,oBAAoB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAA6C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLxE,OAAA;oBAAI+D,SAAS,EAAC,kBAAkB;oBAAAK,QAAA,gBAC9BpE,OAAA;sBAAM+D,SAAS,EAAC,oBAAoB;sBAAAK,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAoE,QAAA,EAAM;oBAA8B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAG,CAAE;cAC/ByD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAExD,CAAC,EAAE;cAAE,CAAE;cAC9B0D,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CpB,SAAS,EAAC,iEAAiE;cAAAK,QAAA,gBAE3EpE,OAAA;gBAAI+D,SAAS,EAAC,0CAA0C;gBAAAK,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvFxE,OAAA;gBAAK+D,SAAS,EAAC,uCAAuC;gBAAAK,QAAA,EACnD,CACC;kBAAER,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,aAAa;kBAAE+C,IAAI,EAAE;gBAAkB,CAAC,EAC7D;kBAAEhD,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,mBAAmB;kBAAE+C,IAAI,EAAE;gBAAkB,CAAC,EACnE;kBAAEhD,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,gBAAgB;kBAAE+C,IAAI,EAAE;gBAAqB,CAAC,EACnE;kBAAEhD,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,oBAAoB;kBAAE+C,IAAI,EAAE;gBAAiB,CAAC,CACpE,CAACV,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBACnBpG,OAAA,CAAChB,MAAM,CAAC2F,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAI,CAAE;kBACpCJ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAE,CAAE;kBAClCH,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGiB;kBAAO,CAAE;kBAC1DrC,SAAS,EAAC,+CAA+C;kBAAAK,QAAA,gBAEzDpE,OAAA;oBAAK+D,SAAS,EAAC,cAAc;oBAAAK,QAAA,EAAEsC,OAAO,CAAC9C;kBAAI;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDxE,OAAA;oBAAK+D,SAAS,EAAC,qCAAqC;oBAAAK,QAAA,EAAEsC,OAAO,CAAC7C;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1ExE,OAAA;oBAAK+D,SAAS,EAAC,uBAAuB;oBAAAK,QAAA,EAAEsC,OAAO,CAACE;kBAAI;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GARtD4B,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAG,CAAE;YAC/ByD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAE,CAAE;YAC9B0D,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,wFAAwF;YAAAK,QAAA,gBAElGpE,OAAA;cAAI+D,SAAS,EAAC,kDAAkD;cAAAK,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAK+D,SAAS,EAAC,sDAAsD;cAAAK,QAAA,EAClE,CACC;gBAAER,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE+C,IAAI,EAAE;cAA6B,CAAC,EAC3E;gBAAEhD,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,iBAAiB;gBAAE+C,IAAI,EAAE;cAA0B,CAAC,EACzE;gBAAEhD,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE+C,IAAI,EAAE;cAA2B,CAAC,EACzE;gBAAEhD,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE+C,IAAI,EAAE;cAAmB,CAAC,CAClE,CAACV,GAAG,CAAC,CAACW,OAAO,EAAET,KAAK,kBACnBpG,OAAA,CAAChB,MAAM,CAAC2F,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEK,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBAClCH,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGiB;gBAAO,CAAE;gBAC1DrC,SAAS,EAAC,+CAA+C;gBAAAK,QAAA,gBAEzDpE,OAAA;kBAAK+D,SAAS,EAAC,eAAe;kBAAAK,QAAA,EAAEyC,OAAO,CAACjD;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDxE,OAAA;kBAAK+D,SAAS,EAAC,kCAAkC;kBAAAK,QAAA,EAAEyC,OAAO,CAAChD;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvExE,OAAA;kBAAK+D,SAAS,EAAC,uBAAuB;kBAAAK,QAAA,EAAEyC,OAAO,CAACD;gBAAI;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GARtD4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAG,CAAE;YAC/ByD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAExD,CAAC,EAAE;YAAE,CAAE;YAC9B0D,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,0FAA0F;YAAAK,QAAA,gBAEpGpE,OAAA;cAAI+D,SAAS,EAAC,wBAAwB;cAAAK,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DxE,OAAA;cAAG+D,SAAS,EAAC,oBAAoB;cAAAK,QAAA,EAAC;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1FxE,OAAA;cAAK+D,SAAS,EAAC,6DAA6D;cAAAK,QAAA,gBAC1EpE,OAAA,CAACf,IAAI;gBAACqH,EAAE,EAAC,WAAW;gBAAAlC,QAAA,eAClBpE,OAAA,CAAChB,MAAM,CAACuH,MAAM;kBACZxC,SAAS,EAAC,6HAA6H;kBACvIyC,UAAU,EAAE;oBAAEtB,KAAK,EAAE;kBAAK,CAAE;kBAC5BuB,QAAQ,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAAAd,QAAA,gBAE1BpE,OAAA;oBAAAoE,QAAA,EAAM;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCxE,OAAA,CAACT,YAAY;oBAACwE,SAAS,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPxE,OAAA;gBAAK+D,SAAS,EAAC,uBAAuB;gBAAAK,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZjE,WAAW,iBACVP,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEU,MAAM,EAAE;QAAE,CAAE;QACnCT,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEU,MAAM,EAAE;QAAO,CAAE;QACxCR,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BjB,SAAS,EAAC,yCAAyC;QAAAK,QAAA,gBAEnDpE,OAAA;UAAI+D,SAAS,EAAC,sCAAsC;UAAAK,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzExE,OAAA;UAAK+D,SAAS,EAAC,WAAW;UAAAK,QAAA,GAAA9D,qBAAA,GACvBJ,MAAM,CAAC4G,eAAe,cAAAxG,qBAAA,uBAAtBA,qBAAA,CAAwB4F,GAAG,CAAC,CAACa,CAAC,EAAEX,KAAK,kBACpCpG,OAAA;YAAiB+D,SAAS,EAAG,2BAC3BgD,CAAC,CAACC,SAAS,GAAG,8BAA8B,GAAG,0BAChD,EAAE;YAAA5C,QAAA,eACDpE,OAAA;cAAK+D,SAAS,EAAC,4BAA4B;cAAAK,QAAA,gBACzCpE,OAAA;gBAAK+D,SAAS,EAAG,yDACfgD,CAAC,CAACC,SAAS,GAAG,yBAAyB,GAAG,uBAC3C,EAAE;gBAAA5C,QAAA,EACA2C,CAAC,CAACC,SAAS,gBAAGhH,OAAA,CAACb,OAAO;kBAAC4E,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACZ,GAAG;kBAAC2E,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNxE,OAAA;gBAAK+D,SAAS,EAAC,QAAQ;gBAAAK,QAAA,gBACrBpE,OAAA;kBAAG+D,SAAS,EAAC,gCAAgC;kBAAAK,QAAA,EAAE2C,CAAC,CAACE;gBAAQ;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DxE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAK,QAAA,gBAChCpE,OAAA;oBAAAoE,QAAA,gBACEpE,OAAA;sBAAM+D,SAAS,EAAC,eAAe;sBAAAK,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnDxE,OAAA;sBAAM+D,SAAS,EAAG,oBAAmBgD,CAAC,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;sBAAA5C,QAAA,EACpF2C,CAAC,CAACG,UAAU,IAAI;oBAAc;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACH,CAACuC,CAAC,CAACC,SAAS,iBACXhH,OAAA;oBAAAoE,QAAA,gBACEpE,OAAA;sBAAM+D,SAAS,EAAC,eAAe;sBAAAK,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtDxE,OAAA;sBAAM+D,SAAS,EAAC,iCAAiC;sBAAAK,QAAA,EAAE2C,CAAC,CAACI;oBAAa;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA1BE4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAKDxE,OAAA,CAAChB,MAAM,CAAC2F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAG,CAAE;QAC/ByD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAExD,CAAC,EAAE;QAAE,CAAE;QAC9B0D,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,gDAAgD;QAAAK,QAAA,gBAE1DpE,OAAA;UACEqG,OAAO,EAAElG,YAAa;UACtB4D,SAAS,EAAC,0GAA0G;UAAAK,QAAA,EACrH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETxE,OAAA,CAACf,IAAI;UAACqH,EAAE,EAAC,GAAG;UAAAlC,QAAA,eACVpE,OAAA;YAAQ+D,SAAS,EAAC,6FAA6F;YAAAK,QAAA,EAAC;UAEhH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbxE,OAAA;QAAK+D,SAAS,EAAC,kBAAkB;QAAAK,QAAA,eAC/BpE,OAAA;UAAM+D,SAAS,EAAC,uFAAuF;UAAAK,QAAA,EAAC;QAExG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAxoBIJ,eAAe;AAAAmH,EAAA,GAAfnH,eAAe;AA0oBrB,eAAeA,eAAe;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}