{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizPlay = ({\n  quizData,\n  onComplete,\n  onBack\n}) => {\n  _s();\n  const {\n    exam,\n    trialUserInfo\n  } = quizData;\n  const questions = exam.questions || [];\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n  const goToQuestion = index => {\n    setCurrentQuestionIndex(index);\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"No Questions Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz doesn't have any questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.type) || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.answerType) || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) === 'object' && (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.options) !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionA, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionB, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionC, currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.optionD].filter(Boolean);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-xl border-b-2 border-blue-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onBack,\n              className: \"p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-w-0 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate\",\n                children: exam.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base text-gray-600\",\n                  children: exam.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm sm:text-base font-medium text-blue-600\",\n                  children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 sm:space-x-4 flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center space-x-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl shadow-xl border-2 transition-all duration-300 ${timeLeft <= 60 ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50 animate-pulse' : timeLeft <= 300 ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50' : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'}`,\n                style: {\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                  className: \"w-5 h-5 sm:w-6 sm:h-6 drop-shadow-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-lg sm:text-xl drop-shadow-md\",\n                  children: formatTime(timeLeft)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), timeLeft <= 300 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 border-2 border-yellow-300 rounded-xl animate-ping opacity-75\",\n                style: {\n                  animation: 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:hidden mt-3 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [Math.round((currentQuestionIndex + 1) / questions.length * 100), \"% Complete\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-3\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500\",\n                initial: {\n                  width: 0\n                },\n                animate: {\n                  width: `${(currentQuestionIndex + 1) / questions.length * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-1\",\n            children: questions.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => goToQuestion(index),\n              className: `w-3 h-3 rounded-full transition-all duration-200 ${index === currentQuestionIndex ? 'bg-blue-600 scale-125' : selectedAnswers[questions[index]._id] ? 'bg-green-500' : 'bg-gray-300 hover:bg-gray-400'}`,\n              title: `Question ${index + 1}`\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        transition: {\n          duration: 0.4,\n          ease: \"easeOut\"\n        },\n        className: \"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-6 sm:px-8 lg:px-10 py-6 sm:py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg sm:text-xl font-bold text-white\",\n                  children: currentQuestionIndex + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-white/90 text-sm sm:text-base\",\n                  children: \"Question\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-white font-medium text-lg sm:text-xl\",\n                  children: [currentQuestionIndex + 1, \" of \", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/90 text-sm\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-sm sm:text-base font-medium px-3 py-1 rounded-full ${selectedAnswers[currentQuestion._id] ? 'bg-green-500/20 text-green-100 border border-green-400/30' : 'bg-yellow-500/20 text-yellow-100 border border-yellow-400/30'}`,\n                children: selectedAnswers[currentQuestion._id] ? '✓ Answered' : '○ Pending'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 sm:px-8 lg:px-10 py-8 sm:py-10 lg:py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8 sm:mb-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-relaxed mb-6\",\n              children: currentQuestion.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), currentQuestion.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: currentQuestion.image,\n                  alt: \"Question\",\n                  className: \"max-w-full h-auto rounded-xl shadow-sm mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-10 sm:mb-12\",\n            children: isMCQ && questionOptions.length > 0 ?\n            /*#__PURE__*/\n            // Multiple Choice Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4 sm:space-y-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800 mb-6\",\n                children: \"Choose your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), questionOptions.map((option, index) => {\n                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(currentQuestion._id, optionLetter),\n                  className: `w-full p-5 sm:p-6 text-left rounded-2xl border-2 transition-all duration-300 ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-lg scale-[1.02]' : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'}`,\n                  whileHover: {\n                    scale: isSelected ? 1.02 : 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 sm:space-x-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 flex items-center justify-center font-bold text-lg ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 43\n                      }, this) : optionLetter\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex-1 text-base sm:text-lg font-medium leading-relaxed\",\n                      children: option\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Fill-in-the-blank / Free Text Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800\",\n                children: \"Type your answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: selectedAnswers[currentQuestion._id] || '',\n                  onChange: e => handleAnswerSelect(currentQuestion._id, e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-5 sm:p-6 border-2 border-gray-300 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-base sm:text-lg font-medium bg-gray-50 focus:bg-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-8 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToPrevious,\n              disabled: currentQuestionIndex === 0,\n              className: `flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 hover:shadow-md'}`,\n              whileHover: currentQuestionIndex > 0 ? {\n                scale: 1.02\n              } : {},\n              whileTap: currentQuestionIndex > 0 ? {\n                scale: 0.98\n              } : {},\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-base sm:text-lg\",\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: handleSubmitQuiz,\n              disabled: isSubmitting,\n              className: `flex items-center space-x-3 px-8 sm:px-10 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${isSubmitting ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl'}`,\n              whileHover: !isSubmitting ? {\n                scale: 1.02\n              } : {},\n              whileTap: !isSubmitting ? {\n                scale: 0.98\n              } : {},\n              children: [isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-base sm:text-lg\",\n                children: isSubmitting ? 'Submitting...' : 'Submit Quiz'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: goToNext,\n              className: \"flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl\",\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-base sm:text-lg\",\n                children: \"Next Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, currentQuestionIndex, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\",\n      children: \"Trial Mode\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizPlay, \"poqwUhvGWiexGFrQc9xn7WhRFI8=\");\n_c = TrialQuizPlay;\nexport default TrialQuizPlay;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "TbArrowLeft", "TbArrowRight", "TbClock", "TbCheck", "message", "submitTrialResult", "jsxDEV", "_jsxDEV", "TrialQuizPlay", "quizData", "onComplete", "onBack", "_s", "exam", "trialUserInfo", "questions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedAnswer<PERSON>", "setSelectedAnswers", "timeLeft", "setTimeLeft", "duration", "isSubmitting", "setIsSubmitting", "startTime", "Date", "now", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "questionId", "answer", "timeSpent", "round", "response", "examId", "_id", "answers", "success", "data", "error", "console", "goToNext", "length", "goToPrevious", "goToQuestion", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "isLastQuestion", "answeredQuestions", "Object", "keys", "questionType", "type", "answerType", "isMCQ", "isFillBlank", "questionOptions", "Array", "isArray", "options", "values", "optionA", "optionB", "optionC", "optionD", "filter", "Boolean", "name", "subject", "style", "textShadow", "animation", "div", "initial", "width", "animate", "map", "_", "title", "opacity", "y", "exit", "transition", "ease", "image", "src", "alt", "option", "optionLetter", "String", "fromCharCode", "isSelected", "button", "whileHover", "scale", "whileTap", "value", "onChange", "e", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n      \n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion?.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Enhanced Header with Progress */}\n      <div className=\"bg-white shadow-xl border-b-2 border-blue-200\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n              <button\n                onClick={onBack}\n                className=\"p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md\"\n              >\n                <TbArrowLeft className=\"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800\" />\n              </button>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate\">{exam.name}</h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-sm sm:text-base text-gray-600\">{exam.subject}</span>\n                  <span className=\"text-gray-400\">•</span>\n                  <span className=\"text-sm sm:text-base font-medium text-blue-600\">\n                    Question {currentQuestionIndex + 1} of {questions.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-3 sm:space-x-4 flex-shrink-0\">\n              {/* Enhanced Timer with Professional Styling */}\n              <div className=\"relative\">\n                <div className={`flex items-center space-x-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl shadow-xl border-2 transition-all duration-300 ${\n                  timeLeft <= 60\n                    ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50 animate-pulse'\n                    : timeLeft <= 300\n                    ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50'\n                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'\n                }`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>\n                  <TbClock className=\"w-5 h-5 sm:w-6 sm:h-6 drop-shadow-md\" />\n                  <span className=\"font-bold text-lg sm:text-xl drop-shadow-md\">{formatTime(timeLeft)}</span>\n                </div>\n                {/* Warning animation for low time */}\n                {timeLeft <= 300 && (\n                  <div\n                    className=\"absolute inset-0 border-2 border-yellow-300 rounded-xl animate-ping opacity-75\"\n                    style={{ animation: 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite' }}\n                  ></div>\n                )}\n              </div>\n\n              <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {answeredQuestions}/{questions.length} answered\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile progress indicator */}\n          <div className=\"sm:hidden mt-3 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\">\n              <span className=\"text-sm font-medium text-gray-700\">\n                {answeredQuestions}/{questions.length} answered\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Progress</span>\n                <span className=\"text-sm text-gray-500\">\n                  {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}% Complete\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                <motion.div\n                  className=\"bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500\"\n                  initial={{ width: 0 }}\n                  animate={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}\n                />\n              </div>\n            </div>\n\n            {/* Quick navigation dots */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n              {questions.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => goToQuestion(index)}\n                  className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                    index === currentQuestionIndex\n                      ? 'bg-blue-600 scale-125'\n                      : selectedAnswers[questions[index]._id]\n                      ? 'bg-green-500'\n                      : 'bg-gray-300 hover:bg-gray-400'\n                  }`}\n                  title={`Question ${index + 1}`}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\">\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden\"\n        >\n          {/* Question Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-6 sm:px-8 lg:px-10 py-6 sm:py-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n                  <span className=\"text-lg sm:text-xl font-bold text-white\">\n                    {currentQuestionIndex + 1}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"text-white/90 text-sm sm:text-base\">Question</div>\n                  <div className=\"text-white font-medium text-lg sm:text-xl\">\n                    {currentQuestionIndex + 1} of {questions.length}\n                  </div>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-white/90 text-sm\">Status</div>\n                <div className={`text-sm sm:text-base font-medium px-3 py-1 rounded-full ${\n                  selectedAnswers[currentQuestion._id]\n                    ? 'bg-green-500/20 text-green-100 border border-green-400/30'\n                    : 'bg-yellow-500/20 text-yellow-100 border border-yellow-400/30'\n                }`}>\n                  {selectedAnswers[currentQuestion._id] ? '✓ Answered' : '○ Pending'}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10 lg:py-12\">\n            <div className=\"mb-8 sm:mb-10\">\n              <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-relaxed mb-6\">\n                {currentQuestion.name}\n              </h2>\n\n              {/* Question Image (if exists) */}\n              {currentQuestion.image && (\n                <div className=\"mb-8\">\n                  <div className=\"bg-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-200\">\n                    <img\n                      src={currentQuestion.image}\n                      alt=\"Question\"\n                      className=\"max-w-full h-auto rounded-xl shadow-sm mx-auto\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Answer Section */}\n            <div className=\"mb-10 sm:mb-12\">\n              {isMCQ && questionOptions.length > 0 ? (\n                // Multiple Choice Questions\n                <div className=\"space-y-4 sm:space-y-5\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800 mb-6\">\n                    Choose your answer:\n                  </h3>\n                  {questionOptions.map((option, index) => {\n                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n\n                    return (\n                      <motion.button\n                        key={index}\n                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                        className={`w-full p-5 sm:p-6 text-left rounded-2xl border-2 transition-all duration-300 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-lg scale-[1.02]'\n                            : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'\n                        }`}\n                        whileHover={{ scale: isSelected ? 1.02 : 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-center space-x-4 sm:space-x-5\">\n                          <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 flex items-center justify-center font-bold text-lg ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6\" /> : optionLetter}\n                          </div>\n                          <span className=\"flex-1 text-base sm:text-lg font-medium leading-relaxed\">\n                            {option}\n                          </span>\n                        </div>\n                      </motion.button>\n                    );\n                  })}\n                </div>\n              ) : (\n                // Fill-in-the-blank / Free Text Questions\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800\">\n                    Type your answer:\n                  </h3>\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-5 sm:p-6 border-2 border-gray-300 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-base sm:text-lg font-medium bg-gray-50 focus:bg-white\"\n                    />\n                    <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                      ✏️\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex items-center justify-between pt-8 border-t border-gray-200\">\n              <motion.button\n                onClick={goToPrevious}\n                disabled={currentQuestionIndex === 0}\n                className={`flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${\n                  currentQuestionIndex === 0\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 hover:shadow-md'\n                }`}\n                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}\n                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}\n              >\n                <TbArrowLeft className=\"w-5 h-5\" />\n                <span className=\"text-base sm:text-lg\">Previous</span>\n              </motion.button>\n\n              {isLastQuestion ? (\n                <motion.button\n                  onClick={handleSubmitQuiz}\n                  disabled={isSubmitting}\n                  className={`flex items-center space-x-3 px-8 sm:px-10 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${\n                    isSubmitting\n                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                      : 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl'\n                  }`}\n                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n                >\n                  {isSubmitting ? (\n                    <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                  ) : (\n                    <TbCheck className=\"w-5 h-5\" />\n                  )}\n                  <span className=\"text-base sm:text-lg\">\n                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\n                  </span>\n                </motion.button>\n              ) : (\n                <motion.button\n                  onClick={goToNext}\n                  className=\"flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span className=\"text-base sm:text-lg\">Next Question</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\">\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iBAAiB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAGL,QAAQ;EACxC,MAAMM,SAAS,GAAGF,IAAI,CAACE,SAAS,IAAI,EAAE;EAEtC,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,CAACiB,IAAI,CAACS,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,SAAS,CAAC,GAAG7B,QAAQ,CAAC8B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAExC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIuB,QAAQ,IAAI,CAAC,EAAE;MACjBQ,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAACU,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMa,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDxB,kBAAkB,CAACY,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAG9B,WAAW,CAAC,YAAY;IAC/C,IAAIyB,YAAY,EAAE;IAElBC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMoB,SAAS,GAAGR,IAAI,CAACS,KAAK,CAAC,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;;MAE/D,MAAMqB,QAAQ,GAAG,MAAMzC,iBAAiB,CAAC;QACvC0C,MAAM,EAAElC,IAAI,CAACmC,GAAG;QAChBC,OAAO,EAAE/B,eAAe;QACxB0B,SAAS;QACT9B;MACF,CAAC,CAAC;MAEF,IAAIgC,QAAQ,CAACI,OAAO,EAAE;QACpBxC,UAAU,CAACoC,QAAQ,CAACK,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL/C,OAAO,CAACgD,KAAK,CAACN,QAAQ,CAAC1C,OAAO,IAAI,uBAAuB,CAAC;QAC1DoB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDhD,OAAO,CAACgD,KAAK,CAAC,yCAAyC,CAAC;MACxD5B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACX,IAAI,CAACmC,GAAG,EAAE9B,eAAe,EAAEJ,aAAa,EAAEW,SAAS,EAAEf,UAAU,EAAEa,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAM+B,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAItC,oBAAoB,GAAGD,SAAS,CAACwC,MAAM,GAAG,CAAC,EAAE;MAC/CtC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9BzC,uBAAuB,CAACyC,KAAK,CAAC;EAChC,CAAC;EAED,IAAI3C,SAAS,CAACwC,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACEhD,OAAA;MAAKoD,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpHrD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAIoD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFzD,OAAA;UAAGoD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3EzD,OAAA;UACE0D,OAAO,EAAEtD,MAAO;UAChBgD,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGnD,SAAS,CAACC,oBAAoB,CAAC;EACvD,MAAMmD,cAAc,GAAGnD,oBAAoB,KAAKD,SAAS,CAACwC,MAAM,GAAG,CAAC;EACpE,MAAMa,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACpD,eAAe,CAAC,CAACqC,MAAM;;EAE7D;EACA,MAAMgB,YAAY,GAAG,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,MAAIN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,UAAU,KAAI,KAAK;EAClF,MAAMC,KAAK,GAAGH,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,iBAAiB;EACxG,MAAMI,WAAW,GAAGJ,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,mBAAmB,IAAIA,YAAY,KAAK,WAAW;;EAEnH;EACA,IAAIK,eAAe,GAAG,EAAE;EACxB,IAAIF,KAAK,EAAE;IACT,IAAIG,KAAK,CAACC,OAAO,CAACZ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,CAAC,EAAE;MAC3CH,eAAe,GAAGV,eAAe,CAACa,OAAO;IAC3C,CAAC,MAAM,IAAI,QAAOb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,MAAK,QAAQ,IAAI,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEa,OAAO,MAAK,IAAI,EAAE;MAC5FH,eAAe,GAAGP,MAAM,CAACW,MAAM,CAACd,eAAe,CAACa,OAAO,CAAC;IAC1D,CAAC,MAAM;MACLH,eAAe,GAAG,CAACV,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,OAAO,EAAEf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgB,OAAO,EAAEhB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiB,OAAO,EAAEjB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkB,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC5I;EACF;EAEA,oBACE/E,OAAA;IAAKoD,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExErD,OAAA;MAAKoD,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DrD,OAAA;QAAKoD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClErD,OAAA;UAAKoD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDrD,OAAA;YAAKoD,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtErD,OAAA;cACE0D,OAAO,EAAEtD,MAAO;cAChBgD,SAAS,EAAC,mGAAmG;cAAAC,QAAA,eAE7GrD,OAAA,CAACP,WAAW;gBAAC2D,SAAS,EAAC;cAA+D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACTzD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAIoD,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,EAAE/C,IAAI,CAAC0E;cAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9JzD,OAAA;gBAAKoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CrD,OAAA;kBAAMoD,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAE/C,IAAI,CAAC2E;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1EzD,OAAA;kBAAMoD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCzD,OAAA;kBAAMoD,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAAC,WACtD,EAAC5C,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACwC,MAAM;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAKoD,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBAErErD,OAAA;cAAKoD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBrD,OAAA;gBAAKoD,SAAS,EAAG,mHACfvC,QAAQ,IAAI,EAAE,GACV,mFAAmF,GACnFA,QAAQ,IAAI,GAAG,GACf,iFAAiF,GACjF,2EACL,EAAE;gBAACqE,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAA8B,CAAE;gBAAA9B,QAAA,gBACvDrD,OAAA,CAACL,OAAO;kBAACyD,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DzD,OAAA;kBAAMoD,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EAAE3B,UAAU,CAACb,QAAQ;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,EAEL5C,QAAQ,IAAI,GAAG,iBACdb,OAAA;gBACEoD,SAAS,EAAC,gFAAgF;gBAC1F8B,KAAK,EAAE;kBAAEE,SAAS,EAAE;gBAA8C;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzD,OAAA;cAAKoD,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrFrD,OAAA;gBAAMoD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAACrD,SAAS,CAACwC,MAAM,EAAC,WACxC;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9DrD,OAAA;YAAKoD,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3ErD,OAAA;cAAMoD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAChDQ,iBAAiB,EAAC,GAAC,EAACrD,SAAS,CAACwC,MAAM,EAAC,WACxC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAKoD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDrD,OAAA;QAAKoD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1DrD,OAAA;UAAKoD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrD,OAAA;YAAKoD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBrD,OAAA;cAAKoD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDrD,OAAA;gBAAMoD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnEzD,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpCxB,IAAI,CAACS,KAAK,CAAE,CAAC7B,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAACwC,MAAM,GAAI,GAAG,CAAC,EAAC,YACrE;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClDrD,OAAA,CAACR,MAAM,CAAC6F,GAAG;gBACTjC,SAAS,EAAC,yFAAyF;gBACnGkC,OAAO,EAAE;kBAAEC,KAAK,EAAE;gBAAE,CAAE;gBACtBC,OAAO,EAAE;kBAAED,KAAK,EAAG,GAAG,CAAC9E,oBAAoB,GAAG,CAAC,IAAID,SAAS,CAACwC,MAAM,GAAI,GAAI;gBAAG;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnD7C,SAAS,CAACiF,GAAG,CAAC,CAACC,CAAC,EAAEvC,KAAK,kBACtBnD,OAAA;cAEE0D,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACC,KAAK,CAAE;cACnCC,SAAS,EAAG,oDACVD,KAAK,KAAK1C,oBAAoB,GAC1B,uBAAuB,GACvBE,eAAe,CAACH,SAAS,CAAC2C,KAAK,CAAC,CAACV,GAAG,CAAC,GACrC,cAAc,GACd,+BACL,EAAE;cACHkD,KAAK,EAAG,YAAWxC,KAAK,GAAG,CAAE;YAAE,GAT1BA,KAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAKoD,SAAS,EAAC,8DAA8D;MAAAC,QAAA,eAC3ErD,OAAA,CAACR,MAAM,CAAC6F,GAAG;QAETC,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAEI,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BC,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BE,UAAU,EAAE;UAAEhF,QAAQ,EAAE,GAAG;UAAEiF,IAAI,EAAE;QAAU,CAAE;QAC/C5C,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBAGjFrD,OAAA;UAAKoD,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC5FrD,OAAA;YAAKoD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrD,OAAA;cAAKoD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CrD,OAAA;gBAAKoD,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChGrD,OAAA;kBAAMoD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACtD5C,oBAAoB,GAAG;gBAAC;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNzD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAKoD,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClEzD,OAAA;kBAAKoD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GACvD5C,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACwC,MAAM;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrD,OAAA;gBAAKoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDzD,OAAA;gBAAKoD,SAAS,EAAG,2DACfzC,eAAe,CAACgD,eAAe,CAAClB,GAAG,CAAC,GAChC,2DAA2D,GAC3D,8DACL,EAAE;gBAAAY,QAAA,EACA1C,eAAe,CAACgD,eAAe,CAAClB,GAAG,CAAC,GAAG,YAAY,GAAG;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrD,OAAA;cAAIoD,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EACzFM,eAAe,CAACqB;YAAI;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EAGJE,eAAe,CAACsC,KAAK,iBACpBjG,OAAA;cAAKoD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBrD,OAAA;gBAAKoD,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,eACvErD,OAAA;kBACEkG,GAAG,EAAEvC,eAAe,CAACsC,KAAM;kBAC3BE,GAAG,EAAC,UAAU;kBACd/C,SAAS,EAAC;gBAAgD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bc,KAAK,IAAIE,eAAe,CAACrB,MAAM,GAAG,CAAC;YAAA;YAClC;YACAhD,OAAA;cAAKoD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCrD,OAAA;gBAAIoD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJY,eAAe,CAACoB,GAAG,CAAC,CAACW,MAAM,EAAEjD,KAAK,KAAK;gBACtC,MAAMkD,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGpD,KAAK,CAAC,CAAC,CAAC;gBACtD,MAAMqD,UAAU,GAAG7F,eAAe,CAACgD,eAAe,CAAClB,GAAG,CAAC,KAAK4D,YAAY;gBAExE,oBACErG,OAAA,CAACR,MAAM,CAACiH,MAAM;kBAEZ/C,OAAO,EAAEA,CAAA,KAAMxB,kBAAkB,CAACyB,eAAe,CAAClB,GAAG,EAAE4D,YAAY,CAAE;kBACrEjD,SAAS,EAAG,gFACVoD,UAAU,GACN,iEAAiE,GACjE,wEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAEH,UAAU,GAAG,IAAI,GAAG;kBAAK,CAAE;kBAChDI,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAtD,QAAA,eAE1BrD,OAAA;oBAAKoD,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,gBACvDrD,OAAA;sBAAKoD,SAAS,EAAG,sGACfoD,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAAnD,QAAA,EACAmD,UAAU,gBAAGxG,OAAA,CAACJ,OAAO;wBAACwD,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG4C;oBAAY;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACNzD,OAAA;sBAAMoD,SAAS,EAAC,yDAAyD;sBAAAC,QAAA,EACtE+C;oBAAM;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GArBDN,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsBG,CAAC;cAEpB,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;YAAA;YAEN;YACAzD,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAIoD,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAE/D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzD,OAAA;gBAAKoD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBrD,OAAA;kBACEiE,IAAI,EAAC,MAAM;kBACX4C,KAAK,EAAElG,eAAe,CAACgD,eAAe,CAAClB,GAAG,CAAC,IAAI,EAAG;kBAClDqE,QAAQ,EAAGC,CAAC,IAAK7E,kBAAkB,CAACyB,eAAe,CAAClB,GAAG,EAAEsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACzEI,WAAW,EAAC,0BAA0B;kBACtC7D,SAAS,EAAC;gBAAsM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjN,CAAC,eACFzD,OAAA;kBAAKoD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9ErD,OAAA,CAACR,MAAM,CAACiH,MAAM;cACZ/C,OAAO,EAAET,YAAa;cACtBiE,QAAQ,EAAEzG,oBAAoB,KAAK,CAAE;cACrC2C,SAAS,EAAG,8GACV3C,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6DACL,EAAE;cACHiG,UAAU,EAAEjG,oBAAoB,GAAG,CAAC,GAAG;gBAAEkG,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAC5DC,QAAQ,EAAEnG,oBAAoB,GAAG,CAAC,GAAG;gBAAEkG,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAAtD,QAAA,gBAE1DrD,OAAA,CAACP,WAAW;gBAAC2D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCzD,OAAA;gBAAMoD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,EAEfG,cAAc,gBACb5D,OAAA,CAACR,MAAM,CAACiH,MAAM;cACZ/C,OAAO,EAAErC,gBAAiB;cAC1B6F,QAAQ,EAAElG,YAAa;cACvBoC,SAAS,EAAG,+GACVpC,YAAY,GACR,8CAA8C,GAC9C,2HACL,EAAE;cACH0F,UAAU,EAAE,CAAC1F,YAAY,GAAG;gBAAE2F,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cACjDC,QAAQ,EAAE,CAAC5F,YAAY,GAAG;gBAAE2F,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAAAtD,QAAA,GAE9CrC,YAAY,gBACXhB,OAAA;gBAAKoD,SAAS,EAAC;cAA2E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE7FzD,OAAA,CAACJ,OAAO;gBAACwD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC/B,eACDzD,OAAA;gBAAMoD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EACnCrC,YAAY,GAAG,eAAe,GAAG;cAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,gBAEhBzD,OAAA,CAACR,MAAM,CAACiH,MAAM;cACZ/C,OAAO,EAAEX,QAAS;cAClBK,SAAS,EAAC,kOAAkO;cAC5OsD,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAtD,QAAA,gBAE1BrD,OAAA;gBAAMoD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DzD,OAAA,CAACN,YAAY;gBAAC0D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAzKDhD,oBAAoB;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0Kf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNzD,OAAA;MAAKoD,SAAS,EAAC,oGAAoG;MAAAC,QAAA,EAAC;IAEpH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CA3ZIJ,aAAa;AAAAkH,EAAA,GAAblH,aAAa;AA6ZnB,eAAeA,aAAa;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}