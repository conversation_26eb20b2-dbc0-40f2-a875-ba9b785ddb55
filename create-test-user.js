const axios = require('axios');

const createTestUser = async () => {
  try {
    console.log('🧪 Creating test user...');
    
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'test123',
      level: 'primary',
      class: '1'
    };
    
    console.log('📝 Registering user:', userData.email);
    
    const response = await axios.post('http://localhost:5000/api/users/register', userData);
    
    console.log('✅ Registration response:');
    console.log(response.data);
    
    // Now try to login
    console.log('\n🔐 Testing login...');
    const loginData = {
      email: userData.email,
      password: userData.password
    };
    
    const loginResponse = await axios.post('http://localhost:5000/api/users/login', loginData);
    
    console.log('✅ Login response:');
    console.log(loginResponse.data);
    
  } catch (error) {
    console.log('❌ Error occurred');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
};

createTestUser();
