import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { TbArrowLeft, TbArrowRight, Tb<PERSON>lock, TbCheck } from "react-icons/tb";
import { message } from "antd";
import { submitTrialResult } from "../../apicalls/trial";
import "./TrialQuiz.css";

const TrialQuizPlay = ({ quizData, onComplete, onBack }) => {
  const { exam, trialUserInfo } = quizData;
  const questions = exam.questions || [];
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startTime] = useState(Date.now());

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) {
      handleSubmitQuiz();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerSelect = (questionId, answer) => {
    playSuccessSound();
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // Submit quiz
  const handleSubmitQuiz = useCallback(async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds
      
      const response = await submitTrialResult({
        examId: exam._id,
        answers: selectedAnswers,
        timeSpent,
        trialUserInfo
      });

      if (response.success) {
        onComplete(response.data);
      } else {
        message.error(response.message || "Failed to submit quiz");
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("❌ Error submitting trial quiz:", error);
      message.error("Something went wrong. Please try again.");
      setIsSubmitting(false);
    }
  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);

  // Sound effects for navigation
  const playNavigationSound = () => {
    try {
      const audio = new Audio('/sounds/click.mp3');
      audio.volume = 0.2;
      audio.play().catch(() => {});
    } catch (error) {
      console.log('Navigation sound not available');
    }
  };

  const playSuccessSound = () => {
    try {
      const audio = new Audio('/sounds/success.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {});
    } catch (error) {
      console.log('Success sound not available');
    }
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      playNavigationSound();
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      playNavigationSound();
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToQuestion = (index) => {
    playNavigationSound();
    setCurrentQuestionIndex(index);
  };

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">No Questions Available</h2>
          <p className="text-gray-600 mb-4">This quiz doesn't have any questions.</p>
          <button
            onClick={onBack}
            className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const answeredQuestions = Object.keys(selectedAnswers).length;

  // Determine question type and prepare options
  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';
  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';
  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';
  const isDiagram = questionType === 'diagram' || questionType === 'Diagram' || currentQuestion?.image;

  // Prepare options for MCQ questions
  let questionOptions = [];
  if (isMCQ) {
    if (Array.isArray(currentQuestion?.options)) {
      questionOptions = currentQuestion.options;
    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {
      questionOptions = Object.values(currentQuestion.options);
    } else {
      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)'
    }}>
      {/* Enhanced Header with Progress */}
      <div className="trial-header" style={{
        background: 'white',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
        borderBottom: '2px solid #e0e7ff'
      }}>
        <div className="trial-container">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
              <button
                onClick={onBack}
                className="p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md"
              >
                <TbArrowLeft className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800" />
              </button>
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate">{exam.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm sm:text-base text-gray-600">{exam.subject}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm sm:text-base font-medium text-blue-600">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
              </div>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexShrink: 0 }}>
              {/* Enhanced Timer with Professional Styling */}
              <div style={{ position: 'relative' }}>
                <div className={`trial-timer ${
                  timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'
                }`}>
                  <TbClock className="trial-timer-icon" />
                  <span>{formatTime(timeLeft)}</span>
                </div>
                {/* Warning animation for low time */}
                {timeLeft <= 300 && (
                  <div className="timer-warning-ring"></div>
                )}
              </div>

              <div className="hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl">
                <span className="text-sm font-medium text-gray-700">
                  {answeredQuestions}/{questions.length} answered
                </span>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="trial-progress-container">
            <div className="trial-progress-header">
              <span className="trial-progress-label">
                Progress: {currentQuestionIndex + 1} of {questions.length}
              </span>
              <span className="trial-progress-percentage">
                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%
              </span>
            </div>
            <div className="trial-progress-bar">
              <div
                className="trial-progress-fill"
                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Mobile progress indicator */}
          <div className="sm:hidden mt-3 flex items-center justify-center">
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
              <span className="text-sm font-medium text-gray-700">
                {answeredQuestions}/{questions.length} answered
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Question Content */}
      <div className="trial-container trial-content">
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="trial-question-card"
        >
          {/* Question Header */}
          <div className="trial-question-header">
            <div className="trial-question-number">
              <div className="trial-question-number-badge">
                <span>{currentQuestionIndex + 1}</span>
              </div>
              <div>
                <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>Question</div>
                <div style={{ color: 'white', fontWeight: '500', fontSize: '18px' }}>
                  {currentQuestionIndex + 1} of {questions.length}
                </div>
              </div>
            </div>

            <h2 className="trial-question-title">
              {currentQuestion.name}
            </h2>
          </div>

          {/* Question Content */}
          <div className="trial-content">
            {/* Question Image (if exists) */}
            {currentQuestion.image && (
              <div style={{ marginBottom: '32px', textAlign: 'center' }}>
                <img
                  src={currentQuestion.image}
                  alt="Question"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '400px',
                    borderRadius: '16px',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </div>
            )}

            {/* Answer Section */}
            <div className="mb-10 sm:mb-12">
              {isMCQ && questionOptions.length > 0 ? (
                // Multiple Choice Questions
                <div className="space-y-4 sm:space-y-5">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-6">
                    Choose your answer:
                  </h3>
                  {questionOptions.map((option, index) => {
                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;

                    return (
                      <motion.button
                        key={index}
                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}
                        className={`trial-option ${isSelected ? 'selected' : ''}`}
                        whileHover={{ scale: isSelected ? 1.01 : 1.005 }}
                        whileTap={{ scale: 0.995 }}
                      >
                        <div className="trial-option-content">
                          <div className="trial-option-letter">
                            {isSelected ? <TbCheck style={{ width: '24px', height: '24px' }} /> : optionLetter}
                          </div>
                          <span className="trial-option-text">
                            {option}
                          </span>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              ) : isFillBlank ? (
                // Fill-in-the-blank / Free Text Questions
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    💭 Type your answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Type your answer here..."
                      className="trial-input"
                      style={{ paddingRight: '60px' }}
                    />
                    <div style={{
                      position: 'absolute',
                      right: '20px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      fontSize: '24px',
                      color: '#9ca3af'
                    }}>
                      ✏️
                    </div>
                  </div>
                  <div style={{
                    marginTop: '12px',
                    fontSize: '14px',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    💡 Tip: Be specific and clear in your answer
                  </div>
                </div>
              ) : isDiagram ? (
                // Diagram Questions
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    🔍 Study the diagram and answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Analyze the diagram and type your answer..."
                      className="trial-input"
                      style={{ paddingRight: '60px' }}
                    />
                    <div style={{
                      position: 'absolute',
                      right: '20px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      fontSize: '24px',
                      color: '#9ca3af'
                    }}>
                      📊
                    </div>
                  </div>
                  <div style={{
                    marginTop: '12px',
                    fontSize: '14px',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    🎯 Tip: Look carefully at all parts of the diagram
                  </div>
                </div>
              ) : (
                // Default fallback
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    📝 Provide your answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Type your answer here..."
                      className="trial-input"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="trial-nav-buttons">
              <motion.button
                onClick={goToPrevious}
                disabled={currentQuestionIndex === 0}
                className={`trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`}
                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}
                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}
              >
                <TbArrowLeft style={{ width: '20px', height: '20px' }} />
                <span>Previous</span>
              </motion.button>

              {isLastQuestion ? (
                <motion.button
                  onClick={handleSubmitQuiz}
                  disabled={isSubmitting}
                  className={`trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`}
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <div className="trial-spinner" />
                  ) : (
                    <TbCheck style={{ width: '20px', height: '20px' }} />
                  )}
                  <span>
                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
                  </span>
                </motion.button>
              ) : (
                <motion.button
                  onClick={goToNext}
                  className="trial-btn trial-btn-primary"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Next Question</span>
                  <TbArrowRight style={{ width: '20px', height: '20px' }} />
                </motion.button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Trial Watermark */}
      <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
        Trial Mode
      </div>
    </div>
  );
};

export default TrialQuizPlay;
