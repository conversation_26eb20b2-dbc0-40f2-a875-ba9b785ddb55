import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { TbArrowLeft, TbArrowRight, Tb<PERSON>lock, TbCheck } from "react-icons/tb";
import { message } from "antd";
import { submitTrialResult } from "../../apicalls/trial";
import "./TrialQuiz.css";

const TrialQuizPlay = ({ quizData, onComplete, onBack }) => {
  const { exam, trialUserInfo } = quizData;
  const questions = exam.questions || [];
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startTime] = useState(Date.now());

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) {
      handleSubmitQuiz();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerSelect = (questionId, answer) => {
    playSuccessSound();
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // Submit quiz
  const handleSubmitQuiz = useCallback(async () => {
    if (isSubmitting) return;

    playSubmitSound(); // Play submit sound
    setIsSubmitting(true);
    try {
      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds

      const response = await submitTrialResult({
        examId: exam._id,
        answers: selectedAnswers,
        timeSpent,
        trialUserInfo
      });

      if (response.success) {
        onComplete(response.data);
      } else {
        message.error(response.message || "Failed to submit quiz");
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("❌ Error submitting trial quiz:", error);
      message.error("Something went wrong. Please try again.");
      setIsSubmitting(false);
    }
  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);

  // Enhanced Sound effects for navigation
  const playNavigationSound = () => {
    try {
      // Create a simple click sound using Web Audio API
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.log('Navigation sound not available');
    }
  };

  const playSuccessSound = () => {
    try {
      // Create a pleasant success sound
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5
      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5
      oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G5

      gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.log('Success sound not available');
    }
  };

  const playSubmitSound = () => {
    try {
      // Create a completion sound
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4
      oscillator.frequency.setValueAtTime(554, audioContext.currentTime + 0.15); // C#5
      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.3); // E5
      oscillator.frequency.setValueAtTime(880, audioContext.currentTime + 0.45); // A5

      gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.6);
    } catch (error) {
      console.log('Submit sound not available');
    }
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      playNavigationSound();
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      playNavigationSound();
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToQuestion = (index) => {
    playNavigationSound();
    setCurrentQuestionIndex(index);
  };

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">No Questions Available</h2>
          <p className="text-gray-600 mb-4">This quiz doesn't have any questions.</p>
          <button
            onClick={onBack}
            className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const answeredQuestions = Object.keys(selectedAnswers).length;

  // Determine question type and prepare options
  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';
  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';
  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';
  const isDiagram = questionType === 'diagram' || questionType === 'Diagram' || currentQuestion?.image;

  // Prepare options for MCQ questions
  let questionOptions = [];
  if (isMCQ) {
    if (Array.isArray(currentQuestion?.options)) {
      questionOptions = currentQuestion.options;
    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {
      questionOptions = Object.values(currentQuestion.options);
    } else {
      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);
    }
  }

  return (
    <div className="trial-background" style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)',
      width: '100%',
      overflow: 'hidden'
    }}>
      {/* Enhanced Header with Progress */}
      <div className="trial-header" style={{
        background: 'white',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
        borderBottom: '2px solid #e0e7ff',
        width: '100%',
        position: 'sticky',
        top: 0,
        zIndex: 100
      }}>
        <div className="trial-container">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 'clamp(12px, 3vw, 20px)',
            flexWrap: 'wrap',
            gap: 'clamp(8px, 2vw, 16px)',
            width: '100%'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'clamp(8px, 2vw, 16px)',
              minWidth: 0,
              flex: 1,
              maxWidth: '100%'
            }}>
              <button
                onClick={onBack}
                style={{
                  padding: 'clamp(8px, 2vw, 12px)',
                  background: 'rgba(59, 130, 246, 0.1)',
                  border: 'none',
                  borderRadius: 'clamp(8px, 2vw, 12px)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '40px',
                  minHeight: '40px'
                }}
                onMouseEnter={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.2)'}
                onMouseLeave={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.1)'}
              >
                <TbArrowLeft style={{
                  width: 'clamp(18px, 4vw, 24px)',
                  height: 'clamp(18px, 4vw, 24px)',
                  color: '#2563eb'
                }} />
              </button>
              <div style={{ minWidth: 0, flex: 1, overflow: 'hidden' }}>
                <h1 style={{
                  fontSize: 'clamp(16px, 4vw, 24px)',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  lineHeight: 1.2
                }}>{exam.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm sm:text-base text-gray-600">{exam.subject}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm sm:text-base font-medium text-blue-600">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'clamp(8px, 2vw, 16px)',
              flexShrink: 0,
              flexWrap: 'wrap',
              justifyContent: 'flex-end'
            }}>
              {/* Enhanced Timer with Professional Styling */}
              <div style={{ position: 'relative' }}>
                <div className={`trial-timer ${
                  timeLeft <= 60 ? 'critical' : timeLeft <= 300 ? 'warning' : 'normal'
                }`}>
                  <TbClock className="trial-timer-icon" />
                  <span>{formatTime(timeLeft)}</span>
                </div>
                {/* Warning animation for low time */}
                {timeLeft <= 300 && (
                  <div className="timer-warning-ring"></div>
                )}
              </div>

              <div className="hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl">
                <span className="text-sm font-medium text-gray-700">
                  {answeredQuestions}/{questions.length} answered
                </span>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="trial-progress-container">
            <div className="trial-progress-header">
              <span className="trial-progress-label">
                Progress: {currentQuestionIndex + 1} of {questions.length}
              </span>
              <span className="trial-progress-percentage">
                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%
              </span>
            </div>
            <div className="trial-progress-bar">
              <div
                className="trial-progress-fill"
                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Mobile progress indicator */}
          <div className="sm:hidden mt-3 flex items-center justify-center">
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
              <span className="text-sm font-medium text-gray-700">
                {answeredQuestions}/{questions.length} answered
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Question Content */}
      <div className="trial-container" style={{
        paddingTop: 'clamp(16px, 4vw, 32px)',
        paddingBottom: 'clamp(16px, 4vw, 32px)'
      }}>
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="trial-question-card"
        >
          {/* Question Header */}
          <div className="trial-question-header">
            <div className="trial-question-number">
              <div className="trial-question-number-badge">
                <span>{currentQuestionIndex + 1}</span>
              </div>
              <div>
                <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>Question</div>
                <div style={{ color: 'white', fontWeight: '500', fontSize: '18px' }}>
                  {currentQuestionIndex + 1} of {questions.length}
                </div>
              </div>
            </div>

            <h2 className="trial-question-title">
              {currentQuestion.name}
            </h2>
          </div>

          {/* Question Content */}
          <div className="trial-content" style={{
            padding: 'clamp(16px, 4vw, 40px)',
            width: '100%',
            boxSizing: 'border-box'
          }}>
            {/* Question Image (if exists) */}
            {currentQuestion.image && (
              <div style={{ marginBottom: '32px', textAlign: 'center' }}>
                <img
                  src={currentQuestion.image}
                  alt="Question"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '400px',
                    borderRadius: '16px',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </div>
            )}

            {/* Answer Section */}
            <div className="mb-10 sm:mb-12">
              {isMCQ && questionOptions.length > 0 ? (
                // Multiple Choice Questions
                <div className="space-y-4 sm:space-y-5">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-6">
                    Choose your answer:
                  </h3>
                  {questionOptions.map((option, index) => {
                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;

                    return (
                      <motion.button
                        key={index}
                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}
                        className={`trial-option ${isSelected ? 'selected' : ''}`}
                        whileHover={{ scale: isSelected ? 1.01 : 1.005 }}
                        whileTap={{ scale: 0.995 }}
                      >
                        <div className="trial-option-content">
                          <div className="trial-option-letter">
                            {isSelected ? <TbCheck style={{ width: '24px', height: '24px' }} /> : optionLetter}
                          </div>
                          <span className="trial-option-text">
                            {option}
                          </span>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              ) : isFillBlank ? (
                // Fill-in-the-blank / Free Text Questions
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    💭 Type your answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Type your answer here..."
                      className="trial-input"
                      style={{ paddingRight: '60px' }}
                    />
                    <div style={{
                      position: 'absolute',
                      right: '20px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      fontSize: '24px',
                      color: '#9ca3af'
                    }}>
                      ✏️
                    </div>
                  </div>
                  <div style={{
                    marginTop: '12px',
                    fontSize: '14px',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    💡 Tip: Be specific and clear in your answer
                  </div>
                </div>
              ) : isDiagram ? (
                // Diagram Questions
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    🔍 Study the diagram and answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Analyze the diagram and type your answer..."
                      className="trial-input"
                      style={{ paddingRight: '60px' }}
                    />
                    <div style={{
                      position: 'absolute',
                      right: '20px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      fontSize: '24px',
                      color: '#9ca3af'
                    }}>
                      📊
                    </div>
                  </div>
                  <div style={{
                    marginTop: '12px',
                    fontSize: '14px',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    🎯 Tip: Look carefully at all parts of the diagram
                  </div>
                </div>
              ) : (
                // Default fallback
                <div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>
                    📝 Provide your answer:
                  </h3>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Type your answer here..."
                      className="trial-input"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="trial-nav-buttons">
              <motion.button
                onClick={goToPrevious}
                disabled={currentQuestionIndex === 0}
                className={`trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`}
                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}
                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}
              >
                <TbArrowLeft style={{ width: '20px', height: '20px' }} />
                <span>Previous</span>
              </motion.button>

              {isLastQuestion ? (
                <motion.button
                  onClick={handleSubmitQuiz}
                  disabled={isSubmitting}
                  className={`trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`}
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <div className="trial-spinner" />
                  ) : (
                    <TbCheck style={{ width: '20px', height: '20px' }} />
                  )}
                  <span>
                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
                  </span>
                </motion.button>
              ) : (
                <motion.button
                  onClick={goToNext}
                  className="trial-btn trial-btn-primary"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Next Question</span>
                  <TbArrowRight style={{ width: '20px', height: '20px' }} />
                </motion.button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Trial Watermark */}
      <div style={{
        position: 'fixed',
        bottom: 'clamp(12px, 3vw, 20px)',
        right: 'clamp(12px, 3vw, 20px)',
        background: '#2563eb',
        color: 'white',
        padding: 'clamp(6px, 1.5vw, 8px) clamp(12px, 3vw, 16px)',
        borderRadius: '20px',
        fontSize: 'clamp(10px, 2.5vw, 12px)',
        fontWeight: '500',
        boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
        zIndex: 1000,
        userSelect: 'none',
        pointerEvents: 'none'
      }}>
        Trial Mode
      </div>
    </div>
  );
};

export default TrialQuizPlay;
