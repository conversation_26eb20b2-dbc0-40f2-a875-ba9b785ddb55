import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from "react-icons/tb";
import { message } from "antd";
import { submitTrialResult } from "../../apicalls/trial";

const TrialQuizPlay = ({ quizData, onComplete, onBack }) => {
  const { exam, trialUserInfo } = quizData;
  const questions = exam.questions || [];
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startTime] = useState(Date.now());

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) {
      handleSubmitQuiz();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerSelect = (questionId, answer) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // Submit quiz
  const handleSubmitQuiz = useCallback(async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds
      
      const response = await submitTrialResult({
        examId: exam._id,
        answers: selectedAnswers,
        timeSpent,
        trialUserInfo
      });

      if (response.success) {
        onComplete(response.data);
      } else {
        message.error(response.message || "Failed to submit quiz");
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("❌ Error submitting trial quiz:", error);
      message.error("Something went wrong. Please try again.");
      setIsSubmitting(false);
    }
  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToQuestion = (index) => {
    setCurrentQuestionIndex(index);
  };

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">No Questions Available</h2>
          <p className="text-gray-600 mb-4">This quiz doesn't have any questions.</p>
          <button
            onClick={onBack}
            className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const answeredQuestions = Object.keys(selectedAnswers).length;

  // Determine question type and prepare options
  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';
  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';
  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';

  // Prepare options for MCQ questions
  let questionOptions = [];
  if (isMCQ) {
    if (Array.isArray(currentQuestion?.options)) {
      questionOptions = currentQuestion.options;
    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {
      questionOptions = Object.values(currentQuestion.options);
    } else {
      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Enhanced Header with Progress */}
      <div className="bg-white shadow-xl border-b-2 border-blue-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
              <button
                onClick={onBack}
                className="p-2 sm:p-3 hover:bg-blue-100 rounded-xl transition-all duration-200 flex-shrink-0 group shadow-md"
              >
                <TbArrowLeft className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 group-hover:text-blue-800" />
              </button>
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent truncate">{exam.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm sm:text-base text-gray-600">{exam.subject}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm sm:text-base font-medium text-blue-600">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3 sm:space-x-4 flex-shrink-0">
              {/* Enhanced Timer with Professional Styling */}
              <div className="relative">
                <div className={`flex items-center space-x-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl shadow-xl border-2 transition-all duration-300 ${
                  timeLeft <= 60
                    ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50 animate-pulse'
                    : timeLeft <= 300
                    ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50'
                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'
                }`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>
                  <TbClock className="w-5 h-5 sm:w-6 sm:h-6 drop-shadow-md" />
                  <span className="font-bold text-lg sm:text-xl drop-shadow-md">{formatTime(timeLeft)}</span>
                </div>
                {/* Warning animation for low time */}
                {timeLeft <= 300 && (
                  <div
                    className="absolute inset-0 border-2 border-yellow-300 rounded-xl animate-ping opacity-75"
                    style={{ animation: 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite' }}
                  ></div>
                )}
              </div>

              <div className="hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl">
                <span className="text-sm font-medium text-gray-700">
                  {answeredQuestions}/{questions.length} answered
                </span>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Progress: {currentQuestionIndex + 1} of {questions.length}
              </span>
              <span className="text-sm font-medium text-blue-600">
                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-500 ease-out shadow-lg"
                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Mobile progress indicator */}
          <div className="sm:hidden mt-3 flex items-center justify-center">
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
              <span className="text-sm font-medium text-gray-700">
                {answeredQuestions}/{questions.length} answered
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Question Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"
        >
          {/* Question Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 sm:px-8 lg:px-10 py-6 sm:py-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl flex items-center justify-center">
                  <span className="text-lg sm:text-xl font-bold text-white">
                    {currentQuestionIndex + 1}
                  </span>
                </div>
                <div>
                  <div className="text-white/90 text-sm sm:text-base">Question</div>
                  <div className="text-white font-medium text-lg sm:text-xl">
                    {currentQuestionIndex + 1} of {questions.length}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white/90 text-sm">Status</div>
                <div className={`text-sm sm:text-base font-medium px-3 py-1 rounded-full ${
                  selectedAnswers[currentQuestion._id]
                    ? 'bg-green-500/20 text-green-100 border border-green-400/30'
                    : 'bg-yellow-500/20 text-yellow-100 border border-yellow-400/30'
                }`}>
                  {selectedAnswers[currentQuestion._id] ? '✓ Answered' : '○ Pending'}
                </div>
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div className="px-6 sm:px-8 lg:px-10 py-8 sm:py-10 lg:py-12">
            <div className="mb-8 sm:mb-10">
              <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-relaxed mb-6">
                {currentQuestion.name}
              </h2>

              {/* Question Image (if exists) */}
              {currentQuestion.image && (
                <div className="mb-8">
                  <div className="bg-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-200">
                    <img
                      src={currentQuestion.image}
                      alt="Question"
                      className="max-w-full h-auto rounded-xl shadow-sm mx-auto"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Answer Section */}
            <div className="mb-10 sm:mb-12">
              {isMCQ && questionOptions.length > 0 ? (
                // Multiple Choice Questions
                <div className="space-y-4 sm:space-y-5">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-6">
                    Choose your answer:
                  </h3>
                  {questionOptions.map((option, index) => {
                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;

                    return (
                      <motion.button
                        key={index}
                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}
                        className={`w-full p-5 sm:p-6 text-left rounded-2xl border-2 transition-all duration-300 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-lg scale-[1.02]'
                            : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'
                        }`}
                        whileHover={{ scale: isSelected ? 1.02 : 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="flex items-center space-x-4 sm:space-x-5">
                          <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 flex items-center justify-center font-bold text-lg ${
                            isSelected
                              ? 'border-blue-500 bg-blue-500 text-white'
                              : 'border-gray-300 text-gray-600'
                          }`}>
                            {isSelected ? <TbCheck className="w-5 h-5 sm:w-6 sm:h-6" /> : optionLetter}
                          </div>
                          <span className="flex-1 text-base sm:text-lg font-medium leading-relaxed">
                            {option}
                          </span>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              ) : (
                // Fill-in-the-blank / Free Text Questions
                <div className="space-y-6">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                    Type your answer:
                  </h3>
                  <div className="relative">
                    <input
                      type="text"
                      value={selectedAnswers[currentQuestion._id] || ''}
                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}
                      placeholder="Type your answer here..."
                      className="w-full p-5 sm:p-6 border-2 border-gray-300 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-base sm:text-lg font-medium bg-gray-50 focus:bg-white"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                      ✏️
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-8 border-t border-gray-200">
              <motion.button
                onClick={goToPrevious}
                disabled={currentQuestionIndex === 0}
                className={`flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${
                  currentQuestionIndex === 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 hover:shadow-md'
                }`}
                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}
                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}
              >
                <TbArrowLeft className="w-5 h-5" />
                <span className="text-base sm:text-lg">Previous</span>
              </motion.button>

              {isLastQuestion ? (
                <motion.button
                  onClick={handleSubmitQuiz}
                  disabled={isSubmitting}
                  className={`flex items-center space-x-3 px-8 sm:px-10 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${
                    isSubmitting
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl'
                  }`}
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  ) : (
                    <TbCheck className="w-5 h-5" />
                  )}
                  <span className="text-base sm:text-lg">
                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
                  </span>
                </motion.button>
              ) : (
                <motion.button
                  onClick={goToNext}
                  className="flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-base sm:text-lg">Next Question</span>
                  <TbArrowRight className="w-5 h-5" />
                </motion.button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Trial Watermark */}
      <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
        Trial Mode
      </div>
    </div>
  );
};

export default TrialQuizPlay;
