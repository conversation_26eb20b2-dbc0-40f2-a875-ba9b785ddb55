const axios = require('axios');

async function testQuizAPI() {
  try {
    console.log('🔍 Testing Quiz API...');
    
    // Test get all exams
    const response = await axios.post('http://localhost:5000/api/exams/get-all-exams');
    console.log('✅ API Response Status:', response.status);
    console.log('📊 Exams found:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      const firstExam = response.data.data[0];
      console.log('\n🎯 First exam details:');
      console.log('- ID:', firstExam._id);
      console.log('- Name:', firstExam.name);
      console.log('- Questions count:', firstExam.questions?.length || 0);
      console.log('- Duration:', firstExam.duration);
      console.log('- Subject:', firstExam.subject);
      console.log('- Class:', firstExam.class);
      
      // Test get exam by ID
      if (firstExam._id) {
        console.log('\n🔍 Testing get exam by ID...');
        const examResponse = await axios.post('http://localhost:5000/api/exams/get-exam-by-id', {
          examId: firstExam._id
        });
        
        console.log('✅ Exam by ID Status:', examResponse.status);
        const examData = examResponse.data.data;
        console.log('📝 Questions in exam:', examData.questions?.length || 0);
        
        if (examData.questions && examData.questions.length > 0) {
          const firstQuestion = examData.questions[0];
          console.log('\n📋 First question:');
          console.log('- Question text:', firstQuestion.name?.substring(0, 100) + '...');
          console.log('- Type:', firstQuestion.type);
          console.log('- Answer Type:', firstQuestion.answerType);
          console.log('- Has options:', firstQuestion.options ? 'Yes' : 'No');
          if (firstQuestion.options) {
            console.log('- Option count:', Object.keys(firstQuestion.options).length);
          }
        } else {
          console.log('❌ No questions found in exam details');
        }
      }
    } else {
      console.log('❌ No exams found');
    }
    
  } catch (error) {
    console.error('❌ API Test Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testQuizAPI();
