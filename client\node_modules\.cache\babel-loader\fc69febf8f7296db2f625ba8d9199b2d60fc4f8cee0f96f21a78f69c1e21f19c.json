{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbTrophy, TbCheck, TbX, TbClock, TbBrain, TbArrowRight, TbStar, TbUsers, TbBook, TbMessageCircle, TbChartBar, TbSettings } from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport \"./TrialQuiz.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizResult = ({\n  result,\n  onTryAnother,\n  onRegister\n}) => {\n  _s();\n  var _result$questionResul;\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n  const [showFailAnimation, setShowFailAnimation] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: {\n            y: 0.6\n          }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Enhanced failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setShowFailAnimation(true);\n\n        // Play failure sound\n        try {\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          // Create a descending failure sound\n          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);\n          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);\n          oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);\n          gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 1);\n        } catch (error) {\n          console.log('Failure sound not available');\n        }\n        setTimeout(() => {\n          setShowFlash(false);\n          setShowFailAnimation(false);\n        }, 800);\n      }, 1000);\n    }\n  }, [result.percentage]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n  const getPerformanceMessage = percentage => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n  const premiumFeatures = [{\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Access comprehensive study materials, notes, and resources\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with other students and track your progress\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\"\n  }, {\n    icon: TbUsers,\n    title: \"Unlimited Quizzes\",\n    description: \"Take as many quizzes as you want across all subjects\"\n  }, {\n    icon: TbStar,\n    title: \"Progress Tracking\",\n    description: \"Detailed analytics and performance insights\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)',\n      position: 'relative'\n    },\n    children: [showFlash && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flash-animation ${result.percentage >= 60 ? 'flash-success' : 'flash-failure'}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"trial-container\",\n      style: {\n        position: 'relative',\n        zIndex: 10\n      },\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        className: \"text-center mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.3,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: `inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`,\n          onAnimationComplete: () => setAnimationComplete(true),\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n          children: \"Quiz Complete! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.8\n          },\n          className: `inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`,\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-xl sm:text-2xl font-bold ${performance.color}`,\n            children: performance.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.6,\n          delay: 1.0\n        },\n        className: \"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                duration: 0.8,\n                delay: 1.2,\n                type: \"spring\"\n              },\n              className: \"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\",\n              children: [result.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/90 text-lg sm:text-xl\",\n              children: \"Your Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\",\n            children: [{\n              label: \"Total Questions\",\n              value: result.totalQuestions,\n              icon: TbBook,\n              color: \"blue\",\n              delay: 1.4\n            }, {\n              label: \"Correct Answers\",\n              value: result.correctAnswers,\n              icon: TbCheck,\n              color: \"green\",\n              delay: 1.6\n            }, {\n              label: \"Wrong Answers\",\n              value: result.wrongAnswers,\n              icon: TbX,\n              color: \"red\",\n              delay: 1.8\n            }, {\n              label: \"Time Taken\",\n              value: formatTime(result.timeSpent),\n              icon: TbClock,\n              color: \"purple\",\n              delay: 2.0\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: stat.delay\n              },\n              className: `p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: `w-6 h-6 text-${stat.color}-600`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`,\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.2\n            },\n            className: \"text-center mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${isPassed ? 'bg-green-100 text-green-700 border-2 border-green-200' : 'bg-red-100 text-red-700 border-2 border-red-200'}`,\n              children: [isPassed ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.4\n            },\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetails(!showDetails),\n              className: \"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: showDetails ? 'Hide Question Summary' : 'View Question Summary'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 2.6\n        },\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\",\n            whileHover: {\n              scale: 1.05,\n              y: -2\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 2.8\n        },\n        className: \"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl sm:text-3xl font-bold mb-2\",\n            children: \"\\uD83D\\uDD13 Unlock These Amazing Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Join thousands of students already excelling with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 3.0 + 0.1 * index\n              },\n              className: \"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-gray-800\",\n                  children: feature.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center text-blue-600 font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-5 h-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"Premium Feature\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 3.4\n            },\n            className: \"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-6 h-6 mr-2 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), \"Advanced Quiz Features & Maximum Control\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 3.6\n                },\n                className: \"bg-white rounded-xl p-5 shadow-sm border border-purple-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(TbBook, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-bold text-gray-800\",\n                    children: \"Multiple Subject Selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-gray-600 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Choose from \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"15+ subjects\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 435,\n                        columnNumber: 41\n                      }, this), \" across all levels\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Mix and match subjects in custom quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Subject-specific performance tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Cross-subject comparison analytics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 3.8\n                },\n                className: \"bg-white rounded-xl p-5 shadow-sm border border-blue-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(TbSettings, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-bold text-gray-800\",\n                    children: \"Maximum Quiz Control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-gray-600 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Set custom \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"time limits\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 40\n                      }, this), \" (5-180 minutes)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose question count (5-100 questions)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Select difficulty levels (Easy, Medium, Hard)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-500 mr-2\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Pause and resume quiz sessions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 4.0\n              },\n              className: \"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-bold text-gray-800 mb-4 text-center\",\n                children: \"\\uD83D\\uDE80 Advanced Quiz Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [{\n                  icon: \"⏱️\",\n                  title: \"Smart Timer\",\n                  desc: \"Adaptive timing\"\n                }, {\n                  icon: \"🎯\",\n                  title: \"Targeted Practice\",\n                  desc: \"Weak area focus\"\n                }, {\n                  icon: \"📊\",\n                  title: \"Live Analytics\",\n                  desc: \"Real-time insights\"\n                }, {\n                  icon: \"🏆\",\n                  title: \"Achievement System\",\n                  desc: \"Unlock rewards\"\n                }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    duration: 0.3,\n                    delay: 4.2 + 0.1 * index\n                  },\n                  className: \"text-center p-3 bg-white rounded-lg shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xl mb-1\",\n                    children: feature.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-xs text-gray-800\",\n                    children: feature.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: feature.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 3.6\n            },\n            className: \"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n              children: \"\\uD83C\\uDFAF Why Students Choose BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n              children: [{\n                icon: \"🚀\",\n                title: \"Instant Access\",\n                desc: \"Start learning immediately\"\n              }, {\n                icon: \"📱\",\n                title: \"Mobile Friendly\",\n                desc: \"Study anywhere, anytime\"\n              }, {\n                icon: \"🎓\",\n                title: \"Expert Content\",\n                desc: \"Created by top educators\"\n              }, {\n                icon: \"🏆\",\n                title: \"Proven Results\",\n                desc: \"98% success rate\"\n              }].map((benefit, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.4,\n                  delay: 3.8 + 0.1 * index\n                },\n                className: \"text-center p-4 bg-white rounded-xl shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl mb-2\",\n                  children: benefit.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-gray-800 mb-1\",\n                  children: benefit.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: benefit.desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 4.2\n            },\n            className: \"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold mb-2\",\n              children: \"Ready to Excel? \\uD83C\\uDF1F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 mb-4\",\n              children: \"Join BrainWave today and unlock your full potential!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-3 justify-center items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Create Free Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-200 text-sm\",\n                children: \"\\u2728 No credit card required \\u2022 Start immediately\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: \"auto\"\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-800 mb-4\",\n          children: \"Question Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: (_result$questionResul = result.questionResults) === null || _result$questionResul === void 0 ? void 0 : _result$questionResul.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg border-2 ${q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center ${q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                children: q.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 38\n                }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800 mb-2\",\n                  children: q.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Your answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`,\n                      children: q.userAnswer || 'Not answered'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 25\n                  }, this), !q.isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Correct answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 font-medium text-green-600\",\n                      children: q.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onTryAnother,\n          className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n          children: \"Try Another Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",\n            children: \"Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\",\n          children: \"\\uD83C\\uDFAF Trial Mode - Register for unlimited access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizResult, \"Iq8oqC1KXd0V/mWq3xNjG7JY8qE=\");\n_c = TrialQuizResult;\nexport default TrialQuizResult;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "TbTrophy", "TbCheck", "TbX", "TbClock", "TbBrain", "TbArrowRight", "TbStar", "TbUsers", "TbBook", "TbMessageCircle", "TbChartBar", "TbSettings", "confetti", "jsxDEV", "_jsxDEV", "TrialQuizResult", "result", "onTryAnother", "onRegister", "_s", "_result$questionResul", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "showFlash", "setShowFlash", "showFailAnimation", "setShowFailAnimation", "isPassed", "percentage", "setTimeout", "particleCount", "spread", "origin", "y", "audio", "Audio", "volume", "play", "catch", "error", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getPerformanceMessage", "message", "color", "bg", "gradient", "performance", "premiumFeatures", "icon", "title", "description", "style", "minHeight", "background", "position", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "zIndex", "div", "initial", "opacity", "animate", "transition", "duration", "ease", "scale", "delay", "type", "stiffness", "onAnimationComplete", "h1", "split", "label", "value", "totalQuestions", "correctAnswers", "wrongAnswers", "timeSpent", "map", "stat", "index", "onClick", "to", "button", "whileHover", "whileTap", "feature", "x", "desc", "benefit", "height", "questionResults", "q", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport {\n  TbTrophy,\n  TbCheck,\n  TbX,\n  TbClock,\n  TbBrain,\n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar,\n  TbSettings\n} from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n  const [showFailAnimation, setShowFailAnimation] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: { y: 0.6 }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Enhanced failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setShowFailAnimation(true);\n\n        // Play failure sound\n        try {\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          // Create a descending failure sound\n          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);\n          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);\n          oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);\n\n          gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);\n\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 1);\n        } catch (error) {\n          console.log('Failure sound not available');\n        }\n\n        setTimeout(() => {\n          setShowFlash(false);\n          setShowFailAnimation(false);\n        }, 800);\n      }, 1000);\n    }\n  }, [result.percentage]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)',\n      position: 'relative'\n    }}>\n      {/* Flash Animation Overlay */}\n      {showFlash && (\n        <div\n          className={`flash-animation ${\n            result.percentage >= 60 ? 'flash-success' : 'flash-failure'\n          }`}\n        />\n      )}\n\n      <div className=\"trial-container\" style={{ position: 'relative', zIndex: 10 }}>\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\" />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\"\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className=\"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\"\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-blue-600 font-medium\">\n                    <TbStar className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Premium Feature</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Better Quiz Features */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.4 }}\n              className=\"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\">\n                <TbBrain className=\"w-6 h-6 mr-2 text-purple-600\" />\n                Advanced Quiz Features & Maximum Control\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Multiple Subject Selection */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.6 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-purple-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbBook className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Multiple Subject Selection</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Mix and match subjects in custom quizzes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Subject-specific performance tracking</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Cross-subject comparison analytics</span>\n                    </li>\n                  </ul>\n                </motion.div>\n\n                {/* Maximum Control */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.8 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-blue-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbSettings className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Maximum Quiz Control</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Choose question count (5-100 questions)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Select difficulty levels (Easy, Medium, Hard)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Pause and resume quiz sessions</span>\n                    </li>\n                  </ul>\n                </motion.div>\n              </div>\n\n              {/* Advanced Features */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 4.0 }}\n                className=\"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\"\n              >\n                <h5 className=\"font-bold text-gray-800 mb-4 text-center\">🚀 Advanced Quiz Features</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {[\n                    { icon: \"⏱️\", title: \"Smart Timer\", desc: \"Adaptive timing\" },\n                    { icon: \"🎯\", title: \"Targeted Practice\", desc: \"Weak area focus\" },\n                    { icon: \"📊\", title: \"Live Analytics\", desc: \"Real-time insights\" },\n                    { icon: \"🏆\", title: \"Achievement System\", desc: \"Unlock rewards\" }\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}\n                      className=\"text-center p-3 bg-white rounded-lg shadow-sm\"\n                    >\n                      <div className=\"text-xl mb-1\">{feature.icon}</div>\n                      <div className=\"font-semibold text-xs text-gray-800\">{feature.title}</div>\n                      <div className=\"text-xs text-gray-600\">{feature.desc}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            </motion.div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,EACfC,UAAU,EACVC,UAAU,QACL,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgC,QAAQ,GAAGb,MAAM,CAACc,UAAU,IAAI,EAAE;IAExC,IAAID,QAAQ,EAAE;MACZ;MACAE,UAAU,CAAC,MAAM;QACfnB,QAAQ,CAAC;UACPoB,aAAa,EAAE,GAAG;UAClBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAI;QACnB,CAAC,CAAC;;QAEF;QACA,IAAI;UACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,kBAAkB,CAAC;UAC3CD,KAAK,CAACE,MAAM,GAAG,GAAG;UAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACpC;;QAEA;QACAjB,YAAY,CAAC,IAAI,CAAC;QAClBK,UAAU,CAAC,MAAML,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL;MACAK,UAAU,CAAC,MAAM;QACf;QACAL,YAAY,CAAC,IAAI,CAAC;QAClBE,oBAAoB,CAAC,IAAI,CAAC;;QAE1B;QACA,IAAI;UACF,MAAMgB,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAC7E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;UAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,CAAC,CAAC;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAACR,YAAY,CAACS,WAAW,CAAC;;UAE1C;UACAL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEX,YAAY,CAACY,WAAW,CAAC;UAClER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAEb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;UACtFR,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAEb,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;UAEpFN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,EAAEX,YAAY,CAACY,WAAW,CAAC;UAC5DN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAEb,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;UAE9ER,UAAU,CAACW,KAAK,CAACf,YAAY,CAACY,WAAW,CAAC;UAC1CR,UAAU,CAACY,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;QAEAZ,UAAU,CAAC,MAAM;UACfL,YAAY,CAAC,KAAK,CAAC;UACnBE,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACZ,MAAM,CAACc,UAAU,CAAC,CAAC;EAEvB,MAAM+B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,KAAIG,gBAAiB,GAAE;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAIrC,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE,gBAAgB;MACvBC,EAAE,EAAE,aAAa;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,eAAe;MACxBC,KAAK,EAAE,eAAe;MACtBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,cAAc;MACvBC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE,eAAe;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIzC,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BsC,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAGL,qBAAqB,CAACnD,MAAM,CAACc,UAAU,CAAC;EAC5D,MAAMD,QAAQ,GAAGb,MAAM,CAACc,UAAU,IAAI,EAAE;EAExC,MAAM2C,eAAe,GAAG,CACtB;IACEC,IAAI,EAAElE,MAAM;IACZmE,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEtE,OAAO;IACbuE,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhE,UAAU;IAChBiE,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEjE,eAAe;IACrBkE,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEnE,OAAO;IACboE,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEpE,MAAM;IACZqE,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE9D,OAAA;IAAK+D,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,GAECxD,SAAS,iBACRX,OAAA;MACEoE,SAAS,EAAG,mBACVlE,MAAM,CAACc,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,eAC7C;IAAE;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACF,eAEDxE,OAAA;MAAKoE,SAAS,EAAC,iBAAiB;MAACL,KAAK,EAAE;QAAEG,QAAQ,EAAE,UAAU;QAAEO,MAAM,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAE3EnE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCwD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAE,CAAE;QAC9ByD,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAC/CZ,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBAErCnE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;UACTC,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBJ,OAAO,EAAE;YAAEI,KAAK,EAAE;UAAE,CAAE;UACtBH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE,GAAG;YAAEC,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC1EhB,SAAS,EAAG,mHAAkHV,WAAW,CAACD,QAAS,iBAAiB;UACpK4B,mBAAmB,EAAEA,CAAA,KAAM3E,oBAAoB,CAAC,IAAI,CAAE;UAAAyD,QAAA,eAEtDnE,OAAA,CAACd,QAAQ;YAACkF,SAAS,EAAC;UAAsD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAEbxE,OAAA,CAAChB,MAAM,CAACsG,EAAE;UACRX,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,+DAA+D;UAAAD,QAAA,EAC1E;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEvD,CAAC,EAAE;UAAG,CAAE;UAC/BwD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEvD,CAAC,EAAE;UAAE,CAAE;UAC9ByD,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAG,uCAAsCV,WAAW,CAACF,EAAG,oBAAmBE,WAAW,CAACH,KAAK,CAACgC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM;UAAApB,QAAA,eAE1HnE,OAAA;YAAGoE,SAAS,EAAG,iCAAgCV,WAAW,CAACH,KAAM,EAAE;YAAAY,QAAA,EAChET,WAAW,CAACJ;UAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,KAAK,EAAE;QAAI,CAAE;QACpCJ,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEK,KAAK,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,sFAAsF;QAAAD,QAAA,gBAGhGnE,OAAA;UAAKoE,SAAS,EAAG,oBAAmBV,WAAW,CAACD,QAAS,qCAAqC;UAAAU,QAAA,eAC5FnE,OAAA;YAAKoE,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1BnE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;cACTC,OAAO,EAAE;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACtBJ,OAAO,EAAE;gBAAEI,KAAK,EAAE;cAAE,CAAE;cACtBH,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC1Df,SAAS,EAAC,4DAA4D;cAAAD,QAAA,GAErEjE,MAAM,CAACc,UAAU,EAAC,GACrB;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA;cAAKoE,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAKoE,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAElDnE,OAAA;YAAKoE,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EACjE,CACC;cACEqB,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAEvF,MAAM,CAACwF,cAAc;cAC5B9B,IAAI,EAAElE,MAAM;cACZ6D,KAAK,EAAE,MAAM;cACb2B,KAAK,EAAE;YACT,CAAC,EACD;cACEM,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAEvF,MAAM,CAACyF,cAAc;cAC5B/B,IAAI,EAAEzE,OAAO;cACboE,KAAK,EAAE,OAAO;cACd2B,KAAK,EAAE;YACT,CAAC,EACD;cACEM,KAAK,EAAE,eAAe;cACtBC,KAAK,EAAEvF,MAAM,CAAC0F,YAAY;cAC1BhC,IAAI,EAAExE,GAAG;cACTmE,KAAK,EAAE,KAAK;cACZ2B,KAAK,EAAE;YACT,CAAC,EACD;cACEM,KAAK,EAAE,YAAY;cACnBC,KAAK,EAAE1C,UAAU,CAAC7C,MAAM,CAAC2F,SAAS,CAAC;cACnCjC,IAAI,EAAEvE,OAAO;cACbkE,KAAK,EAAE,QAAQ;cACf2B,KAAK,EAAE;YACT,CAAC,CACF,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBhG,OAAA,CAAChB,MAAM,CAAC0F,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAG,CAAE;cAC/BwD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAE,CAAE;cAC9ByD,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAEa,IAAI,CAACb;cAAM,CAAE;cACjDd,SAAS,EAAG,iBAAgB2B,IAAI,CAACxC,KAAM,iCAAgCwC,IAAI,CAACxC,KAAM,kBAAkB;cAAAY,QAAA,gBAEpGnE,OAAA;gBAAKoE,SAAS,EAAG,6BAA4B2B,IAAI,CAACxC,KAAM,kDAAkD;gBAAAY,QAAA,eACxGnE,OAAA,CAAC+F,IAAI,CAACnC,IAAI;kBAACQ,SAAS,EAAG,gBAAe2B,IAAI,CAACxC,KAAM;gBAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNxE,OAAA;gBAAKoE,SAAS,EAAG,uCAAsC2B,IAAI,CAACxC,KAAM,WAAW;gBAAAY,QAAA,EAC1E4B,IAAI,CAACN;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACNxE,OAAA;gBAAKoE,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAC/C4B,IAAI,CAACP;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAdDwB,KAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEK,KAAK,EAAE;YAAI,CAAE;YACpCJ,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEK,KAAK,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,kBAAkB;YAAAD,QAAA,eAE5BnE,OAAA;cAAKoE,SAAS,EAAG,kFACfrD,QAAQ,GACJ,uDAAuD,GACvD,iDACL,EAAE;cAAAoD,QAAA,GACApD,QAAQ,gBACPf,OAAA,CAACb,OAAO;gBAACiF,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/BxE,OAAA,CAACZ,GAAG;gBAACgF,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC3B,eACDxE,OAAA;gBAAAmE,QAAA,EACGpD,QAAQ,GAAG,iCAAiC,GAAG;cAAsC;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,aAAa;YAAAD,QAAA,eAEvBnE,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAMzF,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C6D,SAAS,EAAC,6IAA6I;cAAAD,QAAA,gBAEvJnE,OAAA,CAACJ,UAAU;gBAACwE,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCxE,OAAA;gBAAAmE,QAAA,EAAO5D,WAAW,GAAG,uBAAuB,GAAG;cAAuB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAG,CAAE;QAC/BwD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAE,CAAE;QAC9ByD,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAE5BnE,OAAA,CAACf,IAAI;UAACiH,EAAE,EAAC,WAAW;UAAA/B,QAAA,eAClBnE,OAAA,CAAChB,MAAM,CAACmH,MAAM;YACZ/B,SAAS,EAAC,mOAAmO;YAC7OgC,UAAU,EAAE;cAAEnB,KAAK,EAAE,IAAI;cAAE5D,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCgF,QAAQ,EAAE;cAAEpB,KAAK,EAAE;YAAK,CAAE;YAAAd,QAAA,gBAE1BnE,OAAA;cAAAmE,QAAA,EAAM;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBxE,OAAA,CAACT,YAAY;cAAC6E,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAG,CAAE;QAC/BwD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAE,CAAE;QAC9ByD,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,4EAA4E;QAAAD,QAAA,gBAGtFnE,OAAA;UAAKoE,SAAS,EAAC,uFAAuF;UAAAD,QAAA,gBACpGnE,OAAA;YAAIoE,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAEpD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxE,OAAA;YAAGoE,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNxE,OAAA;UAAKoE,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBnE,OAAA;YAAKoE,SAAS,EAAC,sDAAsD;YAAAD,QAAA,EAClER,eAAe,CAACmC,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAClChG,OAAA,CAAChB,MAAM,CAAC0F,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAG,CAAE;cAC/BwD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAE,CAAE;cAC9ByD,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;cAAO,CAAE;cAC1D5B,SAAS,EAAC,sIAAsI;cAAAD,QAAA,gBAEhJnE,OAAA;gBAAKoE,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,gBAC/CnE,OAAA;kBAAKoE,SAAS,EAAC,4JAA4J;kBAAAD,QAAA,eACzKnE,OAAA,CAACsG,OAAO,CAAC1C,IAAI;oBAACQ,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNxE,OAAA;kBAAIoE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAEmC,OAAO,CAACzC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNxE,OAAA;gBAAGoE,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,EAAEmC,OAAO,CAACxC;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtExE,OAAA;gBAAKoE,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC/DnE,OAAA,CAACR,MAAM;kBAAC4E,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCxE,OAAA;kBAAMoE,SAAS,EAAC,SAAS;kBAAAD,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA,GAhBDwB,KAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAG,CAAE;YAC/BwD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAE,CAAE;YAC9ByD,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,0FAA0F;YAAAD,QAAA,gBAEpGnE,OAAA;cAAIoE,SAAS,EAAC,mFAAmF;cAAAD,QAAA,gBAC/FnE,OAAA,CAACV,OAAO;gBAAC8E,SAAS,EAAC;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4CAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAKoE,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBAEpDnE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAE2B,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChC1B,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAE2B,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,4DAA4D;gBAAAD,QAAA,gBAEtEnE,OAAA;kBAAKoE,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCnE,OAAA;oBAAKoE,SAAS,EAAC,yGAAyG;oBAAAD,QAAA,eACtHnE,OAAA,CAACN,MAAM;sBAAC0E,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNxE,OAAA;oBAAIoE,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACNxE,OAAA;kBAAIoE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAC7CnE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAmE,QAAA,GAAM,cAAY,eAAAnE,OAAA;wBAAAmE,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,sBAAkB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAAwC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAAqC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAE2B,CAAC,EAAE;gBAAG,CAAE;gBAC/B1B,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAE2B,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,0DAA0D;gBAAAD,QAAA,gBAEpEnE,OAAA;kBAAKoE,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCnE,OAAA;oBAAKoE,SAAS,EAAC,wGAAwG;oBAAAD,QAAA,eACrHnE,OAAA,CAACH,UAAU;sBAACuE,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNxE,OAAA;oBAAIoE,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACNxE,OAAA;kBAAIoE,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAC7CnE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAmE,QAAA,GAAM,aAAW,eAAAnE,OAAA;wBAAAmE,QAAA,EAAQ;sBAAW;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,oBAAgB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAAuC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLxE,OAAA;oBAAIoE,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC9BnE,OAAA;sBAAMoE,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CxE,OAAA;sBAAAmE,QAAA,EAAM;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAG,CAAE;cAC/BwD,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEvD,CAAC,EAAE;cAAE,CAAE;cAC9ByD,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1Cd,SAAS,EAAC,iEAAiE;cAAAD,QAAA,gBAE3EnE,OAAA;gBAAIoE,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvFxE,OAAA;gBAAKoE,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EACnD,CACC;kBAAEP,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,aAAa;kBAAE2C,IAAI,EAAE;gBAAkB,CAAC,EAC7D;kBAAE5C,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,mBAAmB;kBAAE2C,IAAI,EAAE;gBAAkB,CAAC,EACnE;kBAAE5C,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,gBAAgB;kBAAE2C,IAAI,EAAE;gBAAqB,CAAC,EACnE;kBAAE5C,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,oBAAoB;kBAAE2C,IAAI,EAAE;gBAAiB,CAAC,CACpE,CAACV,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBACnBhG,OAAA,CAAChB,MAAM,CAAC0F,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAI,CAAE;kBACpCJ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAE,CAAE;kBAClCH,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;kBAAO,CAAE;kBAC1D5B,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAEzDnE,OAAA;oBAAKoE,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAEmC,OAAO,CAAC1C;kBAAI;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDxE,OAAA;oBAAKoE,SAAS,EAAC,qCAAqC;oBAAAD,QAAA,EAAEmC,OAAO,CAACzC;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1ExE,OAAA;oBAAKoE,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAEmC,OAAO,CAACE;kBAAI;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GARtDwB,KAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAG,CAAE;YAC/BwD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAE,CAAE;YAC9ByD,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,wFAAwF;YAAAD,QAAA,gBAElGnE,OAAA;cAAIoE,SAAS,EAAC,kDAAkD;cAAAD,QAAA,EAAC;YAEjE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAKoE,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAClE,CACC;gBAAEP,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE2C,IAAI,EAAE;cAA6B,CAAC,EAC3E;gBAAE5C,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,iBAAiB;gBAAE2C,IAAI,EAAE;cAA0B,CAAC,EACzE;gBAAE5C,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE2C,IAAI,EAAE;cAA2B,CAAC,EACzE;gBAAE5C,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAE2C,IAAI,EAAE;cAAmB,CAAC,CAClE,CAACV,GAAG,CAAC,CAACW,OAAO,EAAET,KAAK,kBACnBhG,OAAA,CAAChB,MAAM,CAAC0F,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEK,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBAClCH,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;gBAAO,CAAE;gBAC1D5B,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,gBAEzDnE,OAAA;kBAAKoE,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAEsC,OAAO,CAAC7C;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDxE,OAAA;kBAAKoE,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAEsC,OAAO,CAAC5C;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvExE,OAAA;kBAAKoE,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAEsC,OAAO,CAACD;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GARtDwB,KAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAG,CAAE;YAC/BwD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEvD,CAAC,EAAE;YAAE,CAAE;YAC9ByD,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,0FAA0F;YAAAD,QAAA,gBAEpGnE,OAAA;cAAIoE,SAAS,EAAC,wBAAwB;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DxE,OAAA;cAAGoE,SAAS,EAAC,oBAAoB;cAAAD,QAAA,EAAC;YAAoD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1FxE,OAAA;cAAKoE,SAAS,EAAC,6DAA6D;cAAAD,QAAA,gBAC1EnE,OAAA,CAACf,IAAI;gBAACiH,EAAE,EAAC,WAAW;gBAAA/B,QAAA,eAClBnE,OAAA,CAAChB,MAAM,CAACmH,MAAM;kBACZ/B,SAAS,EAAC,6HAA6H;kBACvIgC,UAAU,EAAE;oBAAEnB,KAAK,EAAE;kBAAK,CAAE;kBAC5BoB,QAAQ,EAAE;oBAAEpB,KAAK,EAAE;kBAAK,CAAE;kBAAAd,QAAA,gBAE1BnE,OAAA;oBAAAmE,QAAA,EAAM;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCxE,OAAA,CAACT,YAAY;oBAAC6E,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPxE,OAAA;gBAAKoE,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAEvC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZjE,WAAW,iBACVP,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAE8B,MAAM,EAAE;QAAE,CAAE;QACnC7B,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAE8B,MAAM,EAAE;QAAO,CAAE;QACxC5B,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BX,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEnDnE,OAAA;UAAIoE,SAAS,EAAC,sCAAsC;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzExE,OAAA;UAAKoE,SAAS,EAAC,WAAW;UAAAD,QAAA,GAAA7D,qBAAA,GACvBJ,MAAM,CAACyG,eAAe,cAAArG,qBAAA,uBAAtBA,qBAAA,CAAwBwF,GAAG,CAAC,CAACc,CAAC,EAAEZ,KAAK,kBACpChG,OAAA;YAAiBoE,SAAS,EAAG,2BAC3BwC,CAAC,CAACC,SAAS,GAAG,8BAA8B,GAAG,0BAChD,EAAE;YAAA1C,QAAA,eACDnE,OAAA;cAAKoE,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzCnE,OAAA;gBAAKoE,SAAS,EAAG,yDACfwC,CAAC,CAACC,SAAS,GAAG,yBAAyB,GAAG,uBAC3C,EAAE;gBAAA1C,QAAA,EACAyC,CAAC,CAACC,SAAS,gBAAG7G,OAAA,CAACb,OAAO;kBAACiF,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACZ,GAAG;kBAACgF,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNxE,OAAA;gBAAKoE,SAAS,EAAC,QAAQ;gBAAAD,QAAA,gBACrBnE,OAAA;kBAAGoE,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAEyC,CAAC,CAACE;gBAAQ;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DxE,OAAA;kBAAKoE,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChCnE,OAAA;oBAAAmE,QAAA,gBACEnE,OAAA;sBAAMoE,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnDxE,OAAA;sBAAMoE,SAAS,EAAG,oBAAmBwC,CAAC,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;sBAAA1C,QAAA,EACpFyC,CAAC,CAACG,UAAU,IAAI;oBAAc;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACH,CAACoC,CAAC,CAACC,SAAS,iBACX7G,OAAA;oBAAAmE,QAAA,gBACEnE,OAAA;sBAAMoE,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtDxE,OAAA;sBAAMoE,SAAS,EAAC,iCAAiC;sBAAAD,QAAA,EAAEyC,CAAC,CAACI;oBAAa;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA1BEwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAKDxE,OAAA,CAAChB,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAG,CAAE;QAC/BwD,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEvD,CAAC,EAAE;QAAE,CAAE;QAC9ByD,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,gDAAgD;QAAAD,QAAA,gBAE1DnE,OAAA;UACEiG,OAAO,EAAE9F,YAAa;UACtBiE,SAAS,EAAC,0GAA0G;UAAAD,QAAA,EACrH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETxE,OAAA,CAACf,IAAI;UAACiH,EAAE,EAAC,GAAG;UAAA/B,QAAA,eACVnE,OAAA;YAAQoE,SAAS,EAAC,6FAA6F;YAAAD,QAAA,EAAC;UAEhH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbxE,OAAA;QAAKoE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC/BnE,OAAA;UAAMoE,SAAS,EAAC,uFAAuF;UAAAD,QAAA,EAAC;QAExG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAxnBIJ,eAAe;AAAAgH,EAAA,GAAfhH,eAAe;AA0nBrB,eAAeA,eAAe;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}