{"ast": null, "code": "// canvas-confetti v1.9.3 built on 2024-04-30T22:19:17.794Z\nvar module = {};\n\n// source content\n/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(global.Worker && global.Blob && global.Promise && global.OffscreenCanvas && global.OffscreenCanvasRenderingContext2D && global.HTMLCanvasElement && global.HTMLCanvasElement.prototype.transferControlToOffscreen && global.URL && global.URL.createObjectURL);\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }();\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n    func(noop, noop);\n    return null;\n  }\n  var bitmapMapper = function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function (bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n        map.set(bitmap, canvas);\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  }(canDrawBitmap, new Map());\n  var raf = function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n    return {\n      frame: frame,\n      cancel: cancel\n    };\n  }();\n  var getWorker = function () {\n    var worker;\n    var prom;\n    var resolves = {};\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({\n          options: options || {},\n          callback: callback\n        });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({\n          canvas: offscreen\n        }, [offscreen]);\n      };\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n        var id = Math.random().toString(36).slice(2);\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n            prom = null;\n            bitmapMapper.clear();\n            done();\n            resolve();\n          }\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n          resolves[id] = workerDone.bind(null, {\n            data: {\n              callback: id\n            }\n          });\n        });\n        return prom;\n      };\n      worker.reset = function resetWorker() {\n        worker.postMessage({\n          reset: true\n        });\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n    return function () {\n      if (worker) {\n        return worker;\n      }\n      if (!isWorker && canUseWorker) {\n        var code = ['var CONFETTI, SIZE = {}, module = {};', '(' + main.toString() + ')(this, module, true, SIZE);', 'onmessage = function(msg) {', '  if (msg.data.options) {', '    CONFETTI(msg.data.options).then(function () {', '      if (msg.data.callback) {', '        postMessage({ callback: msg.data.callback });', '      }', '    });', '  } else if (msg.data.reset) {', '    CONFETTI && CONFETTI.reset();', '  } else if (msg.data.resize) {', '    SIZE.width = msg.data.resize.width;', '    SIZE.height = msg.data.resize.height;', '  } else if (msg.data.canvas) {', '    SIZE.width = msg.data.canvas.width;', '    SIZE.height = msg.data.canvas.height;', '    CONFETTI = module.exports.create(msg.data.canvas);', '  }', '}'].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n          return null;\n        }\n        decorate(worker);\n      }\n      return worker;\n    };\n  }();\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: ['#26ccff', '#a25afd', '#ff5e7e', '#88ff5a', '#fcff42', '#ffa62d', '#ff36ff'],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n  function prop(options, name, transform) {\n    return convert(options && isOk(options[name]) ? options[name] : defaults[name], transform);\n  }\n  function onlyPositiveInt(number) {\n    return number < 0 ? 0 : Math.floor(number);\n  }\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n    if (val.length < 6) {\n      val = val[0] + val[0] + val[1] + val[1] + val[2] + val[2];\n    }\n    return {\n      r: toDecimal(val.substring(0, 2)),\n      g: toDecimal(val.substring(2, 4)),\n      b: toDecimal(val.substring(4, 6))\n    };\n  }\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n    return origin;\n  }\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n    return canvas;\n  }\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: opts.startVelocity * 0.5 + Math.random() * opts.startVelocity,\n      angle2D: -radAngle + (0.5 * radSpread - Math.random() * radSpread),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + 10 * fetti.scalar;\n      fetti.wobbleY = fetti.y + 10 * fetti.scalar;\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + 10 * fetti.scalar * Math.cos(fetti.wobble);\n      fetti.wobbleY = fetti.y + 10 * fetti.scalar * Math.sin(fetti.wobble);\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n    var progress = fetti.tick++ / fetti.totalTicks;\n    var x1 = fetti.x + fetti.random * fetti.tiltCos;\n    var y1 = fetti.y + fetti.random * fetti.tiltSin;\n    var x2 = fetti.wobbleX + fetti.random * fetti.tiltCos;\n    var y2 = fetti.wobbleY + fetti.random * fetti.tiltSin;\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n    context.beginPath();\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(fetti.shape.path, fetti.shape.matrix, fetti.x, fetti.y, Math.abs(x2 - x1) * 0.1, Math.abs(y2 - y1) * 0.1, Math.PI / 10 * fetti.wobble));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n      var matrix = new DOMMatrix([Math.cos(rotation) * scaleX, Math.sin(rotation) * scaleX, -Math.sin(rotation) * scaleY, Math.cos(rotation) * scaleY, fetti.x, fetti.y]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n      context.globalAlpha = 1 - progress;\n      context.fillStyle = pattern;\n      context.fillRect(fetti.x - width / 2, fetti.y - height / 2, width, height);\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ? context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) : ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n    context.closePath();\n    context.fill();\n    return fetti.tick < fetti.totalTicks;\n  }\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n        done();\n        resolve();\n      }\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n        context.clearRect(0, 0, size.width, size.height);\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = canvas && worker ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n      var temp = particleCount;\n      var fettis = [];\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n      while (temp--) {\n        fettis.push(randomPhysics({\n          x: startX,\n          y: startY,\n          angle: angle,\n          spread: spread,\n          startVelocity: startVelocity,\n          color: colors[temp % colors.length],\n          shape: shapes[randomInt(0, shapes.length)],\n          ticks: ticks,\n          decay: decay,\n          gravity: gravity,\n          drift: drift,\n          scalar: scalar,\n          flat: flat\n        }));\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n      animationObj = animate(canvas, fettis, resizer, size, done);\n      return animationObj.promise;\n    }\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n      initialized = true;\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n          resizer(obj);\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n      function done() {\n        animationObj = null;\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas);\n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n      return fireLocal(options, size, done);\n    }\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, {\n        useWorker: true,\n        resize: true\n      });\n    }\n    return defaultFire;\n  }\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([Math.cos(rotation) * scaleX, Math.sin(rotation) * scaleX, -Math.sin(rotation) * scaleY, Math.cos(rotation) * scaleY, x, y]));\n    return t2;\n  }\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n    var path, matrix;\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n      width = maxX - minX;\n      height = maxY - minY;\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize / width, maxDesiredSize / height);\n      matrix = [scale, 0, 0, scale, -Math.round(width / 2 + minX) * scale, -Math.round(height / 2 + minY) * scale];\n    }\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n  function shapeFromText(textData) {\n    var text,\n      scalar = 1,\n      color = '#000000',\n      // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n      fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n    ctx.fillText(text, x, y);\n    var scale = 1 / scalar;\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n  module.exports = function () {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function () {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n})(function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  return this || {};\n}(), module, false);\n\n// end source content\n\nexport default module.exports;\nexport var create = module.exports.create;", "map": {"version": 3, "names": ["module", "main", "global", "isWorker", "workerSize", "canUseWorker", "Worker", "Blob", "Promise", "OffscreenCanvas", "OffscreenCanvasRenderingContext2D", "HTMLCanvasElement", "prototype", "transferControlToOffscreen", "URL", "createObjectURL", "canUsePaths", "Path2D", "DOMMatrix", "canDrawBitmap", "canvas", "ctx", "getContext", "fillRect", "bitmap", "transferToImageBitmap", "createPattern", "e", "noop", "promise", "func", "ModulePromise", "exports", "Prom", "bitmapMapper", "skipTransform", "map", "transform", "has", "get", "width", "height", "drawImage", "set", "clear", "Map", "raf", "TIME", "Math", "floor", "frame", "cancel", "frames", "lastFrameTime", "requestAnimationFrame", "cancelAnimationFrame", "cb", "id", "random", "onFrame", "time", "setTimeout", "timer", "clearTimeout", "getWorker", "worker", "prom", "resolves", "decorate", "execute", "options", "callback", "postMessage", "init", "initWorker", "offscreen", "fire", "fireWorker", "size", "done", "toString", "slice", "resolve", "workerDone", "msg", "data", "removeEventListener", "addEventListener", "bind", "reset", "resetWorker", "code", "join", "console", "undefined", "warn", "defaults", "particleCount", "angle", "spread", "startVelocity", "decay", "gravity", "drift", "ticks", "x", "y", "shapes", "zIndex", "colors", "disableForReducedMotion", "scalar", "convert", "val", "isOk", "prop", "name", "onlyPositiveInt", "number", "randomInt", "min", "max", "toDecimal", "str", "parseInt", "colorsToRgb", "hexToRgb", "String", "replace", "length", "r", "substring", "g", "b", "<PERSON><PERSON><PERSON><PERSON>", "origin", "Object", "Number", "setCanvasWindowSize", "document", "documentElement", "clientWidth", "clientHeight", "setCanvasRectSize", "rect", "getBoundingClientRect", "get<PERSON>anvas", "createElement", "style", "position", "top", "left", "pointerEvents", "ellipse", "context", "radiusX", "radiusY", "rotation", "startAngle", "endAngle", "antiClockwise", "save", "translate", "rotate", "scale", "arc", "restore", "randomPhysics", "opts", "radAngle", "PI", "radSpread", "wobble", "wobbleSpeed", "velocity", "angle2D", "tiltAngle", "color", "shape", "tick", "totalTicks", "tiltSin", "tiltCos", "wobbleX", "wobbleY", "ovalScalar", "flat", "updateFetti", "fetti", "cos", "sin", "progress", "x1", "y1", "x2", "y2", "fillStyle", "beginPath", "type", "path", "Array", "isArray", "matrix", "fill", "transformPath2D", "abs", "scaleX", "scaleY", "multiplySelf", "pattern", "setTransform", "globalAlpha", "rot", "innerRadius", "outerRadius", "spikes", "step", "lineTo", "moveTo", "closePath", "animate", "fettis", "resizer", "animating<PERSON>ettis", "animationFrame", "destroy", "onDone", "clearRect", "update", "filter", "addFettis", "concat", "confettiCannon", "globalOpts", "isLibCanvas", "allowResize", "hasResizeEventRegistered", "globalDisableForReducedMotion", "Boolean", "shouldUseWorker", "initialized", "__confetti_initialized", "preferLessMotion", "matchMedia", "matches", "animationObj", "fireLocal", "temp", "startX", "startY", "push", "body", "append<PERSON><PERSON><PERSON>", "onResize", "obj", "resize", "contains", "<PERSON><PERSON><PERSON><PERSON>", "defaultFire", "getDefaultFire", "useWorker", "pathString", "pathMatrix", "path2d", "t1", "addPath", "t2", "shapeFromPath", "pathData", "Error", "tempCanvas", "tempCtx", "maxSize", "minX", "minY", "maxX", "maxY", "isPointInPath", "maxDesiredSize", "round", "shapeFromText", "textData", "text", "fontFamily", "fontSize", "font", "measureText", "ceil", "actualBoundingBoxRight", "actualBoundingBoxLeft", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "padding", "fillText", "apply", "arguments", "create", "window", "self"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/canvas-confetti/dist/confetti.module.mjs"], "sourcesContent": ["// canvas-confetti v1.9.3 built on 2024-04-30T22:19:17.794Z\nvar module = {};\n\n// source content\n/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(\n    global.Worker &&\n    global.Blob &&\n    global.Promise &&\n    global.OffscreenCanvas &&\n    global.OffscreenCanvasRenderingContext2D &&\n    global.HTMLCanvasElement &&\n    global.HTMLCanvasElement.prototype.transferControlToOffscreen &&\n    global.URL &&\n    global.URL.createObjectURL);\n\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = (function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n\n    return true;\n  })();\n\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n\n    func(noop, noop);\n\n    return null;\n  }\n\n  var bitmapMapper = (function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function(bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n\n        map.set(bitmap, canvas);\n\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  })(canDrawBitmap, new Map());\n\n  var raf = (function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n\n    return { frame: frame, cancel: cancel };\n  }());\n\n  var getWorker = (function () {\n    var worker;\n    var prom;\n    var resolves = {};\n\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({ options: options || {}, callback: callback });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({ canvas: offscreen }, [offscreen]);\n      };\n\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n\n        var id = Math.random().toString(36).slice(2);\n\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n\n            prom = null;\n\n            bitmapMapper.clear();\n\n            done();\n            resolve();\n          }\n\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n\n          resolves[id] = workerDone.bind(null, { data: { callback: id }});\n        });\n\n        return prom;\n      };\n\n      worker.reset = function resetWorker() {\n        worker.postMessage({ reset: true });\n\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n\n    return function () {\n      if (worker) {\n        return worker;\n      }\n\n      if (!isWorker && canUseWorker) {\n        var code = [\n          'var CONFETTI, SIZE = {}, module = {};',\n          '(' + main.toString() + ')(this, module, true, SIZE);',\n          'onmessage = function(msg) {',\n          '  if (msg.data.options) {',\n          '    CONFETTI(msg.data.options).then(function () {',\n          '      if (msg.data.callback) {',\n          '        postMessage({ callback: msg.data.callback });',\n          '      }',\n          '    });',\n          '  } else if (msg.data.reset) {',\n          '    CONFETTI && CONFETTI.reset();',\n          '  } else if (msg.data.resize) {',\n          '    SIZE.width = msg.data.resize.width;',\n          '    SIZE.height = msg.data.resize.height;',\n          '  } else if (msg.data.canvas) {',\n          '    SIZE.width = msg.data.canvas.width;',\n          '    SIZE.height = msg.data.canvas.height;',\n          '    CONFETTI = module.exports.create(msg.data.canvas);',\n          '  }',\n          '}',\n        ].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n\n          return null;\n        }\n\n        decorate(worker);\n      }\n\n      return worker;\n    };\n  })();\n\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: [\n      '#26ccff',\n      '#a25afd',\n      '#ff5e7e',\n      '#88ff5a',\n      '#fcff42',\n      '#ffa62d',\n      '#ff36ff'\n    ],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n\n  function prop(options, name, transform) {\n    return convert(\n      options && isOk(options[name]) ? options[name] : defaults[name],\n      transform\n    );\n  }\n\n  function onlyPositiveInt(number){\n    return number < 0 ? 0 : Math.floor(number);\n  }\n\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n\n    if (val.length < 6) {\n        val = val[0]+val[0]+val[1]+val[1]+val[2]+val[2];\n    }\n\n    return {\n      r: toDecimal(val.substring(0,2)),\n      g: toDecimal(val.substring(2,4)),\n      b: toDecimal(val.substring(4,6))\n    };\n  }\n\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n\n    return origin;\n  }\n\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n\n    return canvas;\n  }\n\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: (opts.startVelocity * 0.5) + (Math.random() * opts.startVelocity),\n      angle2D: -radAngle + ((0.5 * radSpread) - (Math.random() * radSpread)),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + (10 * fetti.scalar);\n      fetti.wobbleY = fetti.y + (10 * fetti.scalar);\n\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + ((10 * fetti.scalar) * Math.cos(fetti.wobble));\n      fetti.wobbleY = fetti.y + ((10 * fetti.scalar) * Math.sin(fetti.wobble));\n\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n\n    var progress = (fetti.tick++) / fetti.totalTicks;\n\n    var x1 = fetti.x + (fetti.random * fetti.tiltCos);\n    var y1 = fetti.y + (fetti.random * fetti.tiltSin);\n    var x2 = fetti.wobbleX + (fetti.random * fetti.tiltCos);\n    var y2 = fetti.wobbleY + (fetti.random * fetti.tiltSin);\n\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n\n    context.beginPath();\n\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(\n        fetti.shape.path,\n        fetti.shape.matrix,\n        fetti.x,\n        fetti.y,\n        Math.abs(x2 - x1) * 0.1,\n        Math.abs(y2 - y1) * 0.1,\n        Math.PI / 10 * fetti.wobble\n      ));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n\n      var matrix = new DOMMatrix([\n        Math.cos(rotation) * scaleX,\n        Math.sin(rotation) * scaleX,\n        -Math.sin(rotation) * scaleY,\n        Math.cos(rotation) * scaleY,\n        fetti.x,\n        fetti.y\n      ]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n\n      context.globalAlpha = (1 - progress);\n      context.fillStyle = pattern;\n      context.fillRect(\n        fetti.x - (width / 2),\n        fetti.y - (height / 2),\n        width,\n        height\n      );\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ?\n        context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) :\n        ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n\n    context.closePath();\n    context.fill();\n\n    return fetti.tick < fetti.totalTicks;\n  }\n\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n\n        done();\n        resolve();\n      }\n\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n\n        context.clearRect(0, 0, size.width, size.height);\n\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = (canvas && worker) ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n\n      var temp = particleCount;\n      var fettis = [];\n\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n\n      while (temp--) {\n        fettis.push(\n          randomPhysics({\n            x: startX,\n            y: startY,\n            angle: angle,\n            spread: spread,\n            startVelocity: startVelocity,\n            color: colors[temp % colors.length],\n            shape: shapes[randomInt(0, shapes.length)],\n            ticks: ticks,\n            decay: decay,\n            gravity: gravity,\n            drift: drift,\n            scalar: scalar,\n            flat: flat\n          })\n        );\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n\n      animationObj = animate(canvas, fettis, resizer, size , done);\n\n      return animationObj.promise;\n    }\n\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n\n      initialized = true;\n\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n\n          resizer(obj);\n\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n\n      function done() {\n        animationObj = null;\n\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas); \n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n\n      return fireLocal(options, size, done);\n    }\n\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, { useWorker: true, resize: true });\n    }\n    return defaultFire;\n  }\n\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([\n      Math.cos(rotation) * scaleX,\n      Math.sin(rotation) * scaleX,\n      -Math.sin(rotation) * scaleY,\n      Math.cos(rotation) * scaleY,\n      x,\n      y\n    ]));\n\n    return t2;\n  }\n\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n\n    var path, matrix;\n\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n\n      width = maxX - minX;\n      height = maxY - minY;\n\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize/width, maxDesiredSize/height);\n\n      matrix = [\n        scale, 0, 0, scale,\n        -Math.round((width/2) + minX) * scale,\n        -Math.round((height/2) + minY) * scale\n      ];\n    }\n\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n\n  function shapeFromText(textData) {\n    var text,\n        scalar = 1,\n        color = '#000000',\n        // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n        fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n\n    ctx.fillText(text, x, y);\n\n    var scale = 1 / scalar;\n\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n\n  module.exports = function() {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function() {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n}((function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n\n  return this || {};\n})(), module, false));\n\n// end source content\n\nexport default module.exports;\nexport var create = module.exports.create;\n"], "mappings": "AAAA;AACA,IAAIA,MAAM,GAAG,CAAC,CAAC;;AAEf;AACA;;AAEC,UAASC,IAAIA,CAACC,MAAM,EAAEF,MAAM,EAAEG,QAAQ,EAAEC,UAAU,EAAE;EACnD,IAAIC,YAAY,GAAG,CAAC,EAClBH,MAAM,CAACI,MAAM,IACbJ,MAAM,CAACK,IAAI,IACXL,MAAM,CAACM,OAAO,IACdN,MAAM,CAACO,eAAe,IACtBP,MAAM,CAACQ,iCAAiC,IACxCR,MAAM,CAACS,iBAAiB,IACxBT,MAAM,CAACS,iBAAiB,CAACC,SAAS,CAACC,0BAA0B,IAC7DX,MAAM,CAACY,GAAG,IACVZ,MAAM,CAACY,GAAG,CAACC,eAAe,CAAC;EAE7B,IAAIC,WAAW,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOC,SAAS,KAAK,UAAU;EACjF,IAAIC,aAAa,GAAI,YAAY;IAC/B;IACA,IAAI,CAACjB,MAAM,CAACO,eAAe,EAAE;MAC3B,OAAO,KAAK;IACd;IAEA,IAAIW,MAAM,GAAG,IAAIX,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC,IAAIY,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACjCD,GAAG,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,IAAIC,MAAM,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;IAE3C,IAAI;MACFJ,GAAG,CAACK,aAAa,CAACF,MAAM,EAAE,WAAW,CAAC;IACxC,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAE,CAAC;EAEJ,SAASC,IAAIA,CAAA,EAAG,CAAC;;EAEjB;EACA;EACA,SAASC,OAAOA,CAACC,IAAI,EAAE;IACrB,IAAIC,aAAa,GAAG/B,MAAM,CAACgC,OAAO,CAACxB,OAAO;IAC1C,IAAIyB,IAAI,GAAGF,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG7B,MAAM,CAACM,OAAO;IAEpE,IAAI,OAAOyB,IAAI,KAAK,UAAU,EAAE;MAC9B,OAAO,IAAIA,IAAI,CAACH,IAAI,CAAC;IACvB;IAEAA,IAAI,CAACF,IAAI,EAAEA,IAAI,CAAC;IAEhB,OAAO,IAAI;EACb;EAEA,IAAIM,YAAY,GAAI,UAAUC,aAAa,EAAEC,GAAG,EAAE;IAChD;IACA;IACA;IACA;IACA;IACA,OAAO;MACLC,SAAS,EAAE,SAAAA,CAASb,MAAM,EAAE;QAC1B,IAAIW,aAAa,EAAE;UACjB,OAAOX,MAAM;QACf;QAEA,IAAIY,GAAG,CAACE,GAAG,CAACd,MAAM,CAAC,EAAE;UACnB,OAAOY,GAAG,CAACG,GAAG,CAACf,MAAM,CAAC;QACxB;QAEA,IAAIJ,MAAM,GAAG,IAAIX,eAAe,CAACe,MAAM,CAACgB,KAAK,EAAEhB,MAAM,CAACiB,MAAM,CAAC;QAC7D,IAAIpB,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACjCD,GAAG,CAACqB,SAAS,CAAClB,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QAE3BY,GAAG,CAACO,GAAG,CAACnB,MAAM,EAAEJ,MAAM,CAAC;QAEvB,OAAOA,MAAM;MACf,CAAC;MACDwB,KAAK,EAAE,SAAAA,CAAA,EAAY;QACjBR,GAAG,CAACQ,KAAK,CAAC,CAAC;MACb;IACF,CAAC;EACH,CAAC,CAAEzB,aAAa,EAAE,IAAI0B,GAAG,CAAC,CAAC,CAAC;EAE5B,IAAIC,GAAG,GAAI,YAAY;IACrB,IAAIC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;IAChC,IAAIC,KAAK,EAAEC,MAAM;IACjB,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,IAAIC,aAAa,GAAG,CAAC;IAErB,IAAI,OAAOC,qBAAqB,KAAK,UAAU,IAAI,OAAOC,oBAAoB,KAAK,UAAU,EAAE;MAC7FL,KAAK,GAAG,SAAAA,CAAUM,EAAE,EAAE;QACpB,IAAIC,EAAE,GAAGT,IAAI,CAACU,MAAM,CAAC,CAAC;QAEtBN,MAAM,CAACK,EAAE,CAAC,GAAGH,qBAAqB,CAAC,SAASK,OAAOA,CAACC,IAAI,EAAE;UACxD,IAAIP,aAAa,KAAKO,IAAI,IAAIP,aAAa,GAAGN,IAAI,GAAG,CAAC,GAAGa,IAAI,EAAE;YAC7DP,aAAa,GAAGO,IAAI;YACpB,OAAOR,MAAM,CAACK,EAAE,CAAC;YAEjBD,EAAE,CAAC,CAAC;UACN,CAAC,MAAM;YACLJ,MAAM,CAACK,EAAE,CAAC,GAAGH,qBAAqB,CAACK,OAAO,CAAC;UAC7C;QACF,CAAC,CAAC;QAEF,OAAOF,EAAE;MACX,CAAC;MACDN,MAAM,GAAG,SAAAA,CAAUM,EAAE,EAAE;QACrB,IAAIL,MAAM,CAACK,EAAE,CAAC,EAAE;UACdF,oBAAoB,CAACH,MAAM,CAACK,EAAE,CAAC,CAAC;QAClC;MACF,CAAC;IACH,CAAC,MAAM;MACLP,KAAK,GAAG,SAAAA,CAAUM,EAAE,EAAE;QACpB,OAAOK,UAAU,CAACL,EAAE,EAAET,IAAI,CAAC;MAC7B,CAAC;MACDI,MAAM,GAAG,SAAAA,CAAUW,KAAK,EAAE;QACxB,OAAOC,YAAY,CAACD,KAAK,CAAC;MAC5B,CAAC;IACH;IAEA,OAAO;MAAEZ,KAAK,EAAEA,KAAK;MAAEC,MAAM,EAAEA;IAAO,CAAC;EACzC,CAAC,CAAC,CAAE;EAEJ,IAAIa,SAAS,GAAI,YAAY;IAC3B,IAAIC,MAAM;IACV,IAAIC,IAAI;IACR,IAAIC,QAAQ,GAAG,CAAC,CAAC;IAEjB,SAASC,QAAQA,CAACH,MAAM,EAAE;MACxB,SAASI,OAAOA,CAACC,OAAO,EAAEC,QAAQ,EAAE;QAClCN,MAAM,CAACO,WAAW,CAAC;UAAEF,OAAO,EAAEA,OAAO,IAAI,CAAC,CAAC;UAAEC,QAAQ,EAAEA;QAAS,CAAC,CAAC;MACpE;MACAN,MAAM,CAACQ,IAAI,GAAG,SAASC,UAAUA,CAACtD,MAAM,EAAE;QACxC,IAAIuD,SAAS,GAAGvD,MAAM,CAACP,0BAA0B,CAAC,CAAC;QACnDoD,MAAM,CAACO,WAAW,CAAC;UAAEpD,MAAM,EAAEuD;QAAU,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;MACxD,CAAC;MAEDV,MAAM,CAACW,IAAI,GAAG,SAASC,UAAUA,CAACP,OAAO,EAAEQ,IAAI,EAAEC,IAAI,EAAE;QACrD,IAAIb,IAAI,EAAE;UACRG,OAAO,CAACC,OAAO,EAAE,IAAI,CAAC;UACtB,OAAOJ,IAAI;QACb;QAEA,IAAIT,EAAE,GAAGT,IAAI,CAACU,MAAM,CAAC,CAAC,CAACsB,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QAE5Cf,IAAI,GAAGrC,OAAO,CAAC,UAAUqD,OAAO,EAAE;UAChC,SAASC,UAAUA,CAACC,GAAG,EAAE;YACvB,IAAIA,GAAG,CAACC,IAAI,CAACd,QAAQ,KAAKd,EAAE,EAAE;cAC5B;YACF;YAEA,OAAOU,QAAQ,CAACV,EAAE,CAAC;YACnBQ,MAAM,CAACqB,mBAAmB,CAAC,SAAS,EAAEH,UAAU,CAAC;YAEjDjB,IAAI,GAAG,IAAI;YAEXhC,YAAY,CAACU,KAAK,CAAC,CAAC;YAEpBmC,IAAI,CAAC,CAAC;YACNG,OAAO,CAAC,CAAC;UACX;UAEAjB,MAAM,CAACsB,gBAAgB,CAAC,SAAS,EAAEJ,UAAU,CAAC;UAC9Cd,OAAO,CAACC,OAAO,EAAEb,EAAE,CAAC;UAEpBU,QAAQ,CAACV,EAAE,CAAC,GAAG0B,UAAU,CAACK,IAAI,CAAC,IAAI,EAAE;YAAEH,IAAI,EAAE;cAAEd,QAAQ,EAAEd;YAAG;UAAC,CAAC,CAAC;QACjE,CAAC,CAAC;QAEF,OAAOS,IAAI;MACb,CAAC;MAEDD,MAAM,CAACwB,KAAK,GAAG,SAASC,WAAWA,CAAA,EAAG;QACpCzB,MAAM,CAACO,WAAW,CAAC;UAAEiB,KAAK,EAAE;QAAK,CAAC,CAAC;QAEnC,KAAK,IAAIhC,EAAE,IAAIU,QAAQ,EAAE;UACvBA,QAAQ,CAACV,EAAE,CAAC,CAAC,CAAC;UACd,OAAOU,QAAQ,CAACV,EAAE,CAAC;QACrB;MACF,CAAC;IACH;IAEA,OAAO,YAAY;MACjB,IAAIQ,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;MAEA,IAAI,CAAC9D,QAAQ,IAAIE,YAAY,EAAE;QAC7B,IAAIsF,IAAI,GAAG,CACT,uCAAuC,EACvC,GAAG,GAAG1F,IAAI,CAAC+E,QAAQ,CAAC,CAAC,GAAG,8BAA8B,EACtD,6BAA6B,EAC7B,2BAA2B,EAC3B,mDAAmD,EACnD,gCAAgC,EAChC,uDAAuD,EACvD,SAAS,EACT,SAAS,EACT,gCAAgC,EAChC,mCAAmC,EACnC,iCAAiC,EACjC,yCAAyC,EACzC,2CAA2C,EAC3C,iCAAiC,EACjC,yCAAyC,EACzC,2CAA2C,EAC3C,wDAAwD,EACxD,KAAK,EACL,GAAG,CACJ,CAACY,IAAI,CAAC,IAAI,CAAC;QACZ,IAAI;UACF3B,MAAM,GAAG,IAAI3D,MAAM,CAACQ,GAAG,CAACC,eAAe,CAAC,IAAIR,IAAI,CAAC,CAACoF,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,OAAOhE,CAAC,EAAE;UACV;UACA,OAAOkE,OAAO,KAAKC,SAAS,IAAI,OAAOD,OAAO,CAACE,IAAI,KAAK,UAAU,GAAGF,OAAO,CAACE,IAAI,CAAC,0BAA0B,EAAEpE,CAAC,CAAC,GAAG,IAAI;UAEvH,OAAO,IAAI;QACb;QAEAyC,QAAQ,CAACH,MAAM,CAAC;MAClB;MAEA,OAAOA,MAAM;IACf,CAAC;EACH,CAAC,CAAE,CAAC;EAEJ,IAAI+B,QAAQ,GAAG;IACbC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,CAAC;IACVC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,GAAG;IACVC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC5BC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,CACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD;IACAC,uBAAuB,EAAE,KAAK;IAC9BC,MAAM,EAAE;EACV,CAAC;EAED,SAASC,OAAOA,CAACC,GAAG,EAAE5E,SAAS,EAAE;IAC/B,OAAOA,SAAS,GAAGA,SAAS,CAAC4E,GAAG,CAAC,GAAGA,GAAG;EACzC;EAEA,SAASC,IAAIA,CAACD,GAAG,EAAE;IACjB,OAAO,EAAEA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKnB,SAAS,CAAC;EAC7C;EAEA,SAASqB,IAAIA,CAAC7C,OAAO,EAAE8C,IAAI,EAAE/E,SAAS,EAAE;IACtC,OAAO2E,OAAO,CACZ1C,OAAO,IAAI4C,IAAI,CAAC5C,OAAO,CAAC8C,IAAI,CAAC,CAAC,GAAG9C,OAAO,CAAC8C,IAAI,CAAC,GAAGpB,QAAQ,CAACoB,IAAI,CAAC,EAC/D/E,SACF,CAAC;EACH;EAEA,SAASgF,eAAeA,CAACC,MAAM,EAAC;IAC9B,OAAOA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGtE,IAAI,CAACC,KAAK,CAACqE,MAAM,CAAC;EAC5C;EAEA,SAASC,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAE;IAC3B;IACA,OAAOzE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,CAAC,CAAC,IAAI+D,GAAG,GAAGD,GAAG,CAAC,CAAC,GAAGA,GAAG;EACtD;EAEA,SAASE,SAASA,CAACC,GAAG,EAAE;IACtB,OAAOC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC;EAC1B;EAEA,SAASE,WAAWA,CAAChB,MAAM,EAAE;IAC3B,OAAOA,MAAM,CAACzE,GAAG,CAAC0F,QAAQ,CAAC;EAC7B;EAEA,SAASA,QAAQA,CAACH,GAAG,EAAE;IACrB,IAAIV,GAAG,GAAGc,MAAM,CAACJ,GAAG,CAAC,CAACK,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;IAEhD,IAAIf,GAAG,CAACgB,MAAM,GAAG,CAAC,EAAE;MAChBhB,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAACA,GAAG,CAAC,CAAC,CAAC,GAACA,GAAG,CAAC,CAAC,CAAC,GAACA,GAAG,CAAC,CAAC,CAAC,GAACA,GAAG,CAAC,CAAC,CAAC,GAACA,GAAG,CAAC,CAAC,CAAC;IACnD;IAEA,OAAO;MACLiB,CAAC,EAAER,SAAS,CAACT,GAAG,CAACkB,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;MAChCC,CAAC,EAAEV,SAAS,CAACT,GAAG,CAACkB,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;MAChCE,CAAC,EAAEX,SAAS,CAACT,GAAG,CAACkB,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC;IACjC,CAAC;EACH;EAEA,SAASG,SAASA,CAAChE,OAAO,EAAE;IAC1B,IAAIiE,MAAM,GAAGpB,IAAI,CAAC7C,OAAO,EAAE,QAAQ,EAAEkE,MAAM,CAAC;IAC5CD,MAAM,CAAC9B,CAAC,GAAGU,IAAI,CAACoB,MAAM,EAAE,GAAG,EAAEE,MAAM,CAAC;IACpCF,MAAM,CAAC7B,CAAC,GAAGS,IAAI,CAACoB,MAAM,EAAE,GAAG,EAAEE,MAAM,CAAC;IAEpC,OAAOF,MAAM;EACf;EAEA,SAASG,mBAAmBA,CAACtH,MAAM,EAAE;IACnCA,MAAM,CAACoB,KAAK,GAAGmG,QAAQ,CAACC,eAAe,CAACC,WAAW;IACnDzH,MAAM,CAACqB,MAAM,GAAGkG,QAAQ,CAACC,eAAe,CAACE,YAAY;EACvD;EAEA,SAASC,iBAAiBA,CAAC3H,MAAM,EAAE;IACjC,IAAI4H,IAAI,GAAG5H,MAAM,CAAC6H,qBAAqB,CAAC,CAAC;IACzC7H,MAAM,CAACoB,KAAK,GAAGwG,IAAI,CAACxG,KAAK;IACzBpB,MAAM,CAACqB,MAAM,GAAGuG,IAAI,CAACvG,MAAM;EAC7B;EAEA,SAASyG,SAASA,CAACtC,MAAM,EAAE;IACzB,IAAIxF,MAAM,GAAGuH,QAAQ,CAACQ,aAAa,CAAC,QAAQ,CAAC;IAE7C/H,MAAM,CAACgI,KAAK,CAACC,QAAQ,GAAG,OAAO;IAC/BjI,MAAM,CAACgI,KAAK,CAACE,GAAG,GAAG,KAAK;IACxBlI,MAAM,CAACgI,KAAK,CAACG,IAAI,GAAG,KAAK;IACzBnI,MAAM,CAACgI,KAAK,CAACI,aAAa,GAAG,MAAM;IACnCpI,MAAM,CAACgI,KAAK,CAACxC,MAAM,GAAGA,MAAM;IAE5B,OAAOxF,MAAM;EACf;EAEA,SAASqI,OAAOA,CAACC,OAAO,EAAEjD,CAAC,EAAEC,CAAC,EAAEiD,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC/FN,OAAO,CAACO,IAAI,CAAC,CAAC;IACdP,OAAO,CAACQ,SAAS,CAACzD,CAAC,EAAEC,CAAC,CAAC;IACvBgD,OAAO,CAACS,MAAM,CAACN,QAAQ,CAAC;IACxBH,OAAO,CAACU,KAAK,CAACT,OAAO,EAAEC,OAAO,CAAC;IAC/BF,OAAO,CAACW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEP,UAAU,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzDN,OAAO,CAACY,OAAO,CAAC,CAAC;EACnB;EAEA,SAASC,aAAaA,CAACC,IAAI,EAAE;IAC3B,IAAIC,QAAQ,GAAGD,IAAI,CAACtE,KAAK,IAAIlD,IAAI,CAAC0H,EAAE,GAAG,GAAG,CAAC;IAC3C,IAAIC,SAAS,GAAGH,IAAI,CAACrE,MAAM,IAAInD,IAAI,CAAC0H,EAAE,GAAG,GAAG,CAAC;IAE7C,OAAO;MACLjE,CAAC,EAAE+D,IAAI,CAAC/D,CAAC;MACTC,CAAC,EAAE8D,IAAI,CAAC9D,CAAC;MACTkE,MAAM,EAAE5H,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,EAAE;MAC1BmH,WAAW,EAAE7H,IAAI,CAACwE,GAAG,CAAC,IAAI,EAAExE,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;MACvDoH,QAAQ,EAAGN,IAAI,CAACpE,aAAa,GAAG,GAAG,GAAKpD,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG8G,IAAI,CAACpE,aAAc;MAC3E2E,OAAO,EAAE,CAACN,QAAQ,IAAK,GAAG,GAAGE,SAAS,GAAK3H,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGiH,SAAU,CAAC;MACtEK,SAAS,EAAE,CAAChI,IAAI,CAACU,MAAM,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,IAAIV,IAAI,CAAC0H,EAAE;MAC3DO,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBC,KAAK,EAAEV,IAAI,CAACU,KAAK;MACjBC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAEZ,IAAI,CAAChE,KAAK;MACtBH,KAAK,EAAEmE,IAAI,CAACnE,KAAK;MACjBE,KAAK,EAAEiE,IAAI,CAACjE,KAAK;MACjB7C,MAAM,EAAEV,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,CAAC;MACzB2H,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVlF,OAAO,EAAEkE,IAAI,CAAClE,OAAO,GAAG,CAAC;MACzBmF,UAAU,EAAE,GAAG;MACf1E,MAAM,EAAEyD,IAAI,CAACzD,MAAM;MACnB2E,IAAI,EAAElB,IAAI,CAACkB;IACb,CAAC;EACH;EAEA,SAASC,WAAWA,CAACjC,OAAO,EAAEkC,KAAK,EAAE;IACnCA,KAAK,CAACnF,CAAC,IAAIzD,IAAI,CAAC6I,GAAG,CAACD,KAAK,CAACb,OAAO,CAAC,GAAGa,KAAK,CAACd,QAAQ,GAAGc,KAAK,CAACrF,KAAK;IACjEqF,KAAK,CAAClF,CAAC,IAAI1D,IAAI,CAAC8I,GAAG,CAACF,KAAK,CAACb,OAAO,CAAC,GAAGa,KAAK,CAACd,QAAQ,GAAGc,KAAK,CAACtF,OAAO;IACnEsF,KAAK,CAACd,QAAQ,IAAIc,KAAK,CAACvF,KAAK;IAE7B,IAAIuF,KAAK,CAACF,IAAI,EAAE;MACdE,KAAK,CAAChB,MAAM,GAAG,CAAC;MAChBgB,KAAK,CAACL,OAAO,GAAGK,KAAK,CAACnF,CAAC,GAAI,EAAE,GAAGmF,KAAK,CAAC7E,MAAO;MAC7C6E,KAAK,CAACJ,OAAO,GAAGI,KAAK,CAAClF,CAAC,GAAI,EAAE,GAAGkF,KAAK,CAAC7E,MAAO;MAE7C6E,KAAK,CAACP,OAAO,GAAG,CAAC;MACjBO,KAAK,CAACN,OAAO,GAAG,CAAC;MACjBM,KAAK,CAAClI,MAAM,GAAG,CAAC;IAClB,CAAC,MAAM;MACLkI,KAAK,CAAChB,MAAM,IAAIgB,KAAK,CAACf,WAAW;MACjCe,KAAK,CAACL,OAAO,GAAGK,KAAK,CAACnF,CAAC,GAAK,EAAE,GAAGmF,KAAK,CAAC7E,MAAM,GAAI/D,IAAI,CAAC6I,GAAG,CAACD,KAAK,CAAChB,MAAM,CAAE;MACxEgB,KAAK,CAACJ,OAAO,GAAGI,KAAK,CAAClF,CAAC,GAAK,EAAE,GAAGkF,KAAK,CAAC7E,MAAM,GAAI/D,IAAI,CAAC8I,GAAG,CAACF,KAAK,CAAChB,MAAM,CAAE;MAExEgB,KAAK,CAACZ,SAAS,IAAI,GAAG;MACtBY,KAAK,CAACP,OAAO,GAAGrI,IAAI,CAAC8I,GAAG,CAACF,KAAK,CAACZ,SAAS,CAAC;MACzCY,KAAK,CAACN,OAAO,GAAGtI,IAAI,CAAC6I,GAAG,CAACD,KAAK,CAACZ,SAAS,CAAC;MACzCY,KAAK,CAAClI,MAAM,GAAGV,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,CAAC;IAClC;IAEA,IAAIqI,QAAQ,GAAIH,KAAK,CAACT,IAAI,EAAE,GAAIS,KAAK,CAACR,UAAU;IAEhD,IAAIY,EAAE,GAAGJ,KAAK,CAACnF,CAAC,GAAImF,KAAK,CAAClI,MAAM,GAAGkI,KAAK,CAACN,OAAQ;IACjD,IAAIW,EAAE,GAAGL,KAAK,CAAClF,CAAC,GAAIkF,KAAK,CAAClI,MAAM,GAAGkI,KAAK,CAACP,OAAQ;IACjD,IAAIa,EAAE,GAAGN,KAAK,CAACL,OAAO,GAAIK,KAAK,CAAClI,MAAM,GAAGkI,KAAK,CAACN,OAAQ;IACvD,IAAIa,EAAE,GAAGP,KAAK,CAACJ,OAAO,GAAII,KAAK,CAAClI,MAAM,GAAGkI,KAAK,CAACP,OAAQ;IAEvD3B,OAAO,CAAC0C,SAAS,GAAG,OAAO,GAAGR,KAAK,CAACX,KAAK,CAAC/C,CAAC,GAAG,IAAI,GAAG0D,KAAK,CAACX,KAAK,CAAC7C,CAAC,GAAG,IAAI,GAAGwD,KAAK,CAACX,KAAK,CAAC5C,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG0D,QAAQ,CAAC,GAAG,GAAG;IAEvHrC,OAAO,CAAC2C,SAAS,CAAC,CAAC;IAEnB,IAAIrL,WAAW,IAAI4K,KAAK,CAACV,KAAK,CAACoB,IAAI,KAAK,MAAM,IAAI,OAAOV,KAAK,CAACV,KAAK,CAACqB,IAAI,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACb,KAAK,CAACV,KAAK,CAACwB,MAAM,CAAC,EAAE;MAC3HhD,OAAO,CAACiD,IAAI,CAACC,eAAe,CAC1BhB,KAAK,CAACV,KAAK,CAACqB,IAAI,EAChBX,KAAK,CAACV,KAAK,CAACwB,MAAM,EAClBd,KAAK,CAACnF,CAAC,EACPmF,KAAK,CAAClF,CAAC,EACP1D,IAAI,CAAC6J,GAAG,CAACX,EAAE,GAAGF,EAAE,CAAC,GAAG,GAAG,EACvBhJ,IAAI,CAAC6J,GAAG,CAACV,EAAE,GAAGF,EAAE,CAAC,GAAG,GAAG,EACvBjJ,IAAI,CAAC0H,EAAE,GAAG,EAAE,GAAGkB,KAAK,CAAChB,MACvB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgB,KAAK,CAACV,KAAK,CAACoB,IAAI,KAAK,QAAQ,EAAE;MACxC,IAAIzC,QAAQ,GAAG7G,IAAI,CAAC0H,EAAE,GAAG,EAAE,GAAGkB,KAAK,CAAChB,MAAM;MAC1C,IAAIkC,MAAM,GAAG9J,IAAI,CAAC6J,GAAG,CAACX,EAAE,GAAGF,EAAE,CAAC,GAAG,GAAG;MACpC,IAAIe,MAAM,GAAG/J,IAAI,CAAC6J,GAAG,CAACV,EAAE,GAAGF,EAAE,CAAC,GAAG,GAAG;MACpC,IAAIzJ,KAAK,GAAGoJ,KAAK,CAACV,KAAK,CAAC1J,MAAM,CAACgB,KAAK,GAAGoJ,KAAK,CAAC7E,MAAM;MACnD,IAAItE,MAAM,GAAGmJ,KAAK,CAACV,KAAK,CAAC1J,MAAM,CAACiB,MAAM,GAAGmJ,KAAK,CAAC7E,MAAM;MAErD,IAAI2F,MAAM,GAAG,IAAIxL,SAAS,CAAC,CACzB8B,IAAI,CAAC6I,GAAG,CAAChC,QAAQ,CAAC,GAAGiD,MAAM,EAC3B9J,IAAI,CAAC8I,GAAG,CAACjC,QAAQ,CAAC,GAAGiD,MAAM,EAC3B,CAAC9J,IAAI,CAAC8I,GAAG,CAACjC,QAAQ,CAAC,GAAGkD,MAAM,EAC5B/J,IAAI,CAAC6I,GAAG,CAAChC,QAAQ,CAAC,GAAGkD,MAAM,EAC3BnB,KAAK,CAACnF,CAAC,EACPmF,KAAK,CAAClF,CAAC,CACR,CAAC;;MAEF;MACAgG,MAAM,CAACM,YAAY,CAAC,IAAI9L,SAAS,CAAC0K,KAAK,CAACV,KAAK,CAACwB,MAAM,CAAC,CAAC;MAEtD,IAAIO,OAAO,GAAGvD,OAAO,CAAChI,aAAa,CAACQ,YAAY,CAACG,SAAS,CAACuJ,KAAK,CAACV,KAAK,CAAC1J,MAAM,CAAC,EAAE,WAAW,CAAC;MAC5FyL,OAAO,CAACC,YAAY,CAACR,MAAM,CAAC;MAE5BhD,OAAO,CAACyD,WAAW,GAAI,CAAC,GAAGpB,QAAS;MACpCrC,OAAO,CAAC0C,SAAS,GAAGa,OAAO;MAC3BvD,OAAO,CAACnI,QAAQ,CACdqK,KAAK,CAACnF,CAAC,GAAIjE,KAAK,GAAG,CAAE,EACrBoJ,KAAK,CAAClF,CAAC,GAAIjE,MAAM,GAAG,CAAE,EACtBD,KAAK,EACLC,MACF,CAAC;MACDiH,OAAO,CAACyD,WAAW,GAAG,CAAC;IACzB,CAAC,MAAM,IAAIvB,KAAK,CAACV,KAAK,KAAK,QAAQ,EAAE;MACnCxB,OAAO,CAACD,OAAO,GACbC,OAAO,CAACD,OAAO,CAACmC,KAAK,CAACnF,CAAC,EAAEmF,KAAK,CAAClF,CAAC,EAAE1D,IAAI,CAAC6J,GAAG,CAACX,EAAE,GAAGF,EAAE,CAAC,GAAGJ,KAAK,CAACH,UAAU,EAAEzI,IAAI,CAAC6J,GAAG,CAACV,EAAE,GAAGF,EAAE,CAAC,GAAGL,KAAK,CAACH,UAAU,EAAEzI,IAAI,CAAC0H,EAAE,GAAG,EAAE,GAAGkB,KAAK,CAAChB,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG5H,IAAI,CAAC0H,EAAE,CAAC,GAC1JjB,OAAO,CAACC,OAAO,EAAEkC,KAAK,CAACnF,CAAC,EAAEmF,KAAK,CAAClF,CAAC,EAAE1D,IAAI,CAAC6J,GAAG,CAACX,EAAE,GAAGF,EAAE,CAAC,GAAGJ,KAAK,CAACH,UAAU,EAAEzI,IAAI,CAAC6J,GAAG,CAACV,EAAE,GAAGF,EAAE,CAAC,GAAGL,KAAK,CAACH,UAAU,EAAEzI,IAAI,CAAC0H,EAAE,GAAG,EAAE,GAAGkB,KAAK,CAAChB,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG5H,IAAI,CAAC0H,EAAE,CAAC;IAC/J,CAAC,MAAM,IAAIkB,KAAK,CAACV,KAAK,KAAK,MAAM,EAAE;MACjC,IAAIkC,GAAG,GAAGpK,IAAI,CAAC0H,EAAE,GAAG,CAAC,GAAG,CAAC;MACzB,IAAI2C,WAAW,GAAG,CAAC,GAAGzB,KAAK,CAAC7E,MAAM;MAClC,IAAIuG,WAAW,GAAG,CAAC,GAAG1B,KAAK,CAAC7E,MAAM;MAClC,IAAIN,CAAC,GAAGmF,KAAK,CAACnF,CAAC;MACf,IAAIC,CAAC,GAAGkF,KAAK,CAAClF,CAAC;MACf,IAAI6G,MAAM,GAAG,CAAC;MACd,IAAIC,IAAI,GAAGxK,IAAI,CAAC0H,EAAE,GAAG6C,MAAM;MAE3B,OAAOA,MAAM,EAAE,EAAE;QACf9G,CAAC,GAAGmF,KAAK,CAACnF,CAAC,GAAGzD,IAAI,CAAC6I,GAAG,CAACuB,GAAG,CAAC,GAAGE,WAAW;QACzC5G,CAAC,GAAGkF,KAAK,CAAClF,CAAC,GAAG1D,IAAI,CAAC8I,GAAG,CAACsB,GAAG,CAAC,GAAGE,WAAW;QACzC5D,OAAO,CAAC+D,MAAM,CAAChH,CAAC,EAAEC,CAAC,CAAC;QACpB0G,GAAG,IAAII,IAAI;QAEX/G,CAAC,GAAGmF,KAAK,CAACnF,CAAC,GAAGzD,IAAI,CAAC6I,GAAG,CAACuB,GAAG,CAAC,GAAGC,WAAW;QACzC3G,CAAC,GAAGkF,KAAK,CAAClF,CAAC,GAAG1D,IAAI,CAAC8I,GAAG,CAACsB,GAAG,CAAC,GAAGC,WAAW;QACzC3D,OAAO,CAAC+D,MAAM,CAAChH,CAAC,EAAEC,CAAC,CAAC;QACpB0G,GAAG,IAAII,IAAI;MACb;IACF,CAAC,MAAM;MACL9D,OAAO,CAACgE,MAAM,CAAC1K,IAAI,CAACC,KAAK,CAAC2I,KAAK,CAACnF,CAAC,CAAC,EAAEzD,IAAI,CAACC,KAAK,CAAC2I,KAAK,CAAClF,CAAC,CAAC,CAAC;MACxDgD,OAAO,CAAC+D,MAAM,CAACzK,IAAI,CAACC,KAAK,CAAC2I,KAAK,CAACL,OAAO,CAAC,EAAEvI,IAAI,CAACC,KAAK,CAACgJ,EAAE,CAAC,CAAC;MACzDvC,OAAO,CAAC+D,MAAM,CAACzK,IAAI,CAACC,KAAK,CAACiJ,EAAE,CAAC,EAAElJ,IAAI,CAACC,KAAK,CAACkJ,EAAE,CAAC,CAAC;MAC9CzC,OAAO,CAAC+D,MAAM,CAACzK,IAAI,CAACC,KAAK,CAAC+I,EAAE,CAAC,EAAEhJ,IAAI,CAACC,KAAK,CAAC2I,KAAK,CAACJ,OAAO,CAAC,CAAC;IAC3D;IAEA9B,OAAO,CAACiE,SAAS,CAAC,CAAC;IACnBjE,OAAO,CAACiD,IAAI,CAAC,CAAC;IAEd,OAAOf,KAAK,CAACT,IAAI,GAAGS,KAAK,CAACR,UAAU;EACtC;EAEA,SAASwC,OAAOA,CAACxM,MAAM,EAAEyM,MAAM,EAAEC,OAAO,EAAEhJ,IAAI,EAAEC,IAAI,EAAE;IACpD,IAAIgJ,eAAe,GAAGF,MAAM,CAAC5I,KAAK,CAAC,CAAC;IACpC,IAAIyE,OAAO,GAAGtI,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACrC,IAAI0M,cAAc;IAClB,IAAIC,OAAO;IAEX,IAAI/J,IAAI,GAAGrC,OAAO,CAAC,UAAUqD,OAAO,EAAE;MACpC,SAASgJ,MAAMA,CAAA,EAAG;QAChBF,cAAc,GAAGC,OAAO,GAAG,IAAI;QAE/BvE,OAAO,CAACyE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAErJ,IAAI,CAACtC,KAAK,EAAEsC,IAAI,CAACrC,MAAM,CAAC;QAChDP,YAAY,CAACU,KAAK,CAAC,CAAC;QAEpBmC,IAAI,CAAC,CAAC;QACNG,OAAO,CAAC,CAAC;MACX;MAEA,SAASkJ,MAAMA,CAAA,EAAG;QAChB,IAAIjO,QAAQ,IAAI,EAAE2E,IAAI,CAACtC,KAAK,KAAKpC,UAAU,CAACoC,KAAK,IAAIsC,IAAI,CAACrC,MAAM,KAAKrC,UAAU,CAACqC,MAAM,CAAC,EAAE;UACvFqC,IAAI,CAACtC,KAAK,GAAGpB,MAAM,CAACoB,KAAK,GAAGpC,UAAU,CAACoC,KAAK;UAC5CsC,IAAI,CAACrC,MAAM,GAAGrB,MAAM,CAACqB,MAAM,GAAGrC,UAAU,CAACqC,MAAM;QACjD;QAEA,IAAI,CAACqC,IAAI,CAACtC,KAAK,IAAI,CAACsC,IAAI,CAACrC,MAAM,EAAE;UAC/BqL,OAAO,CAAC1M,MAAM,CAAC;UACf0D,IAAI,CAACtC,KAAK,GAAGpB,MAAM,CAACoB,KAAK;UACzBsC,IAAI,CAACrC,MAAM,GAAGrB,MAAM,CAACqB,MAAM;QAC7B;QAEAiH,OAAO,CAACyE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAErJ,IAAI,CAACtC,KAAK,EAAEsC,IAAI,CAACrC,MAAM,CAAC;QAEhDsL,eAAe,GAAGA,eAAe,CAACM,MAAM,CAAC,UAAUzC,KAAK,EAAE;UACxD,OAAOD,WAAW,CAACjC,OAAO,EAAEkC,KAAK,CAAC;QACpC,CAAC,CAAC;QAEF,IAAImC,eAAe,CAAC9F,MAAM,EAAE;UAC1B+F,cAAc,GAAGlL,GAAG,CAACI,KAAK,CAACkL,MAAM,CAAC;QACpC,CAAC,MAAM;UACLF,MAAM,CAAC,CAAC;QACV;MACF;MAEAF,cAAc,GAAGlL,GAAG,CAACI,KAAK,CAACkL,MAAM,CAAC;MAClCH,OAAO,GAAGC,MAAM;IAClB,CAAC,CAAC;IAEF,OAAO;MACLI,SAAS,EAAE,SAAAA,CAAUT,MAAM,EAAE;QAC3BE,eAAe,GAAGA,eAAe,CAACQ,MAAM,CAACV,MAAM,CAAC;QAEhD,OAAO3J,IAAI;MACb,CAAC;MACD9C,MAAM,EAAEA,MAAM;MACdS,OAAO,EAAEqC,IAAI;MACbuB,KAAK,EAAE,SAAAA,CAAA,EAAY;QACjB,IAAIuI,cAAc,EAAE;UAClBlL,GAAG,CAACK,MAAM,CAAC6K,cAAc,CAAC;QAC5B;QAEA,IAAIC,OAAO,EAAE;UACXA,OAAO,CAAC,CAAC;QACX;MACF;IACF,CAAC;EACH;EAEA,SAASO,cAAcA,CAACpN,MAAM,EAAEqN,UAAU,EAAE;IAC1C,IAAIC,WAAW,GAAG,CAACtN,MAAM;IACzB,IAAIuN,WAAW,GAAG,CAAC,CAACxH,IAAI,CAACsH,UAAU,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC;IACpD,IAAIG,wBAAwB,GAAG,KAAK;IACpC,IAAIC,6BAA6B,GAAG1H,IAAI,CAACsH,UAAU,EAAE,yBAAyB,EAAEK,OAAO,CAAC;IACxF,IAAIC,eAAe,GAAG1O,YAAY,IAAI,CAAC,CAAC8G,IAAI,CAACsH,UAAU,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC;IAC3E,IAAIxK,MAAM,GAAG8K,eAAe,GAAG/K,SAAS,CAAC,CAAC,GAAG,IAAI;IACjD,IAAI8J,OAAO,GAAGY,WAAW,GAAGhG,mBAAmB,GAAGK,iBAAiB;IACnE,IAAIiG,WAAW,GAAI5N,MAAM,IAAI6C,MAAM,GAAI,CAAC,CAAC7C,MAAM,CAAC6N,sBAAsB,GAAG,KAAK;IAC9E,IAAIC,gBAAgB,GAAG,OAAOC,UAAU,KAAK,UAAU,IAAIA,UAAU,CAAC,0BAA0B,CAAC,CAACC,OAAO;IACzG,IAAIC,YAAY;IAEhB,SAASC,SAASA,CAAChL,OAAO,EAAEQ,IAAI,EAAEC,IAAI,EAAE;MACtC,IAAIkB,aAAa,GAAGkB,IAAI,CAAC7C,OAAO,EAAE,eAAe,EAAE+C,eAAe,CAAC;MACnE,IAAInB,KAAK,GAAGiB,IAAI,CAAC7C,OAAO,EAAE,OAAO,EAAEmE,MAAM,CAAC;MAC1C,IAAItC,MAAM,GAAGgB,IAAI,CAAC7C,OAAO,EAAE,QAAQ,EAAEmE,MAAM,CAAC;MAC5C,IAAIrC,aAAa,GAAGe,IAAI,CAAC7C,OAAO,EAAE,eAAe,EAAEmE,MAAM,CAAC;MAC1D,IAAIpC,KAAK,GAAGc,IAAI,CAAC7C,OAAO,EAAE,OAAO,EAAEmE,MAAM,CAAC;MAC1C,IAAInC,OAAO,GAAGa,IAAI,CAAC7C,OAAO,EAAE,SAAS,EAAEmE,MAAM,CAAC;MAC9C,IAAIlC,KAAK,GAAGY,IAAI,CAAC7C,OAAO,EAAE,OAAO,EAAEmE,MAAM,CAAC;MAC1C,IAAI5B,MAAM,GAAGM,IAAI,CAAC7C,OAAO,EAAE,QAAQ,EAAEuD,WAAW,CAAC;MACjD,IAAIrB,KAAK,GAAGW,IAAI,CAAC7C,OAAO,EAAE,OAAO,EAAEmE,MAAM,CAAC;MAC1C,IAAI9B,MAAM,GAAGQ,IAAI,CAAC7C,OAAO,EAAE,QAAQ,CAAC;MACpC,IAAIyC,MAAM,GAAGI,IAAI,CAAC7C,OAAO,EAAE,QAAQ,CAAC;MACpC,IAAIoH,IAAI,GAAG,CAAC,CAACvE,IAAI,CAAC7C,OAAO,EAAE,MAAM,CAAC;MAClC,IAAIiE,MAAM,GAAGD,SAAS,CAAChE,OAAO,CAAC;MAE/B,IAAIiL,IAAI,GAAGtJ,aAAa;MACxB,IAAI4H,MAAM,GAAG,EAAE;MAEf,IAAI2B,MAAM,GAAGpO,MAAM,CAACoB,KAAK,GAAG+F,MAAM,CAAC9B,CAAC;MACpC,IAAIgJ,MAAM,GAAGrO,MAAM,CAACqB,MAAM,GAAG8F,MAAM,CAAC7B,CAAC;MAErC,OAAO6I,IAAI,EAAE,EAAE;QACb1B,MAAM,CAAC6B,IAAI,CACTnF,aAAa,CAAC;UACZ9D,CAAC,EAAE+I,MAAM;UACT9I,CAAC,EAAE+I,MAAM;UACTvJ,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdC,aAAa,EAAEA,aAAa;UAC5B6E,KAAK,EAAEpE,MAAM,CAAC0I,IAAI,GAAG1I,MAAM,CAACoB,MAAM,CAAC;UACnCiD,KAAK,EAAEvE,MAAM,CAACY,SAAS,CAAC,CAAC,EAAEZ,MAAM,CAACsB,MAAM,CAAC,CAAC;UAC1CzB,KAAK,EAAEA,KAAK;UACZH,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,KAAK,EAAEA,KAAK;UACZQ,MAAM,EAAEA,MAAM;UACd2E,IAAI,EAAEA;QACR,CAAC,CACH,CAAC;MACH;;MAEA;MACA;MACA,IAAI2D,YAAY,EAAE;QAChB,OAAOA,YAAY,CAACf,SAAS,CAACT,MAAM,CAAC;MACvC;MAEAwB,YAAY,GAAGzB,OAAO,CAACxM,MAAM,EAAEyM,MAAM,EAAEC,OAAO,EAAEhJ,IAAI,EAAGC,IAAI,CAAC;MAE5D,OAAOsK,YAAY,CAACxN,OAAO;IAC7B;IAEA,SAAS+C,IAAIA,CAACN,OAAO,EAAE;MACrB,IAAIwC,uBAAuB,GAAG+H,6BAA6B,IAAI1H,IAAI,CAAC7C,OAAO,EAAE,yBAAyB,EAAEwK,OAAO,CAAC;MAChH,IAAIlI,MAAM,GAAGO,IAAI,CAAC7C,OAAO,EAAE,QAAQ,EAAEmE,MAAM,CAAC;MAE5C,IAAI3B,uBAAuB,IAAIoI,gBAAgB,EAAE;QAC/C,OAAOrN,OAAO,CAAC,UAAUqD,OAAO,EAAE;UAChCA,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;MACJ;MAEA,IAAIwJ,WAAW,IAAIW,YAAY,EAAE;QAC/B;QACAjO,MAAM,GAAGiO,YAAY,CAACjO,MAAM;MAC9B,CAAC,MAAM,IAAIsN,WAAW,IAAI,CAACtN,MAAM,EAAE;QACjC;QACAA,MAAM,GAAG8H,SAAS,CAACtC,MAAM,CAAC;QAC1B+B,QAAQ,CAACgH,IAAI,CAACC,WAAW,CAACxO,MAAM,CAAC;MACnC;MAEA,IAAIuN,WAAW,IAAI,CAACK,WAAW,EAAE;QAC/B;QACAlB,OAAO,CAAC1M,MAAM,CAAC;MACjB;MAEA,IAAI0D,IAAI,GAAG;QACTtC,KAAK,EAAEpB,MAAM,CAACoB,KAAK;QACnBC,MAAM,EAAErB,MAAM,CAACqB;MACjB,CAAC;MAED,IAAIwB,MAAM,IAAI,CAAC+K,WAAW,EAAE;QAC1B/K,MAAM,CAACQ,IAAI,CAACrD,MAAM,CAAC;MACrB;MAEA4N,WAAW,GAAG,IAAI;MAElB,IAAI/K,MAAM,EAAE;QACV7C,MAAM,CAAC6N,sBAAsB,GAAG,IAAI;MACtC;MAEA,SAASY,QAAQA,CAAA,EAAG;QAClB,IAAI5L,MAAM,EAAE;UACV;UACA,IAAI6L,GAAG,GAAG;YACR7G,qBAAqB,EAAE,SAAAA,CAAA,EAAY;cACjC,IAAI,CAACyF,WAAW,EAAE;gBAChB,OAAOtN,MAAM,CAAC6H,qBAAqB,CAAC,CAAC;cACvC;YACF;UACF,CAAC;UAED6E,OAAO,CAACgC,GAAG,CAAC;UAEZ7L,MAAM,CAACO,WAAW,CAAC;YACjBuL,MAAM,EAAE;cACNvN,KAAK,EAAEsN,GAAG,CAACtN,KAAK;cAChBC,MAAM,EAAEqN,GAAG,CAACrN;YACd;UACF,CAAC,CAAC;UACF;QACF;;QAEA;QACA;QACAqC,IAAI,CAACtC,KAAK,GAAGsC,IAAI,CAACrC,MAAM,GAAG,IAAI;MACjC;MAEA,SAASsC,IAAIA,CAAA,EAAG;QACdsK,YAAY,GAAG,IAAI;QAEnB,IAAIV,WAAW,EAAE;UACfC,wBAAwB,GAAG,KAAK;UAChC1O,MAAM,CAACoF,mBAAmB,CAAC,QAAQ,EAAEuK,QAAQ,CAAC;QAChD;QAEA,IAAInB,WAAW,IAAItN,MAAM,EAAE;UACzB,IAAIuH,QAAQ,CAACgH,IAAI,CAACK,QAAQ,CAAC5O,MAAM,CAAC,EAAE;YAClCuH,QAAQ,CAACgH,IAAI,CAACM,WAAW,CAAC7O,MAAM,CAAC;UACnC;UACAA,MAAM,GAAG,IAAI;UACb4N,WAAW,GAAG,KAAK;QACrB;MACF;MAEA,IAAIL,WAAW,IAAI,CAACC,wBAAwB,EAAE;QAC5CA,wBAAwB,GAAG,IAAI;QAC/B1O,MAAM,CAACqF,gBAAgB,CAAC,QAAQ,EAAEsK,QAAQ,EAAE,KAAK,CAAC;MACpD;MAEA,IAAI5L,MAAM,EAAE;QACV,OAAOA,MAAM,CAACW,IAAI,CAACN,OAAO,EAAEQ,IAAI,EAAEC,IAAI,CAAC;MACzC;MAEA,OAAOuK,SAAS,CAAChL,OAAO,EAAEQ,IAAI,EAAEC,IAAI,CAAC;IACvC;IAEAH,IAAI,CAACa,KAAK,GAAG,YAAY;MACvB,IAAIxB,MAAM,EAAE;QACVA,MAAM,CAACwB,KAAK,CAAC,CAAC;MAChB;MAEA,IAAI4J,YAAY,EAAE;QAChBA,YAAY,CAAC5J,KAAK,CAAC,CAAC;MACtB;IACF,CAAC;IAED,OAAOb,IAAI;EACb;;EAEA;EACA,IAAIsL,WAAW;EACf,SAASC,cAAcA,CAAA,EAAG;IACxB,IAAI,CAACD,WAAW,EAAE;MAChBA,WAAW,GAAG1B,cAAc,CAAC,IAAI,EAAE;QAAE4B,SAAS,EAAE,IAAI;QAAEL,MAAM,EAAE;MAAK,CAAC,CAAC;IACvE;IACA,OAAOG,WAAW;EACpB;EAEA,SAAStD,eAAeA,CAACyD,UAAU,EAAEC,UAAU,EAAE7J,CAAC,EAAEC,CAAC,EAAEoG,MAAM,EAAEC,MAAM,EAAElD,QAAQ,EAAE;IAC/E,IAAI0G,MAAM,GAAG,IAAItP,MAAM,CAACoP,UAAU,CAAC;IAEnC,IAAIG,EAAE,GAAG,IAAIvP,MAAM,CAAC,CAAC;IACrBuP,EAAE,CAACC,OAAO,CAACF,MAAM,EAAE,IAAIrP,SAAS,CAACoP,UAAU,CAAC,CAAC;IAE7C,IAAII,EAAE,GAAG,IAAIzP,MAAM,CAAC,CAAC;IACrB;IACAyP,EAAE,CAACD,OAAO,CAACD,EAAE,EAAE,IAAItP,SAAS,CAAC,CAC3B8B,IAAI,CAAC6I,GAAG,CAAChC,QAAQ,CAAC,GAAGiD,MAAM,EAC3B9J,IAAI,CAAC8I,GAAG,CAACjC,QAAQ,CAAC,GAAGiD,MAAM,EAC3B,CAAC9J,IAAI,CAAC8I,GAAG,CAACjC,QAAQ,CAAC,GAAGkD,MAAM,EAC5B/J,IAAI,CAAC6I,GAAG,CAAChC,QAAQ,CAAC,GAAGkD,MAAM,EAC3BtG,CAAC,EACDC,CAAC,CACF,CAAC,CAAC;IAEH,OAAOgK,EAAE;EACX;EAEA,SAASC,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI,CAAC5P,WAAW,EAAE;MAChB,MAAM,IAAI6P,KAAK,CAAC,iDAAiD,CAAC;IACpE;IAEA,IAAItE,IAAI,EAAEG,MAAM;IAEhB,IAAI,OAAOkE,QAAQ,KAAK,QAAQ,EAAE;MAChCrE,IAAI,GAAGqE,QAAQ;IACjB,CAAC,MAAM;MACLrE,IAAI,GAAGqE,QAAQ,CAACrE,IAAI;MACpBG,MAAM,GAAGkE,QAAQ,CAAClE,MAAM;IAC1B;IAEA,IAAI6D,MAAM,GAAG,IAAItP,MAAM,CAACsL,IAAI,CAAC;IAC7B,IAAIuE,UAAU,GAAGnI,QAAQ,CAACQ,aAAa,CAAC,QAAQ,CAAC;IACjD,IAAI4H,OAAO,GAAGD,UAAU,CAACxP,UAAU,CAAC,IAAI,CAAC;IAEzC,IAAI,CAACoL,MAAM,EAAE;MACX;MACA,IAAIsE,OAAO,GAAG,IAAI;MAClB,IAAIC,IAAI,GAAGD,OAAO;MAClB,IAAIE,IAAI,GAAGF,OAAO;MAClB,IAAIG,IAAI,GAAG,CAAC;MACZ,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAI5O,KAAK,EAAEC,MAAM;;MAEjB;MACA;MACA,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuK,OAAO,EAAEvK,CAAC,IAAI,CAAC,EAAE;QACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsK,OAAO,EAAEtK,CAAC,IAAI,CAAC,EAAE;UACnC,IAAIqK,OAAO,CAACM,aAAa,CAACd,MAAM,EAAE9J,CAAC,EAAEC,CAAC,EAAE,SAAS,CAAC,EAAE;YAClDuK,IAAI,GAAGjO,IAAI,CAACwE,GAAG,CAACyJ,IAAI,EAAExK,CAAC,CAAC;YACxByK,IAAI,GAAGlO,IAAI,CAACwE,GAAG,CAAC0J,IAAI,EAAExK,CAAC,CAAC;YACxByK,IAAI,GAAGnO,IAAI,CAACyE,GAAG,CAAC0J,IAAI,EAAE1K,CAAC,CAAC;YACxB2K,IAAI,GAAGpO,IAAI,CAACyE,GAAG,CAAC2J,IAAI,EAAE1K,CAAC,CAAC;UAC1B;QACF;MACF;MAEAlE,KAAK,GAAG2O,IAAI,GAAGF,IAAI;MACnBxO,MAAM,GAAG2O,IAAI,GAAGF,IAAI;MAEpB,IAAII,cAAc,GAAG,EAAE;MACvB,IAAIlH,KAAK,GAAGpH,IAAI,CAACwE,GAAG,CAAC8J,cAAc,GAAC9O,KAAK,EAAE8O,cAAc,GAAC7O,MAAM,CAAC;MAEjEiK,MAAM,GAAG,CACPtC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEA,KAAK,EAClB,CAACpH,IAAI,CAACuO,KAAK,CAAE/O,KAAK,GAAC,CAAC,GAAIyO,IAAI,CAAC,GAAG7G,KAAK,EACrC,CAACpH,IAAI,CAACuO,KAAK,CAAE9O,MAAM,GAAC,CAAC,GAAIyO,IAAI,CAAC,GAAG9G,KAAK,CACvC;IACH;IAEA,OAAO;MACLkC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAEA,IAAI;MACVG,MAAM,EAAEA;IACV,CAAC;EACH;EAEA,SAAS8E,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAIC,IAAI;MACJ3K,MAAM,GAAG,CAAC;MACVkE,KAAK,GAAG,SAAS;MACjB;MACA0G,UAAU,GAAG,gKAAgK;IAEjL,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;MAChCC,IAAI,GAAGD,QAAQ;IACjB,CAAC,MAAM;MACLC,IAAI,GAAGD,QAAQ,CAACC,IAAI;MACpB3K,MAAM,GAAG,QAAQ,IAAI0K,QAAQ,GAAGA,QAAQ,CAAC1K,MAAM,GAAGA,MAAM;MACxD4K,UAAU,GAAG,YAAY,IAAIF,QAAQ,GAAGA,QAAQ,CAACE,UAAU,GAAGA,UAAU;MACxE1G,KAAK,GAAG,OAAO,IAAIwG,QAAQ,GAAGA,QAAQ,CAACxG,KAAK,GAAGA,KAAK;IACtD;;IAEA;IACA;IACA,IAAI2G,QAAQ,GAAG,EAAE,GAAG7K,MAAM;IAC1B,IAAI8K,IAAI,GAAG,EAAE,GAAGD,QAAQ,GAAG,KAAK,GAAGD,UAAU;IAE7C,IAAIvQ,MAAM,GAAG,IAAIX,eAAe,CAACmR,QAAQ,EAAEA,QAAQ,CAAC;IACpD,IAAIvQ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IAEjCD,GAAG,CAACwQ,IAAI,GAAGA,IAAI;IACf,IAAI/M,IAAI,GAAGzD,GAAG,CAACyQ,WAAW,CAACJ,IAAI,CAAC;IAChC,IAAIlP,KAAK,GAAGQ,IAAI,CAAC+O,IAAI,CAACjN,IAAI,CAACkN,sBAAsB,GAAGlN,IAAI,CAACmN,qBAAqB,CAAC;IAC/E,IAAIxP,MAAM,GAAGO,IAAI,CAAC+O,IAAI,CAACjN,IAAI,CAACoN,uBAAuB,GAAGpN,IAAI,CAACqN,wBAAwB,CAAC;IAEpF,IAAIC,OAAO,GAAG,CAAC;IACf,IAAI3L,CAAC,GAAG3B,IAAI,CAACmN,qBAAqB,GAAGG,OAAO;IAC5C,IAAI1L,CAAC,GAAG5B,IAAI,CAACoN,uBAAuB,GAAGE,OAAO;IAC9C5P,KAAK,IAAI4P,OAAO,GAAGA,OAAO;IAC1B3P,MAAM,IAAI2P,OAAO,GAAGA,OAAO;IAE3BhR,MAAM,GAAG,IAAIX,eAAe,CAAC+B,KAAK,EAAEC,MAAM,CAAC;IAC3CpB,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IAC7BD,GAAG,CAACwQ,IAAI,GAAGA,IAAI;IACfxQ,GAAG,CAAC+K,SAAS,GAAGnB,KAAK;IAErB5J,GAAG,CAACgR,QAAQ,CAACX,IAAI,EAAEjL,CAAC,EAAEC,CAAC,CAAC;IAExB,IAAI0D,KAAK,GAAG,CAAC,GAAGrD,MAAM;IAEtB,OAAO;MACLuF,IAAI,EAAE,QAAQ;MACd;MACA9K,MAAM,EAAEJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MACtCiL,MAAM,EAAE,CAACtC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEA,KAAK,EAAE,CAAC5H,KAAK,GAAG4H,KAAK,GAAG,CAAC,EAAE,CAAC3H,MAAM,GAAG2H,KAAK,GAAG,CAAC;IACtE,CAAC;EACH;EAEApK,MAAM,CAACgC,OAAO,GAAG,YAAW;IAC1B,OAAOmO,cAAc,CAAC,CAAC,CAACmC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChD,CAAC;EACDvS,MAAM,CAACgC,OAAO,CAACyD,KAAK,GAAG,YAAW;IAChC0K,cAAc,CAAC,CAAC,CAAC1K,KAAK,CAAC,CAAC;EAC1B,CAAC;EACDzF,MAAM,CAACgC,OAAO,CAACwQ,MAAM,GAAGhE,cAAc;EACtCxO,MAAM,CAACgC,OAAO,CAAC2O,aAAa,GAAGA,aAAa;EAC5C3Q,MAAM,CAACgC,OAAO,CAACwP,aAAa,GAAGA,aAAa;AAC9C,CAAC,EAAE,YAAY;EACb,IAAI,OAAOiB,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOA,MAAM;EACf;EAEA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC/B,OAAOA,IAAI;EACb;EAEA,OAAO,IAAI,IAAI,CAAC,CAAC;AACnB,CAAC,CAAE,CAAC,EAAE1S,MAAM,EAAE,KAAK,CAAC;;AAEpB;;AAEA,eAAeA,MAAM,CAACgC,OAAO;AAC7B,OAAO,IAAIwQ,MAAM,GAAGxS,MAAM,CAACgC,OAAO,CAACwQ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}