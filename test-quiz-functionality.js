const axios = require('axios');

// Test Quiz Functionality
async function testQuizFunctionality() {
  console.log('🎯 Testing Quiz Play Functionality...\n');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // 1. Test server health
    console.log('1️⃣ Testing server health...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ Server health:', healthResponse.data);
    
    // 2. Test database connection
    console.log('\n2️⃣ Testing database connection...');
    const dbResponse = await axios.get(`${baseURL}/test/db`);
    console.log('✅ Database status:', dbResponse.data);
    
    // 3. Test getting all exams (without authentication for now)
    console.log('\n3️⃣ Testing exam retrieval...');
    try {
      const examsResponse = await axios.post(`${baseURL}/exams/get-all-exams`);
      console.log('✅ Exams found:', examsResponse.data.data?.length || 0);
      
      if (examsResponse.data.data && examsResponse.data.data.length > 0) {
        const firstExam = examsResponse.data.data[0];
        console.log('📝 First exam:', {
          id: firstExam._id,
          name: firstExam.name,
          duration: firstExam.duration,
          subject: firstExam.subject,
          questionsCount: firstExam.questions?.length || 0
        });
        
        // 4. Test getting specific exam by ID
        console.log('\n4️⃣ Testing specific exam retrieval...');
        try {
          const examResponse = await axios.post(`${baseURL}/exams/get-exam-by-id`, {
            examId: firstExam._id
          });
          console.log('✅ Exam details retrieved successfully');
          console.log('📊 Exam data:', {
            name: examResponse.data.data.name,
            questionsCount: examResponse.data.data.questions?.length || 0,
            duration: examResponse.data.data.duration,
            passingMarks: examResponse.data.data.passingMarks
          });
          
          if (examResponse.data.data.questions && examResponse.data.data.questions.length > 0) {
            const firstQuestion = examResponse.data.data.questions[0];
            console.log('🔍 First question preview:', {
              type: firstQuestion.type || firstQuestion.answerType,
              hasOptions: !!firstQuestion.options,
              correctAnswer: firstQuestion.correctAnswer || firstQuestion.correctOption
            });
          }
          
        } catch (examError) {
          console.log('❌ Error getting specific exam:', examError.response?.data?.message || examError.message);
        }
        
      } else {
        console.log('⚠️ No exams found in database');
      }
      
    } catch (examsError) {
      console.log('❌ Error getting exams:', examsError.response?.data?.message || examsError.message);
    }
    
    // 5. Test trial quiz functionality (no auth required)
    console.log('\n5️⃣ Testing trial quiz functionality...');
    try {
      const trialResponse = await axios.post(`${baseURL}/trial/get-trial-quiz`, {
        level: 'primary',
        class: '3'
      });
      console.log('✅ Trial quiz response:', trialResponse.data.success ? 'Success' : 'Failed');
      
      if (trialResponse.data.success && trialResponse.data.data) {
        console.log('📝 Trial quiz data:', {
          examName: trialResponse.data.data.exam?.name,
          questionsCount: trialResponse.data.data.exam?.questions?.length || 0,
          duration: trialResponse.data.data.exam?.duration
        });
      }
      
    } catch (trialError) {
      console.log('❌ Error getting trial quiz:', trialError.response?.data?.message || trialError.message);
    }
    
    console.log('\n🎉 Quiz functionality test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testQuizFunctionality();
