require('dotenv').config();
const mongoose = require('mongoose');
const Exam = require('./models/examModel');

async function checkQuizData() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');
    
    const exams = await Exam.find().limit(3).populate('questions');
    console.log('📊 Sample exams found:', exams.length);
    
    exams.forEach((exam, index) => {
      console.log(`\n--- Exam ${index + 1}: ${exam.name} ---`);
      console.log('Questions count:', exam.questions?.length || 0);
      console.log('Duration:', exam.duration);
      console.log('Subject:', exam.subject);
      console.log('Class:', exam.class);
      
      if (exam.questions && exam.questions.length > 0) {
        const firstQuestion = exam.questions[0];
        console.log('\n🔍 First question structure:');
        console.log('- ID:', firstQuestion._id);
        console.log('- Name:', firstQuestion.name?.substring(0, 100) + '...');
        console.log('- Type:', firstQuestion.type);
        console.log('- Answer Type:', firstQuestion.answerType);
        console.log('- Has Options:', firstQuestion.options ? 'Yes' : 'No');
        if (firstQuestion.options) {
          console.log('- Option Keys:', Object.keys(firstQuestion.options));
          console.log('- Option Values:', Object.values(firstQuestion.options).map(v => v?.substring(0, 50) + '...'));
        }
        console.log('- Correct Answer:', firstQuestion.correctAnswer || firstQuestion.correctOption);
      } else {
        console.log('❌ No questions found in this exam');
      }
    });
    
    // Check for exams with no questions
    const emptyExams = await Exam.find({ $or: [{ questions: { $size: 0 } }, { questions: { $exists: false } }] }).limit(5);
    console.log('\n🚨 Exams with no questions:', emptyExams.length);
    emptyExams.forEach(exam => {
      console.log(`- ${exam.name} (ID: ${exam._id})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkQuizData();
