const mongoose = require('mongoose');
require('dotenv').config();
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');

async function quickGenerationTest() {
  try {
    console.log('🚀 Quick Generation Test...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    const aiService = new AIQuestionGenerationService();
    
    // Test with valid ObjectIds
    const validUserId = new mongoose.Types.ObjectId();
    const validExamId = new mongoose.Types.ObjectId();
    
    console.log(`📋 Test ObjectIds:`);
    console.log(`- User ID: ${validUserId}`);
    console.log(`- Exam ID: ${validExamId}`);
    console.log('');
    
    // Test parameters
    const testParams = {
      questionTypes: ["multiple_choice"],
      subjects: ["Science and Technology"],
      level: "primary",
      class: "3",
      difficultyLevels: ["medium"],
      syllabusTopics: [],
      totalQuestions: 1, // Just 1 question for quick test
      questionDistribution: {
        multiple_choice: 1,
        fill_blank: 0,
        picture_based: 0
      },
      selectedSyllabusId: null
    };
    
    console.log('🎯 Generating 1 test question...');
    const startTime = Date.now();
    
    const result = await aiService.generateQuestions(
      testParams,
      validUserId,
      validExamId
    );
    
    const endTime = Date.now();
    console.log(`⏱️ Generation completed in ${endTime - startTime}ms\n`);
    
    if (result.success) {
      console.log('✅ Generation successful!');
      console.log(`- Generation ID: ${result.generationId}`);
      console.log(`- Questions generated: ${result.questions?.length || 0}`);
      console.log(`- Generation time: ${result.generationTime}ms`);
      
      if (result.questions && result.questions.length > 0) {
        const question = result.questions[0];
        console.log('\n📝 Sample question:');
        console.log(`- Text: ${question.name?.substring(0, 80)}...`);
        console.log(`- Topic: ${question.topic}`);
        console.log(`- Class Level: ${question.classLevel}`);
        console.log(`- Type: ${question.type}`);
        console.log(`- Correct Answer: ${question.correctAnswer}`);
      }
      
      console.log('\n🎉 All fixes working correctly!');
      console.log('✅ Subject validation improved');
      console.log('✅ ObjectId validation added');
      console.log('✅ Question generation successful');
      
    } else {
      console.log('❌ Generation failed:');
      console.log(`Error: ${result.error}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('validation failed')) {
      console.log('\n🔍 Validation Error Details:');
      if (error.errors) {
        Object.keys(error.errors).forEach(field => {
          console.log(`- ${field}: ${error.errors[field].message}`);
        });
      }
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

quickGenerationTest();
