const mongoose = require('mongoose');
require('dotenv').config();

async function testSchemaFix() {
  try {
    console.log('🔧 Testing Schema Fix...\n');
    
    // Connect to MongoDB with fresh connection
    await mongoose.connect(process.env.MONGO_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB\n');
    
    // Clear mongoose model cache to ensure fresh schema
    delete mongoose.connection.models['ai-question-generations'];
    
    // Re-import the model to get fresh schema
    const { AIQuestionGeneration } = require('./models/aiQuestionGenerationModel');
    
    console.log('📋 Testing direct document creation...');
    
    // Test creating a generation record directly
    const testGeneration = new AIQuestionGeneration({
      requestedBy: new mongoose.Types.ObjectId(),
      examId: new mongoose.Types.ObjectId(),
      generationParams: {
        questionTypes: ["multiple_choice"],
        subjects: ["Science and Technology"],
        level: "primary",
        class: "3",
        difficultyLevels: ["medium"],
        syllabusTopics: [],
        totalQuestions: 1,
        questionDistribution: {
          multiple_choice: 1,
          fill_blank: 0,
          picture_based: 0
        }
      },
      generationStatus: "completed",
      generatedQuestions: [{
        generatedContent: {
          name: "Test question about thermometer?",
          type: "mcq",
          answerType: "Options",
          correctOption: "A",
          correctAnswer: "A",
          options: { 
            A: "Thermometer", 
            B: "Compass", 
            C: "Ruler", 
            D: "Calculator" 
          },
          topic: "Elementary Scientific Skills",
          classLevel: "primary 3",
          difficultyLevel: "medium",
          syllabusTopics: ["Elementary Scientific Skills"],
          questionType: "multiple_choice",
          isAIGenerated: true,
          generationSource: "ai_bulk",
          createdBy: "ai",
          competencyAligned: "Scientific Skills"
        },
        approved: false,
        qualityScore: 90
      }],
      aiModel: "gpt-3.5-turbo",
      generationTime: 5000,
      qualityScore: 90
    });
    
    console.log('💾 Attempting to save test generation...');
    
    try {
      const savedGeneration = await testGeneration.save();
      console.log('✅ Test generation saved successfully!');
      console.log(`- Generation ID: ${savedGeneration._id}`);
      console.log(`- Questions count: ${savedGeneration.generatedQuestions.length}`);
      console.log(`- First question name: ${savedGeneration.generatedQuestions[0].generatedContent.name}`);
      
      // Clean up test data
      await AIQuestionGeneration.findByIdAndDelete(savedGeneration._id);
      console.log('🧹 Test data cleaned up');
      
      console.log('\n🎉 Schema fix successful!');
      console.log('✅ AI generation model schema is working correctly');
      console.log('✅ Question generation should now work');
      
    } catch (saveError) {
      console.log('❌ Save failed:', saveError.message);
      
      if (saveError.errors) {
        console.log('\n🔍 Validation errors:');
        Object.keys(saveError.errors).forEach(field => {
          console.log(`- ${field}: ${saveError.errors[field].message}`);
        });
      }
      
      // Try alternative approach - simpler structure
      console.log('\n🔄 Trying alternative approach...');
      
      const simpleGeneration = new AIQuestionGeneration({
        requestedBy: new mongoose.Types.ObjectId(),
        generationParams: {
          questionTypes: ["multiple_choice"],
          subjects: ["Science and Technology"],
          level: "primary",
          class: "3",
          totalQuestions: 1,
          questionDistribution: { multiple_choice: 1, fill_blank: 0, picture_based: 0 }
        },
        generationStatus: "in_progress",
        generatedQuestions: [], // Start with empty array
        aiModel: "gpt-3.5-turbo"
      });
      
      try {
        const savedSimple = await simpleGeneration.save();
        console.log('✅ Simple generation saved successfully!');
        
        // Now try to add a question
        savedSimple.generatedQuestions.push({
          generatedContent: {
            name: "Simple test question?",
            type: "mcq",
            topic: "Science",
            classLevel: "primary 3",
            correctAnswer: "A",
            options: { A: "Answer A", B: "Answer B", C: "Answer C", D: "Answer D" }
          },
          approved: false
        });
        
        await savedSimple.save();
        console.log('✅ Question added successfully!');
        
        // Clean up
        await AIQuestionGeneration.findByIdAndDelete(savedSimple._id);
        console.log('🧹 Simple test data cleaned up');
        
      } catch (simpleError) {
        console.log('❌ Simple approach also failed:', simpleError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

testSchemaFix();
