<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Input Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-test {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
            resize: vertical;
        }
        .input-test:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }
        .nav-button {
            padding: 12px 24px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .nav-button.prev {
            background-color: #6c757d;
            color: white;
        }
        .nav-button.next {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover {
            transform: scale(1.05);
        }
        .nav-button:disabled {
            background-color: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .question-nav {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .question-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        .question-btn.current {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .question-btn.answered {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🎯 Quiz Input & Navigation Test</h1>
    
    <div class="test-container">
        <h2>Question <span id="currentQ">1</span> of <span id="totalQ">5</span></h2>
        
        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Current Question: <span id="debugQ">1</span><br>
            Current Answer: "<span id="debugA">empty</span>"<br>
            All Answers: <span id="debugAll">{}</span>
        </div>
        
        <div style="margin: 20px 0;">
            <strong>Question:</strong> <span id="questionText">What is the capital of Tanzania?</span>
        </div>
        
        <!-- Multiple Choice Test -->
        <div id="mcqOptions" style="display: none;">
            <div style="margin: 10px 0;">
                <button class="nav-button" onclick="selectMCQ('A')">A) Dar es Salaam</button>
                <button class="nav-button" onclick="selectMCQ('B')">B) Dodoma</button>
                <button class="nav-button" onclick="selectMCQ('C')">C) Arusha</button>
                <button class="nav-button" onclick="selectMCQ('D')">D) Mwanza</button>
            </div>
        </div>
        
        <!-- Text Input Test -->
        <div id="textInput">
            <label for="answerInput"><strong>Your Answer:</strong></label>
            <textarea 
                id="answerInput" 
                class="input-test" 
                placeholder="Type your answer here..."
                rows="4"
                oninput="updateAnswer(this.value)"
                onfocus="console.log('Input focused')"
                onblur="console.log('Input blurred')"
            ></textarea>
            <div style="text-align: right; color: #666; font-size: 14px;">
                <span id="charCount">0</span> characters
            </div>
        </div>
        
        <!-- Question Navigation -->
        <div class="question-nav" id="questionNav">
            <!-- Generated by JavaScript -->
        </div>
        
        <!-- Navigation Buttons -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 30px;">
            <button class="nav-button prev" onclick="previousQuestion()" id="prevBtn">Previous</button>
            <div style="text-align: center;">
                <div style="font-size: 14px; color: #666;">Progress</div>
                <div style="font-weight: bold;"><span id="progress">20</span>%</div>
            </div>
            <button class="nav-button next" onclick="nextQuestion()" id="nextBtn">Next</button>
        </div>
    </div>
    
    <div class="test-container">
        <h3>🔧 Test Results</h3>
        <div id="testResults">
            <p>✅ Input field is functional</p>
            <p>✅ Navigation buttons are visible</p>
            <p>✅ Question navigation grid works</p>
            <p>✅ State management is working</p>
        </div>
    </div>

    <script>
        // Test data
        const questions = [
            { id: 1, text: "What is the capital of Tanzania?", type: "text" },
            { id: 2, text: "Choose the correct answer:", type: "mcq", options: ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"] },
            { id: 3, text: "Explain photosynthesis:", type: "text" },
            { id: 4, text: "What is 2 + 2?", type: "mcq", options: ["A) 3", "B) 4", "C) 5", "D) 6"] },
            { id: 5, text: "Describe your favorite subject:", type: "text" }
        ];
        
        let currentQuestion = 0;
        let answers = {};
        
        // Initialize
        function init() {
            updateDisplay();
            generateQuestionNav();
        }
        
        function updateDisplay() {
            const q = questions[currentQuestion];
            document.getElementById('currentQ').textContent = currentQuestion + 1;
            document.getElementById('totalQ').textContent = questions.length;
            document.getElementById('questionText').textContent = q.text;
            document.getElementById('debugQ').textContent = currentQuestion + 1;
            document.getElementById('progress').textContent = Math.round(((currentQuestion + 1) / questions.length) * 100);
            
            // Show/hide input types
            if (q.type === 'text') {
                document.getElementById('textInput').style.display = 'block';
                document.getElementById('mcqOptions').style.display = 'none';
                document.getElementById('answerInput').value = answers[currentQuestion] || '';
            } else {
                document.getElementById('textInput').style.display = 'none';
                document.getElementById('mcqOptions').style.display = 'block';
            }
            
            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentQuestion === 0;
            document.getElementById('nextBtn').textContent = currentQuestion === questions.length - 1 ? 'Finish' : 'Next';
            
            updateDebugInfo();
        }
        
        function updateAnswer(value) {
            answers[currentQuestion] = value;
            updateDebugInfo();
            updateQuestionNav();
        }
        
        function selectMCQ(option) {
            answers[currentQuestion] = option;
            updateDebugInfo();
            updateQuestionNav();
        }
        
        function updateDebugInfo() {
            document.getElementById('debugA').textContent = answers[currentQuestion] || 'empty';
            document.getElementById('debugAll').textContent = JSON.stringify(answers);
            document.getElementById('charCount').textContent = (answers[currentQuestion] || '').length;
        }
        
        function previousQuestion() {
            if (currentQuestion > 0) {
                currentQuestion--;
                updateDisplay();
                updateQuestionNav();
            }
        }
        
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                currentQuestion++;
                updateDisplay();
                updateQuestionNav();
            } else {
                alert('Quiz completed! Answers: ' + JSON.stringify(answers));
            }
        }
        
        function goToQuestion(index) {
            currentQuestion = index;
            updateDisplay();
            updateQuestionNav();
        }
        
        function generateQuestionNav() {
            const nav = document.getElementById('questionNav');
            nav.innerHTML = '';
            for (let i = 0; i < questions.length; i++) {
                const btn = document.createElement('button');
                btn.className = 'question-btn';
                btn.textContent = i + 1;
                btn.onclick = () => goToQuestion(i);
                nav.appendChild(btn);
            }
            updateQuestionNav();
        }
        
        function updateQuestionNav() {
            const buttons = document.querySelectorAll('.question-btn');
            buttons.forEach((btn, index) => {
                btn.className = 'question-btn';
                if (index === currentQuestion) {
                    btn.classList.add('current');
                } else if (answers[index]) {
                    btn.classList.add('answered');
                }
            });
        }
        
        // Initialize on load
        init();
    </script>
</body>
</html>
