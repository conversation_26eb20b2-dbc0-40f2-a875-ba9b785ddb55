{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\MathExplanation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MathExplanation = ({\n  explanation\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  useEffect(() => {\n    if (containerRef.current && explanation) {\n      // Process the explanation to render LaTeX math\n      renderMathInExplanation();\n    }\n  }, [explanation]);\n  const renderMathInExplanation = () => {\n    if (!containerRef.current) return;\n\n    // Check if KaTeX is available\n    if (window.katex) {\n      try {\n        // Find and render inline math \\\\( ... \\\\)\n        let processedText = explanation.replace(/\\\\\\((.*?)\\\\\\)/g, (match, math) => {\n          try {\n            return window.katex.renderToString(math, {\n              displayMode: false\n            });\n          } catch (e) {\n            console.warn('KaTeX inline render error:', e);\n            return `<span class=\"math-error\">${math}</span>`;\n          }\n        });\n\n        // Find and render block math \\\\[ ... \\\\]\n        processedText = processedText.replace(/\\\\\\[(.*?)\\\\\\]/g, (match, math) => {\n          try {\n            return window.katex.renderToString(math, {\n              displayMode: true\n            });\n          } catch (e) {\n            console.warn('KaTeX block render error:', e);\n            return `<div class=\"math-error\">${math}</div>`;\n          }\n        });\n\n        // Set the processed HTML\n        containerRef.current.innerHTML = processedText;\n      } catch (error) {\n        console.warn('KaTeX processing error:', error);\n        // Fallback to plain text\n        containerRef.current.textContent = explanation;\n      }\n    } else {\n      // KaTeX not available, show plain text\n      containerRef.current.textContent = explanation;\n    }\n  };\n  const formatMathSteps = text => {\n    // Format mathematical steps for better readability\n    return text.replace(/Step \\d+:/g, match => `<strong class=\"text-blue-800\">${match}</strong>`).replace(/Solution:/g, '<strong class=\"text-green-700\">Solution:</strong>').replace(/Calculations:/g, '<strong class=\"text-purple-700\">Calculations:</strong>').replace(/Answer:/g, '<strong class=\"text-red-700\">Answer:</strong>').replace(/Therefore:/g, '<strong class=\"text-indigo-700\">Therefore:</strong>');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"math-explanation\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .math-explanation {\n          font-family: 'Times New Roman', serif;\n          line-height: 1.8;\n        }\n        .math-explanation .katex {\n          font-size: 1.1em;\n        }\n        .math-explanation .katex-display {\n          margin: 1em 0;\n          text-align: center;\n        }\n        .math-error {\n          background-color: #fee2e2;\n          color: #dc2626;\n          padding: 2px 4px;\n          border-radius: 4px;\n          font-family: monospace;\n        }\n        .math-explanation strong {\n          font-weight: 600;\n          margin-top: 0.5em;\n          display: block;\n        }\n        .math-explanation p {\n          margin: 0.5em 0;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      className: \"whitespace-pre-wrap\",\n      dangerouslySetInnerHTML: {\n        __html: formatMathSteps(explanation || '')\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), !window.katex && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Note:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this), \" Mathematical expressions are displayed in text format. For better formatting, please ensure your browser supports mathematical rendering.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(MathExplanation, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = MathExplanation;\nexport default MathExplanation;\nvar _c;\n$RefreshReg$(_c, \"MathExplanation\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "MathExplanation", "explanation", "_s", "containerRef", "current", "renderMathInExplanation", "window", "katex", "processedText", "replace", "match", "math", "renderToString", "displayMode", "e", "console", "warn", "innerHTML", "error", "textContent", "formatMathSteps", "text", "className", "children", "jsx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "dangerouslySetInnerHTML", "__html", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/MathExplanation.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst MathExplanation = ({ explanation }) => {\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    if (containerRef.current && explanation) {\n      // Process the explanation to render LaTeX math\n      renderMathInExplanation();\n    }\n  }, [explanation]);\n\n  const renderMathInExplanation = () => {\n    if (!containerRef.current) return;\n\n    // Check if KaTeX is available\n    if (window.katex) {\n      try {\n        // Find and render inline math \\\\( ... \\\\)\n        let processedText = explanation.replace(/\\\\\\((.*?)\\\\\\)/g, (match, math) => {\n          try {\n            return window.katex.renderToString(math, { displayMode: false });\n          } catch (e) {\n            console.warn('KaTeX inline render error:', e);\n            return `<span class=\"math-error\">${math}</span>`;\n          }\n        });\n\n        // Find and render block math \\\\[ ... \\\\]\n        processedText = processedText.replace(/\\\\\\[(.*?)\\\\\\]/g, (match, math) => {\n          try {\n            return window.katex.renderToString(math, { displayMode: true });\n          } catch (e) {\n            console.warn('KaTeX block render error:', e);\n            return `<div class=\"math-error\">${math}</div>`;\n          }\n        });\n\n        // Set the processed HTML\n        containerRef.current.innerHTML = processedText;\n      } catch (error) {\n        console.warn('KaTeX processing error:', error);\n        // Fallback to plain text\n        containerRef.current.textContent = explanation;\n      }\n    } else {\n      // KaTeX not available, show plain text\n      containerRef.current.textContent = explanation;\n    }\n  };\n\n  const formatMathSteps = (text) => {\n    // Format mathematical steps for better readability\n    return text\n      .replace(/Step \\d+:/g, (match) => `<strong class=\"text-blue-800\">${match}</strong>`)\n      .replace(/Solution:/g, '<strong class=\"text-green-700\">Solution:</strong>')\n      .replace(/Calculations:/g, '<strong class=\"text-purple-700\">Calculations:</strong>')\n      .replace(/Answer:/g, '<strong class=\"text-red-700\">Answer:</strong>')\n      .replace(/Therefore:/g, '<strong class=\"text-indigo-700\">Therefore:</strong>');\n  };\n\n  return (\n    <div className=\"math-explanation\">\n      <style jsx>{`\n        .math-explanation {\n          font-family: 'Times New Roman', serif;\n          line-height: 1.8;\n        }\n        .math-explanation .katex {\n          font-size: 1.1em;\n        }\n        .math-explanation .katex-display {\n          margin: 1em 0;\n          text-align: center;\n        }\n        .math-error {\n          background-color: #fee2e2;\n          color: #dc2626;\n          padding: 2px 4px;\n          border-radius: 4px;\n          font-family: monospace;\n        }\n        .math-explanation strong {\n          font-weight: 600;\n          margin-top: 0.5em;\n          display: block;\n        }\n        .math-explanation p {\n          margin: 0.5em 0;\n        }\n      `}</style>\n      \n      <div \n        ref={containerRef}\n        className=\"whitespace-pre-wrap\"\n        dangerouslySetInnerHTML={{ \n          __html: formatMathSteps(explanation || '') \n        }}\n      />\n      \n      {/* Fallback for when KaTeX is not available */}\n      {!window.katex && (\n        <div className=\"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800\">\n          <strong>Note:</strong> Mathematical expressions are displayed in text format. \n          For better formatting, please ensure your browser supports mathematical rendering.\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MathExplanation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,YAAY,GAAGN,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,IAAIO,YAAY,CAACC,OAAO,IAAIH,WAAW,EAAE;MACvC;MACAI,uBAAuB,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACJ,WAAW,CAAC,CAAC;EAEjB,MAAMI,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACF,YAAY,CAACC,OAAO,EAAE;;IAE3B;IACA,IAAIE,MAAM,CAACC,KAAK,EAAE;MAChB,IAAI;QACF;QACA,IAAIC,aAAa,GAAGP,WAAW,CAACQ,OAAO,CAAC,gBAAgB,EAAE,CAACC,KAAK,EAAEC,IAAI,KAAK;UACzE,IAAI;YACF,OAAOL,MAAM,CAACC,KAAK,CAACK,cAAc,CAACD,IAAI,EAAE;cAAEE,WAAW,EAAE;YAAM,CAAC,CAAC;UAClE,CAAC,CAAC,OAAOC,CAAC,EAAE;YACVC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEF,CAAC,CAAC;YAC7C,OAAQ,4BAA2BH,IAAK,SAAQ;UAClD;QACF,CAAC,CAAC;;QAEF;QACAH,aAAa,GAAGA,aAAa,CAACC,OAAO,CAAC,gBAAgB,EAAE,CAACC,KAAK,EAAEC,IAAI,KAAK;UACvE,IAAI;YACF,OAAOL,MAAM,CAACC,KAAK,CAACK,cAAc,CAACD,IAAI,EAAE;cAAEE,WAAW,EAAE;YAAK,CAAC,CAAC;UACjE,CAAC,CAAC,OAAOC,CAAC,EAAE;YACVC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,CAAC,CAAC;YAC5C,OAAQ,2BAA0BH,IAAK,QAAO;UAChD;QACF,CAAC,CAAC;;QAEF;QACAR,YAAY,CAACC,OAAO,CAACa,SAAS,GAAGT,aAAa;MAChD,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdH,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEE,KAAK,CAAC;QAC9C;QACAf,YAAY,CAACC,OAAO,CAACe,WAAW,GAAGlB,WAAW;MAChD;IACF,CAAC,MAAM;MACL;MACAE,YAAY,CAACC,OAAO,CAACe,WAAW,GAAGlB,WAAW;IAChD;EACF,CAAC;EAED,MAAMmB,eAAe,GAAIC,IAAI,IAAK;IAChC;IACA,OAAOA,IAAI,CACRZ,OAAO,CAAC,YAAY,EAAGC,KAAK,IAAM,iCAAgCA,KAAM,WAAU,CAAC,CACnFD,OAAO,CAAC,YAAY,EAAE,mDAAmD,CAAC,CAC1EA,OAAO,CAAC,gBAAgB,EAAE,wDAAwD,CAAC,CACnFA,OAAO,CAAC,UAAU,EAAE,+CAA+C,CAAC,CACpEA,OAAO,CAAC,aAAa,EAAE,qDAAqD,CAAC;EAClF,CAAC;EAED,oBACEV,OAAA;IAAKuB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BxB,OAAA;MAAOyB,GAAG;MAAAD,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEV7B,OAAA;MACE8B,GAAG,EAAE1B,YAAa;MAClBmB,SAAS,EAAC,qBAAqB;MAC/BQ,uBAAuB,EAAE;QACvBC,MAAM,EAAEX,eAAe,CAACnB,WAAW,IAAI,EAAE;MAC3C;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGD,CAACtB,MAAM,CAACC,KAAK,iBACZR,OAAA;MAAKuB,SAAS,EAAC,gFAAgF;MAAAC,QAAA,gBAC7FxB,OAAA;QAAAwB,QAAA,EAAQ;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,8IAExB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA3GIF,eAAe;AAAAgC,EAAA,GAAfhC,eAAe;AA6GrB,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}