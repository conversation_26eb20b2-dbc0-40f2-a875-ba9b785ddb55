const mongoose = require("mongoose");
require("dotenv").config();

async function fixDatabaseConnection() {
  console.log("🔧 Diagnosing and fixing database connection...");
  
  // Test different connection approaches
  const connections = [
    {
      name: "Primary MongoDB URL",
      url: process.env.MONGO_URL
    },
    {
      name: "Fallback MongoDB URL", 
      url: process.env.MONGO_URL_FALLBACK
    },
    {
      name: "Direct IP Connection (if DNS fails)",
      url: "*********************************************************************************************************************************************************************************"
    }
  ];

  for (const conn of connections) {
    try {
      console.log(`\n🔍 Testing: ${conn.name}`);
      console.log(`📡 URL: ${conn.url.substring(0, 50)}...`);
      
      await mongoose.connect(conn.url, {
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        family: 4, // Force IPv4
        maxPoolSize: 10,
        retryWrites: true
      });
      
      console.log("✅ Connection successful!");
      
      // Test basic operations
      const admin = mongoose.connection.db.admin();
      const result = await admin.ping();
      console.log("✅ Database ping successful:", result);
      
      // Count some documents to verify
      const collections = await mongoose.connection.db.listCollections().toArray();
      console.log(`📊 Found ${collections.length} collections`);
      
      await mongoose.disconnect();
      console.log("✅ Disconnected successfully");
      
      // Update .env with working connection
      if (conn.url !== process.env.MONGO_URL) {
        console.log(`\n🔧 Working connection found: ${conn.name}`);
        console.log("💡 Consider updating your .env MONGO_URL with this working connection");
      }
      
      return conn.url;
      
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`);
      
      if (error.message.includes("ENOTFOUND")) {
        console.log("   🔍 DNS resolution issue detected");
      } else if (error.message.includes("ETIMEOUT")) {
        console.log("   ⏱️ Connection timeout - network/firewall issue");
      } else if (error.message.includes("Authentication failed")) {
        console.log("   🔐 Authentication issue - check credentials");
      }
    }
  }
  
  console.log("\n❌ All connection attempts failed!");
  console.log("\n🔧 Troubleshooting steps:");
  console.log("1. Check internet connection");
  console.log("2. Flush DNS cache: ipconfig /flushdns");
  console.log("3. Change DNS to Google: *******, *******");
  console.log("4. Check MongoDB Atlas IP whitelist");
  console.log("5. Verify MongoDB Atlas cluster is running");
  
  return null;
}

// Run the fix
fixDatabaseConnection()
  .then((workingUrl) => {
    if (workingUrl) {
      console.log("\n🎉 Database connection fixed!");
      console.log("✅ You can now start the server normally");
    } else {
      console.log("\n⚠️ Could not establish database connection");
      console.log("💡 Server can still run without database, but features will be limited");
    }
  })
  .catch((error) => {
    console.error("❌ Fix attempt failed:", error);
  });
