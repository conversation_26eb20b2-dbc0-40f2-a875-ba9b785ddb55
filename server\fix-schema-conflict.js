const mongoose = require('mongoose');
require('dotenv').config();

async function fixSchemaConflict() {
  try {
    console.log('🔧 Fixing Schema Conflict...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Check existing collections
    console.log('📋 Checking existing collections...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    const aiGenerationCollection = collections.find(c => c.name === 'ai-question-generations');
    
    if (aiGenerationCollection) {
      console.log('✅ Found ai-question-generations collection');
      
      // Check existing documents structure
      const existingDocs = await mongoose.connection.db.collection('ai-question-generations').find({}).limit(1).toArray();
      
      if (existingDocs.length > 0) {
        console.log('📄 Existing document structure:');
        console.log('- Has generatedQuestions:', !!existingDocs[0].generatedQuestions);
        if (existingDocs[0].generatedQuestions && existingDocs[0].generatedQuestions.length > 0) {
          const firstQuestion = existingDocs[0].generatedQuestions[0];
          console.log('- First question structure:');
          console.log('  - Has generatedContent:', !!firstQuestion.generatedContent);
          console.log('  - generatedContent type:', typeof firstQuestion.generatedContent);
          if (firstQuestion.generatedContent) {
            console.log('  - generatedContent keys:', Object.keys(firstQuestion.generatedContent));
          }
        }
      } else {
        console.log('📄 No existing documents found');
      }
    } else {
      console.log('❌ ai-question-generations collection not found');
    }
    
    console.log('\n🔄 Attempting to create a simple schema test...');
    
    // Create a completely new schema to test
    const testSchema = new mongoose.Schema({
      testField: String,
      testQuestions: [{
        questionData: {
          name: String,
          topic: String,
          classLevel: String,
          type: String,
          correctAnswer: String,
          options: mongoose.Schema.Types.Mixed
        },
        approved: { type: Boolean, default: false }
      }]
    });
    
    // Use a different collection name to avoid conflicts
    const TestModel = mongoose.model('test-ai-generations', testSchema);
    
    console.log('💾 Testing with new schema...');
    
    const testDoc = new TestModel({
      testField: "test",
      testQuestions: [{
        questionData: {
          name: "Test question?",
          topic: "Science",
          classLevel: "primary 3",
          type: "mcq",
          correctAnswer: "A",
          options: { A: "Answer A", B: "Answer B", C: "Answer C", D: "Answer D" }
        },
        approved: false
      }]
    });
    
    try {
      const savedTest = await testDoc.save();
      console.log('✅ Test schema works! The issue is with the existing schema.');
      console.log(`- Test document ID: ${savedTest._id}`);
      console.log(`- Question data saved: ${savedTest.testQuestions[0].questionData.name}`);
      
      // Clean up test
      await TestModel.findByIdAndDelete(savedTest._id);
      await mongoose.connection.db.dropCollection('test-ai-generations');
      console.log('🧹 Test collection cleaned up');
      
      console.log('\n🎯 Solution: The schema structure is correct, but there might be a conflict.');
      console.log('💡 Recommendation: Try using Mixed type for generatedContent');
      
    } catch (testError) {
      console.log('❌ Test schema also failed:', testError.message);
    }
    
    console.log('\n🔧 Attempting to fix the original schema...');
    
    // Try to fix by using Mixed type
    const fixedSchema = new mongoose.Schema({
      requestedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
      examId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "exams",
      },
      generationParams: {
        questionTypes: [String],
        subjects: [String],
        level: String,
        class: String,
        difficultyLevels: [String],
        syllabusTopics: [String],
        totalQuestions: Number,
        questionDistribution: mongoose.Schema.Types.Mixed,
        selectedSyllabusId: String
      },
      generationStatus: {
        type: String,
        enum: ["pending", "in_progress", "completed", "failed", "cancelled"],
        default: "pending",
      },
      generatedQuestions: [{
        questionId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "questions",
        },
        generatedContent: mongoose.Schema.Types.Mixed, // Use Mixed type instead of nested schema
        approved: {
          type: Boolean,
          default: false,
        },
        rejectionReason: String,
        editedContent: mongoose.Schema.Types.Mixed,
      }],
      aiModel: {
        type: String,
        default: "gpt-3.5-turbo",
      },
      generationTime: Number,
      errorMessage: String,
      qualityScore: Number,
    }, {
      timestamps: true,
    });
    
    // Drop the existing model from mongoose cache
    delete mongoose.connection.models['ai-question-generations'];
    
    // Create new model with fixed schema
    const FixedAIGeneration = mongoose.model("ai-question-generations", fixedSchema);
    
    console.log('💾 Testing fixed schema...');
    
    const fixedTest = new FixedAIGeneration({
      requestedBy: new mongoose.Types.ObjectId(),
      generationParams: {
        questionTypes: ["multiple_choice"],
        subjects: ["Science and Technology"],
        level: "primary",
        class: "3",
        totalQuestions: 1,
        questionDistribution: { multiple_choice: 1, fill_blank: 0, picture_based: 0 }
      },
      generationStatus: "completed",
      generatedQuestions: [{
        generatedContent: {
          name: "Fixed test question?",
          type: "mcq",
          topic: "Science",
          classLevel: "primary 3",
          correctAnswer: "A",
          options: { A: "Answer A", B: "Answer B", C: "Answer C", D: "Answer D" }
        },
        approved: false
      }],
      aiModel: "gpt-3.5-turbo",
      qualityScore: 90
    });
    
    try {
      const savedFixed = await fixedTest.save();
      console.log('✅ Fixed schema works!');
      console.log(`- Fixed document ID: ${savedFixed._id}`);
      console.log(`- Question saved: ${savedFixed.generatedQuestions[0].generatedContent.name}`);
      
      // Clean up
      await FixedAIGeneration.findByIdAndDelete(savedFixed._id);
      console.log('🧹 Fixed test data cleaned up');
      
      console.log('\n🎉 Schema fix successful!');
      console.log('✅ Using Mixed type for generatedContent resolves the issue');
      console.log('💡 Update the model to use Mixed type for generatedContent');
      
    } catch (fixedError) {
      console.log('❌ Fixed schema failed:', fixedError.message);
    }
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

fixSchemaConflict();
