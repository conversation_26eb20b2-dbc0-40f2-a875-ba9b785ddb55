import React, { useState, useEffect, useRef } from "react";
import "./index.css";
import { getUserInfo } from "../../../apicalls/users";
import { message, Button, Input, Form, Avatar, Pagination } from "antd";
import PageTitle from "../../../components/PageTitle";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import ProfilePicture from "../../../components/common/ProfilePicture";
import OnlineStatusIndicator from "../../../components/common/OnlineStatusIndicator";
import {
  addQuestion,
  addReply,
  getAllQuestions,
  deleteQuestion,
  updateQuestion,
  updateReplyStatus,
} from "../../../apicalls/forum";
import image from "../../../assets/person.png";
import { FaPencilAlt } from "react-icons/fa";
import { MdDelete, MdMessage } from "react-icons/md";
import { FaCheck } from "react-icons/fa";

const Forum = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [userData, setUserData] = useState("");
  const [questions, setQuestions] = useState([]);
  const [expandedReplies, setExpandedReplies] = useState({});
  const [askQuestionVisible, setAskQuestionVisible] = useState(false);
  const [replyQuestionId, setReplyQuestionId] = useState(null);
  const [editQuestion, setEditQuestion] = useState(null);
  const [form] = Form.useForm();
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Skeleton loader component
  const ForumSkeleton = () => (
    <div className="forum-container">
      <div className="forum-header">
        <div className="h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
        <div className="h-10 bg-blue-100 rounded w-32 animate-pulse"></div>
      </div>
      <div className="forum-content">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="question-card mb-4 p-4 border rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse"></div>
                <div className="h-3 bg-gray-100 rounded w-full mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-100 rounded w-2/3 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
  const [form2] = Form.useForm();
  const dispatch = useDispatch();
  const [replyRefs, setReplyRefs] = useState({});

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(10);
  const [totalQuestions, setTotalQuestions] = useState(0);

  const fetchQuestions = async (page) => {
    try {
      // Check cache first
      const cacheKey = `forum_questions_${page}_${limit}`;
      const cachedData = localStorage.getItem(cacheKey);
      const cacheTime = localStorage.getItem(`${cacheKey}_time`);
      const now = Date.now();

      // Use cache if less than 5 minutes old
      if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 300000) {
        const cached = JSON.parse(cachedData);
        setQuestions(cached.questions);
        setTotalQuestions(cached.totalQuestions);
        setTotalPages(cached.totalPages);
        return;
      }

      dispatch(ShowLoading());
      const response = await getAllQuestions({ page, limit }); // Pass query params to API call
      if (response.success) {
        console.log(response.data);
        setQuestions(response.data); // No need to reverse as backend will handle order
        setTotalQuestions(response.totalQuestions);
        setTotalPages(response.totalPages);

        // Cache the data
        localStorage.setItem(cacheKey, JSON.stringify({
          questions: response.data,
          totalQuestions: response.totalQuestions,
          totalPages: response.totalPages
        }));
        localStorage.setItem(`${cacheKey}_time`, now.toString());
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      dispatch(HideLoading());
    }
  };

  useEffect(() => {
    fetchQuestions(currentPage).finally(() => {
      setIsInitialLoad(false);
    });
  }, [currentPage, limit]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getUserData = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getUserInfo();
      if (response.success) {
        if (response.data.isAdmin) {
          setIsAdmin(true);
          setUserData(response.data);
          await fetchQuestions();
        } else {
          setIsAdmin(false);
          setUserData(response.data);
          await fetchQuestions();
        }
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  useEffect(() => {
    if (localStorage.getItem("token")) {
      getUserData();
    }
  }, []);

  const toggleReplies = (questionId) => {
    setExpandedReplies((prevExpandedReplies) => ({
      ...prevExpandedReplies,
      [questionId]: !prevExpandedReplies[questionId],
    }));
  };

  const handleAskQuestion = async (values) => {
    try {
      const response = await addQuestion(values);
      if (response.success) {
        message.success(response.message);
        setAskQuestionVisible(false);
        form.resetFields();
        await fetchQuestions();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  const handleReply = (questionId) => {
    setReplyQuestionId(questionId);
  };

  const handleReplySubmit = async (values) => {
    try {
      const payload = {
        questionId: replyQuestionId,
        text: values.text,
      };
      const response = await addReply(payload);
      if (response.success) {
        message.success(response.message);
        setReplyQuestionId(null);
        form.resetFields();
        await fetchQuestions();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  useEffect(() => {
    if (replyQuestionId && !replyRefs[replyQuestionId]) {
      setReplyRefs((prevRefs) => ({
        ...prevRefs,
        [replyQuestionId]: React.createRef(),
      }));
    }
  }, [replyQuestionId, replyRefs]);

  useEffect(() => {
    if (replyQuestionId && replyRefs[replyQuestionId]) {
      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: "smooth" });
    }
  }, [replyQuestionId, replyRefs]);

  const handleEdit = (question) => {
    setEditQuestion(question);
  };

  const handleDelete = async (question) => {
    try {
      const confirmDelete = window.confirm(
        "Are you sure you want to delete this question?"
      );
      if (!confirmDelete) {
        return;
      }
      const response = await deleteQuestion(question._id);
      if (response.success) {
        message.success(response.message);
        await fetchQuestions();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  const handleUpdateQuestion = async (values) => {
    try {
      const response = await updateQuestion(values, editQuestion._id);
      if (response.success) {
        message.success(response.message);
        setEditQuestion(null);
        await fetchQuestions();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  const handleCancelUpdate = () => {
    setEditQuestion("");
  };
  const handleCancelAdd = () => {
    setAskQuestionVisible(false);
    form.resetFields();
  };

  useEffect(() => {
    if (editQuestion) {
      form2.setFieldsValue({
        title: editQuestion.title,
        body: editQuestion.body,
      });
    } else {
      form2.resetFields();
    }
  }, [editQuestion]);

  const handleUpdateStatus = async (questionId, replyId, status) => {
    try {
      const response = await updateReplyStatus({ replyId, status }, questionId);
      if (response.success) {
        message.success(response.message);
        await fetchQuestions();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  // Show skeleton on initial load
  if (isInitialLoad && questions.length === 0) {
    return <ForumSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Modern Header Section */}
        <div className="text-center mb-8 sm:mb-10 lg:mb-12">
          <div className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg">
            <i className="ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white"></i>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Community <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Forum</span>
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4">
            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.
          </p>

          {/* Ask Question Button */}
          <button
            onClick={() => setAskQuestionVisible(true)}
            className="inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base"
          >
            <i className="ri-add-line text-lg sm:text-xl mr-2"></i>
            Ask a Question
          </button>
        </div>

        {/* Modern Ask Question Form */}
        {askQuestionVisible && (
          <div className="bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100">
            <div className="flex items-center mb-4 sm:mb-6">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                <i className="ri-question-line text-white text-sm sm:text-lg"></i>
              </div>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Ask a Question</h2>
            </div>

            <Form form={form} onFinish={handleAskQuestion} layout="vertical" className="modern-form">
              <Form.Item
                name="title"
                label="Question Title"
                rules={[{ required: true, message: "Please enter a descriptive title" }]}
              >
                <Input
                  placeholder="What would you like to know?"
                  className="h-12 text-lg"
                />
              </Form.Item>
              <Form.Item
                name="body"
                label="Question Details"
                rules={[{ required: true, message: "Please provide more details about your question" }]}
              >
                <Input.TextArea
                  rows={6}
                  placeholder="Describe your question in detail. The more information you provide, the better answers you'll receive."
                  className="text-base"
                />
              </Form.Item>
              <Form.Item className="mb-0">
                <div className="flex items-center space-x-4">
                  <Button
                    type="primary"
                    htmlType="submit"
                    className="h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base"
                  >
                    <i className="ri-send-plane-line mr-2"></i>
                    Post Question
                  </Button>
                  <Button
                    onClick={handleCancelAdd}
                    className="h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50"
                  >
                    Cancel
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </div>
        )}

        {/* Loading State */}
        {questions.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <i className="ri-loader-4-line text-2xl text-gray-400 animate-spin"></i>
            </div>
            <p className="text-gray-500">Loading discussions...</p>
          </div>
        )}

        {/* Questions Grid */}
        <div className="space-y-4 sm:space-y-6">
          {questions.map((question) => (
            <div key={question._id} className="bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
              {/* Question Header */}
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="relative">
                      <ProfilePicture
                        user={question.user}
                        size="sm"
                        showOnlineStatus={false}
                        style={{
                          width: '32px',
                          height: '32px'
                        }}
                      />
                      {/* Only show online dot if user is actually online */}
                      {question.user.isOnline && (
                        <div
                          style={{
                            position: 'absolute',
                            bottom: '-2px',
                            right: '-2px',
                            width: '12px',
                            height: '12px',
                            backgroundColor: '#22c55e',
                            borderRadius: '50%',
                            border: '2px solid #ffffff',
                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',
                            zIndex: 10
                          }}
                          title="Online"
                        />
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{question.user.name}</h4>
                      <p className="text-xs sm:text-sm text-gray-500">
                        {new Date(question.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-1 sm:space-x-2 ml-2">
                    {(userData._id === question.user._id || userData.isAdmin) && (
                      <>
                        <button
                          onClick={() => handleEdit(question)}
                          className="flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        >
                          <FaPencilAlt className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(question)}
                          className="flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        >
                          <MdDelete className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Question Content */}
              <div className="p-4 sm:p-6">
                <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight">{question.title}</h3>
                <p className="text-gray-700 leading-relaxed mb-6">{question.body}</p>

                {/* Action Bar */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0">
                  <div className="flex items-center space-x-2 sm:space-x-4 overflow-x-auto">
                    <button
                      onClick={() => toggleReplies(question._id)}
                      className="flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap"
                    >
                      <i className="ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base"></i>
                      {expandedReplies[question._id] ? "Hide Replies" : "View Replies"}
                    </button>
                    <button
                      onClick={() => handleReply(question._id)}
                      className="flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap"
                    >
                      <i className="ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base"></i>
                      Reply
                    </button>
                  </div>

                  <div className="flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto">
                    <MdMessage className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2" />
                    <span className="text-xs sm:text-sm font-medium text-gray-700">{question.replies.length}</span>
                  </div>
                </div>
              </div>

              {/* Edit Question Form */}
              {editQuestion && editQuestion._id === question._id && (
                <Form
                form={form2}
                onFinish={handleUpdateQuestion}
                layout="vertical"
                initialValues={{
                  title: editQuestion.title,
                  body: editQuestion.body,
                }}
              >
                <Form.Item
                  name="title"
                  label="Title"
                  rules={[
                    { required: true, message: "Please enter the title" },
                  ]}
                >
                  <Input style={{ padding: "18px 12px" }} />
                </Form.Item>
                <Form.Item
                  name="body"
                  label="Body"
                  rules={[{ required: true, message: "Please enter the body" }]}
                >
                  <Input.TextArea />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    Update Question
                  </Button>
                  <Button
                    onClick={handleCancelUpdate}
                    style={{ marginLeft: 10 }}
                  >
                    Cancel
                  </Button>
                </Form.Item>
              </Form>
              )}
            {expandedReplies[question._id] && (
              <div className="mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4">
                <h4 className="text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                  <i className="ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base"></i>
                  Replies ({question.replies.length})
                </h4>
                {question.replies.map((reply, index) => (
                  <div
                    key={reply._id}
                    className={`bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ${
                      reply.user.isAdmin
                        ? "border-purple-500 bg-purple-50"
                        : reply.isVerified
                        ? "border-green-500 bg-green-50"
                        : "border-gray-300"
                    }`}
                  >
                    <div className="flex items-start space-x-2 sm:space-x-3">
                      {/* Avatar with Online Status */}
                      <div className="flex-shrink-0 relative">
                        <ProfilePicture
                          user={reply.user}
                          size="xs"
                          showOnlineStatus={false}
                          style={{
                            width: '20px',
                            height: '20px'
                          }}
                          className="sm:w-6 sm:h-6"
                        />
                        {/* Only show online dot if user is actually online */}
                        {reply.user.isOnline && (
                          <div
                            style={{
                              position: 'absolute',
                              bottom: '-1px',
                              right: '-1px',
                              width: '6px',
                              height: '6px',
                              backgroundColor: '#22c55e',
                              borderRadius: '50%',
                              border: '1px solid #ffffff',
                              boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',
                              zIndex: 10
                            }}
                            className="sm:w-2 sm:h-2"
                            title="Online"
                          />
                        )}
                      </div>

                      {/* Reply Content */}
                      <div className="flex-1 min-w-0">
                        {/* Header */}
                        <div className="flex items-start sm:items-center justify-between mb-2 gap-2">
                          <div className="flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1">
                            <h5 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{reply.user.name}</h5>
                            {reply.user.isAdmin && (
                              <span className="px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap">
                                Admin
                              </span>
                            )}
                            {reply.isVerified && !reply.user.isAdmin && (
                              <div className="flex items-center space-x-1">
                                <FaCheck className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
                                <span className="px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap">
                                  Verified
                                </span>
                              </div>
                            )}
                          </div>
                          <span className="text-xs sm:text-sm text-gray-500 whitespace-nowrap">
                            {(() => {
                              try {
                                const date = new Date(reply.createdAt);
                                if (isNaN(date.getTime())) {
                                  return 'Invalid date';
                                }
                                return date.toLocaleDateString('en-US', {
                                  month: "short",
                                  day: "numeric",
                                  hour: "2-digit",
                                  minute: "2-digit"
                                });
                              } catch (error) {
                                return 'Invalid date';
                              }
                            })()}
                          </span>
                        </div>

                        {/* Reply Text */}
                        <div className={`leading-relaxed mb-3 text-sm sm:text-base ${
                          reply.isVerified && !reply.user.isAdmin
                            ? 'text-green-800 font-medium'
                            : reply.user.isAdmin
                            ? 'text-purple-800 font-medium'
                            : 'text-gray-700'
                        }`}>
                          {reply.text}
                        </div>

                        {/* Admin Actions */}
                        {isAdmin && !reply.user.isAdmin && (
                          <div className="flex justify-end">
                            <button
                              onClick={() =>
                                handleUpdateStatus(
                                  question._id,
                                  reply._id,
                                  !reply.isVerified
                                )
                              }
                              className={`px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ${
                                reply.isVerified
                                  ? "bg-red-100 text-red-700 hover:bg-red-200"
                                  : "bg-green-100 text-green-700 hover:bg-green-200"
                              }`}
                            >
                              {reply.isVerified ? "Disapprove" : "Approve"}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div ref={replyRefs[question._id]} className="mt-4 sm:mt-6">
              {replyQuestionId === question._id && (
                <div className="bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200">
                  <Form
                    form={form}
                    onFinish={handleReplySubmit}
                    layout="vertical"
                  >
                    <Form.Item
                      name="text"
                      label={<span className="text-sm sm:text-base font-medium">Your Reply</span>}
                      rules={[
                        { required: true, message: "Please enter your reply" },
                      ]}
                    >
                      <Input.TextArea
                        rows={3}
                        className="text-sm sm:text-base"
                        placeholder="Write your reply here..."
                      />
                    </Form.Item>
                    <Form.Item className="mb-0">
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                        <Button
                          type="primary"
                          htmlType="submit"
                          className="w-full sm:w-auto"
                          size="large"
                        >
                          Submit Reply
                        </Button>
                        <Button
                          onClick={() => setReplyQuestionId(null)}
                          className="w-full sm:w-auto"
                          size="large"
                        >
                          Cancel
                        </Button>
                      </div>
                    </Form.Item>
                  </Form>
                </div>
              )}
            </div>
            </div>
          ))}

        </div>

        <Pagination
          current={currentPage}
          total={totalQuestions}
          pageSize={limit}
          onChange={handlePageChange}
          style={{ marginTop: "20px", textAlign: "center" }}
          showSizeChanger={false}
        />
      </div>
    </div>
  );
};

export default Forum;
