import axiosInstance from "./index";

// Get trial quiz based on level and class (no authentication required)
export const getTrialQuiz = async (payload) => {
  try {
    const response = await axiosInstance.post("/api/trial/get-trial-quiz", payload);
    return response.data;
  } catch (error) {
    return error.response?.data || { success: false, message: "Network error" };
  }
};

// Submit trial quiz results (no authentication required)
export const submitTrialResult = async (payload) => {
  try {
    const response = await axiosInstance.post("/api/trial/submit-trial-result", payload);
    return response.data;
  } catch (error) {
    return error.response?.data || { success: false, message: "Network error" };
  }
};

// Get trial statistics (optional)
export const getTrialStats = async () => {
  try {
    const response = await axiosInstance.get("/api/trial/trial-stats");
    return response.data;
  } catch (error) {
    return error.response?.data || { success: false, message: "Network error" };
  }
};
