import React, { useEffect, useRef } from 'react';

const MathExplanation = ({ explanation }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    if (containerRef.current && explanation) {
      // Process the explanation to render LaTeX math
      renderMathInExplanation();
    }
  }, [explanation]);

  const renderMathInExplanation = () => {
    if (!containerRef.current) return;

    // Check if KaTeX is available
    if (window.katex) {
      try {
        // Find and render inline math \\( ... \\)
        let processedText = explanation.replace(/\\\((.*?)\\\)/g, (match, math) => {
          try {
            return window.katex.renderToString(math, { displayMode: false });
          } catch (e) {
            console.warn('KaTeX inline render error:', e);
            return `<span class="math-error">${math}</span>`;
          }
        });

        // Find and render block math \\[ ... \\]
        processedText = processedText.replace(/\\\[(.*?)\\\]/g, (match, math) => {
          try {
            return window.katex.renderToString(math, { displayMode: true });
          } catch (e) {
            console.warn('KaTeX block render error:', e);
            return `<div class="math-error">${math}</div>`;
          }
        });

        // Set the processed HTML
        containerRef.current.innerHTML = processedText;
      } catch (error) {
        console.warn('KaTeX processing error:', error);
        // Fallback to plain text
        containerRef.current.textContent = explanation;
      }
    } else {
      // KaTeX not available, show plain text
      containerRef.current.textContent = explanation;
    }
  };

  const formatMathSteps = (text) => {
    // Format mathematical steps for better readability
    return text
      .replace(/Step \d+:/g, (match) => `<strong class="text-blue-800">${match}</strong>`)
      .replace(/Solution:/g, '<strong class="text-green-700">Solution:</strong>')
      .replace(/Calculations:/g, '<strong class="text-purple-700">Calculations:</strong>')
      .replace(/Answer:/g, '<strong class="text-red-700">Answer:</strong>')
      .replace(/Therefore:/g, '<strong class="text-indigo-700">Therefore:</strong>');
  };

  return (
    <div className="math-explanation">
      <style jsx>{`
        .math-explanation {
          font-family: 'Times New Roman', serif;
          line-height: 1.8;
        }
        .math-explanation .katex {
          font-size: 1.1em;
        }
        .math-explanation .katex-display {
          margin: 1em 0;
          text-align: center;
        }
        .math-error {
          background-color: #fee2e2;
          color: #dc2626;
          padding: 2px 4px;
          border-radius: 4px;
          font-family: monospace;
        }
        .math-explanation strong {
          font-weight: 600;
          margin-top: 0.5em;
          display: block;
        }
        .math-explanation p {
          margin: 0.5em 0;
        }
      `}</style>
      
      <div 
        ref={containerRef}
        className="whitespace-pre-wrap"
        dangerouslySetInnerHTML={{ 
          __html: formatMathSteps(explanation || '') 
        }}
      />
      
      {/* Fallback for when KaTeX is not available */}
      {!window.katex && (
        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
          <strong>Note:</strong> Mathematical expressions are displayed in text format. 
          For better formatting, please ensure your browser supports mathematical rendering.
        </div>
      )}
    </div>
  );
};

export default MathExplanation;
