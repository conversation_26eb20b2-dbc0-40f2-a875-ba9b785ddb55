/* ===== RESPONSIVE LOGIN PAGE ===== */

.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);
    z-index: 0;
}

.login-card {
    width: 100%;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.login-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* ===== HEADER SECTION ===== */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    display: block;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.login-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);
}

.login-title {
    font-size: 1.875rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.login-subtitle {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* ===== FORM STYLING ===== */
.login-form {
    margin-bottom: 1.5rem;
}

.login-form .ant-form-item {
    margin-bottom: 1.5rem;
}

.login-form .ant-form-item-label > label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #f9fafb;
    color: #1f2937;
    font-family: inherit;
    line-height: 1.5;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
    margin-top: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);
}

.login-btn:active {
    transform: translateY(0);
}

/* ===== FOOTER SECTION ===== */
.login-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.login-footer p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.login-link {
    color: #007BFF;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.login-link:hover {
    color: #0056D2;
    text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .login-container {
        padding: 0.75rem;
        align-items: flex-start;
        padding-top: 2rem;
    }

    .login-card {
        max-width: 100%;
        padding: 1.5rem;
        border-radius: 1rem;
        margin: 0;
    }

    .login-logo {
        width: 56px;
        height: 56px;
        margin-bottom: 1rem;
    }

    .login-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .login-subtitle {
        font-size: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .login-form .ant-form-item {
        margin-bottom: 1.25rem;
    }

    .form-input {
        padding: 1rem;
        font-size: 1rem;
        border-radius: 0.5rem;
    }

    .login-btn {
        padding: 1.125rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.5rem;
        width: 100%;
        min-height: 48px; /* Touch-friendly minimum */
        font-weight: 600;
    }

    .login-footer {
        padding-top: 1.25rem;
    }

    .login-footer p {
        font-size: 0.8rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .login-container {
        padding: 1rem;
    }

    .login-card {
        max-width: 420px;
        padding: 2rem;
    }

    .login-logo {
        width: 60px;
        height: 60px;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .form-input {
        padding: 0.9375rem 1rem;
        font-size: 0.9375rem;
    }

    .login-btn {
        padding: 1.0625rem 1.5rem;
        font-size: 0.9375rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .login-card {
        max-width: 440px;
        padding: 2.5rem;
    }

    .login-logo {
        width: 68px;
        height: 68px;
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .login-card {
        max-width: 460px;
        padding: 3rem;
    }

    .login-logo {
        width: 72px;
        height: 72px;
    }

    .login-title {
        font-size: 2rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .login-container {
        padding: 1rem;
        align-items: center;
    }

    .login-card {
        padding: 1.5rem;
    }

    .login-logo {
        width: 48px;
        height: 48px;
        margin-bottom: 0.75rem;
    }

    .login-title {
        font-size: 1.375rem;
        margin-bottom: 0.375rem;
    }

    .login-subtitle {
        margin-bottom: 1.25rem;
    }

    .login-form .ant-form-item {
        margin-bottom: 1rem;
    }
}

.login-form .form-group {
    margin-bottom: var(--space-5, 1.25rem);
}

.login-form .form-control {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid var(--gray-200, #e5e7eb);
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.login-form .form-control:focus {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary, #007BFF);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.login-form .btn {
    width: 100%;
    margin-top: var(--space-4, 1rem);
    padding: var(--space-4, 1rem) var(--space-6, 1.5rem);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.login-links {
    text-align: center;
    margin-top: var(--space-6, 1.5rem);
    padding-top: var(--space-6, 1.5rem);
    border-top: 1px solid var(--gray-200, #e5e7eb);
}

.login-links a {
    color: var(--primary, #007BFF);
    font-weight: 500;
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.login-links a:hover {
    color: var(--primary-dark, #0056D2);
    text-decoration: underline;
}

/* Responsive Design */
@media only screen and (max-width: 640px) {
    .login-container {
        padding: var(--space-3, 0.75rem);
    }

    .card {
        padding: var(--space-6, 1.5rem);
        margin: var(--space-3, 0.75rem);
    }

    .login-logo {
        width: 60px;
        height: 60px;
    }

    .login-title {
        font-size: var(--font-size-xl, 1.25rem);
    }
}

@media only screen and (max-width: 480px) {
    .card {
        padding: var(--space-4, 1rem);
    }

    .login-form .form-control {
        padding: var(--space-3, 0.75rem);
    }

    .login-form .btn {
        padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
    }
}