import React, { useState, useEffect, useCallback, startTransition } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import {
  Tb<PERSON>earch,
  Tb<PERSON><PERSON><PERSON>,
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain,
  TbTarget,
  TbCheck,
  TbX,
  TbStar,
  TbHome,
  TbBolt,
  TbRefresh
} from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReportsByUser } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import './animations.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getUserResults = useCallback(async () => {
    try {
      if (!user?._id) return;

      const response = await getAllReportsByUser({ userId: user._id });

      if (response.success) {
        const resultsMap = {};
        response.data.forEach(report => {
          const examId = report.exam?._id;
          if (!examId || !report.result) return;

          // Extract data from the result object
          const result = report.result;

          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {
            resultsMap[examId] = {
              verdict: result.verdict,
              percentage: result.percentage,
              correctAnswers: result.correctAnswers,
              wrongAnswers: result.wrongAnswers,
              totalQuestions: result.totalQuestions,
              obtainedMarks: result.obtainedMarks,
              totalMarks: result.totalMarks,
              score: result.score,
              points: result.points,
              xpEarned: result.xpEarned || result.points || result.xpGained || 0,
              timeTaken: report.timeTaken,
              completedAt: report.createdAt,
            };
          }
        });
        setUserResults(resultsMap);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    }
  }, [user?._id]);

  // Define getExams function outside useEffect so it can be called from other functions
  const getExams = useCallback(async (isRefresh = false) => {
      try {
        // Safety check: ensure user exists before proceeding
        if (!user) {
          console.log("User not loaded yet, skipping exam fetch");
          return;
        }

        // Check cache first (unless refreshing)
        if (!isRefresh) {
          const cachedExams = localStorage.getItem('user_exams_cache');
          const cacheTime = localStorage.getItem('user_exams_cache_time');
          const now = Date.now();

          // Use cache if less than 3 minutes old
          if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 180000) {
            const cached = JSON.parse(cachedExams);
            setExams(cached);
            setLastRefresh(new Date(parseInt(cacheTime)));
            if (user?.class) {
              setSelectedClass(String(user.class));
            }
            setLoading(false);
            return;
          }
        }

        if (isRefresh) {
          setRefreshing(true);
        } else {
          dispatch(ShowLoading());
        }

        const response = await getAllExams();

        if (isRefresh) {
          setRefreshing(false);
        } else {
          dispatch(HideLoading());
        }

        if (response.success) {
          console.log('Raw exams from API:', response.data.length);
          console.log('User level:', user?.level);

          // Filter exams by user's level with proper null checks
          const userLevelExams = response.data.filter(exam => {
            if (!exam.level || !user || !user.level) return false;
            return exam.level.toLowerCase() === user.level.toLowerCase();
          });

          console.log('User level exams after filtering:', userLevelExams.length);
          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          setExams(sortedExams);
          setLastRefresh(new Date());

          // Cache the exams data
          localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));
          localStorage.setItem('user_exams_cache_time', Date.now().toString());

          // Set default class filter to user's class
          if (user?.class) {
            setSelectedClass(String(user.class));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        if (isRefresh) {
          setRefreshing(false);
        } else {
          dispatch(HideLoading());
        }
        message.error(error.message);
      } finally {
        setLoading(false);
      }
  }, [dispatch, user]);

  useEffect(() => {
    getExams(false); // Initial load
    getUserResults();
  }, [getExams, getUserResults]);

  // Real-time updates for quiz completion and new exams
  useEffect(() => {
    // Listen for real-time updates from quiz completion
    const handleRankingUpdate = () => {
      console.log('🔄 Quiz listing - refreshing data after quiz completion...');
      getUserResults(); // Refresh user results to show updated XP
    };

    // Listen for new exam creation events
    const handleNewExam = () => {
      console.log('🆕 New exam created - refreshing exam list...');
      if (user) {
        getExams(true); // Use refresh mode
        getUserResults();
      }
    };

    // Listen for window focus to refresh data when returning from quiz
    const handleWindowFocus = () => {
      console.log('🎯 Quiz listing - window focused, refreshing data...');
      getUserResults();
      // Also refresh exams list to show newly generated exams
      if (user) {
        console.log('🔄 Refreshing exams list for new exams...');
        getExams(true); // Use refresh mode
      }
    };

    window.addEventListener('rankingUpdate', handleRankingUpdate);
    window.addEventListener('focus', handleWindowFocus);
    window.addEventListener('newExamCreated', handleNewExam);

    return () => {
      window.removeEventListener('rankingUpdate', handleRankingUpdate);
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('newExamCreated', handleNewExam);
    };
  }, []);

  // Periodic refresh to ensure quiz list stays up to date
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      if (user && !loading && !refreshing) {
        console.log('🔄 Periodic refresh of quiz list...');
        getExams(true); // Use refresh mode
      }
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => clearInterval(refreshInterval);
  }, [user, loading, refreshing]);

  useEffect(() => {
    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });
    let filtered = exams;
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedClass) {
      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));
    }
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    console.log('Filtered exams result:', filtered.length);
    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }

    // Validate MongoDB ObjectId format (24 character hex string)
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    if (!objectIdRegex.test(quiz._id)) {
      message.error('Invalid quiz ID format. Please try again.');
      return;
    }

    startTransition(() => {
      navigate(`/quiz/${quiz._id}/play`);
    });
  };

  // Manual refresh function
  const handleRefresh = async () => {
    console.log('🔄 Manual refresh triggered...');
    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes

    try {
      if (user) {
        await getExams(true); // Use refresh mode
        await getUserResults();
        message.success('Quiz list refreshed successfully!');
      }
    } catch (error) {
      message.error('Failed to refresh quiz list');
    }
  };

  const handleQuizView = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }
    // Check if user has attempted this quiz
    const userResult = userResults[quiz._id];
    if (!userResult) {
      message.info('You need to attempt this quiz first to view results.');
      return;
    }
    startTransition(() => {
      navigate(`/quiz/${quiz._id}/result`);
    });
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading quizzes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Hero Section */}
        <div className="text-center mb-8 sm:mb-12 opacity-100">
          <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg">
            <TbBrain className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Challenge Your Brain, Beat the Rest
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4">
            Test your knowledge with our comprehensive quizzes designed for You!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{filteredExams.length} Available Quizzes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Level: {user?.level || 'All Levels'}</span>
            </div>
            {lastRefresh && (
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100">
          <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                  <TbSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                />
              </div>
              <div className="sm:w-48 md:w-64">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                    <TbFilter className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>Class {className}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={loading || refreshing}
                className="flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
                title="Refresh quiz list"
              >
                <TbRefresh className={`h-4 w-4 sm:h-5 sm:w-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
                <span className="ml-2 hidden sm:inline text-sm sm:text-base">
                  {refreshing ? 'Refreshing...' : 'Refresh'}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Quiz Grid */}
        <div className="opacity-100">


          {filteredExams.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto">
                <TbTarget className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {searchTerm || selectedClass
                    ? "Try adjusting your search or filter criteria."
                    : "No quizzes are available for your level at the moment."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="quiz-grid">
              {filteredExams.map((quiz, index) => (
                <QuizCard
                  key={quiz._id}
                  quiz={quiz}
                  userResult={userResults[quiz._id]}
                  showResults={true}
                  onStart={handleQuizStart}
                  onView={() => handleQuizView(quiz)}
                  index={index}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Simple QuizCard component without Framer Motion
const QuizCard = ({ quiz, userResult, onStart, onView, index }) => {
  const formatTime = (seconds) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatCompletionTime = (timeInSeconds) => {
    // Handle different possible time formats
    if (!timeInSeconds && timeInSeconds !== 0) return '0s';

    let totalSeconds = timeInSeconds;

    // If it's a string, try to parse it
    if (typeof timeInSeconds === 'string') {
      totalSeconds = parseInt(timeInSeconds, 10);
    }

    // If it's still not a valid number, return 0s
    if (isNaN(totalSeconds) || totalSeconds < 0) return '0s';

    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${timeInSeconds}s`;
  };

  // Safety checks for quiz object
  if (!quiz || typeof quiz !== 'object') {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <p className="text-gray-500">Invalid quiz data</p>
      </div>
    );
  }

  return (
    <div
      className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col"
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',
        border: userResult
          ? (userResult.verdict === 'Pass' ? '2px solid #10b981' : '2px solid #ef4444')
          : '2px solid #3b82f6',
        boxShadow: userResult
          ? (userResult.verdict === 'Pass'
              ? '0 8px 20px rgba(16, 185, 129, 0.3)'
              : '0 8px 20px rgba(239, 68, 68, 0.3)')
          : '0 8px 20px rgba(59, 130, 246, 0.3)',
        minHeight: window.innerWidth <= 768 ? '240px' : '320px',
        height: 'auto'
      }}
    >
      {/* Quiz Title - At Top */}
      <div className="mb-2 text-center">
        <h3
          className="font-bold mb-1 line-clamp-2"
          style={{
            color: '#1f2937',
            textShadow: '0 1px 2px rgba(0,0,0,0.1)',
            lineHeight: '1.1',
            fontSize: window.innerWidth <= 768 ? '14px' : '16px'
          }}
        >
          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}
        </h3>
      </div>

      {/* Status Tags - Centered */}
      <div className="mb-2 text-center">
        {userResult ? (
          <div className="flex items-center justify-center gap-1">
            <div
              className="px-2 py-1 rounded-full text-xs font-bold text-white shadow-md"
              style={{
                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',
                fontSize: window.innerWidth <= 768 ? '9px' : '10px'
              }}
            >
              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
            </div>
            <div
              className="px-2 py-1 rounded-full text-xs font-bold text-center shadow-md"
              style={{
                backgroundColor: '#ffffff',
                color: '#1f2937',
                fontSize: window.innerWidth <= 768 ? '9px' : '10px'
              }}
            >
              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%
            </div>
          </div>
        ) : (
          <div
            className="px-2 py-1 rounded-full text-xs font-bold text-white shadow-md"
            style={{
              backgroundColor: '#3b82f6',
              fontSize: window.innerWidth <= 768 ? '9px' : '10px'
            }}
          >
            🆕 NOT ATTEMPTED
          </div>
        )}
      </div>

      <div className="text-center mb-6">
        <div className="flex-1">

          {/* Questions and Duration - Horizontal */}
          <div className="flex gap-1 mb-2 justify-center">
            <div
              className="flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm"
              style={{
                background: 'linear-gradient(to right, #eff6ff, #e0e7ff)',
                borderColor: '#bfdbfe'
              }}
            >
              <TbQuestionMark className="w-3 h-3" style={{ color: '#2563eb' }} />
              <span
                className="font-bold"
                style={{
                  color: '#1e40af',
                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'
                }}
              >
                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}
              </span>
            </div>
            <div
              className="flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm"
              style={{
                background: 'linear-gradient(to right, #fdf4ff, #fce7f3)',
                borderColor: '#e9d5ff'
              }}
            >
              <TbClock className="w-3 h-3" style={{ color: '#9333ea' }} />
              <span
                className="font-bold"
                style={{
                  color: '#7c3aed',
                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'
                }}
              >
                3m
              </span>
            </div>
          </div>



          <div className="flex items-center justify-center gap-1 flex-wrap mb-2">
            <span
              className="inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm"
              style={{
                background: 'linear-gradient(to right, #4ade80, #3b82f6)',
                fontSize: window.innerWidth <= 768 ? '8px' : '10px'
              }}
            >
              📖{typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A'}
            </span>
            <span
              className="inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm"
              style={{
                background: 'linear-gradient(to right, #667eea, #764ba2)',
                fontSize: window.innerWidth <= 768 ? '8px' : '10px'
              }}
            >
              📚{quiz.subject}
            </span>
            {/* Category Tag - Only show if not General */}
            {quiz.category && quiz.category !== 'General' && (
              <span
                className="inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm"
                style={{
                  background: 'linear-gradient(to right, #f97316, #ea580c)',
                  fontSize: window.innerWidth <= 768 ? '8px' : '10px'
                }}
              >
                📂{quiz.category}
              </span>
            )}
            {/* Topic Tag - Only show if not General */}
            {quiz.topic && quiz.topic !== 'General' && (
              <span
                className="inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm"
                style={{
                  background: 'linear-gradient(to right, #10b981, #059669)',
                  fontSize: window.innerWidth <= 768 ? '8px' : '10px'
                }}
              >
                📖{quiz.topic}
              </span>
            )}
          </div>
        </div>
      </div>



      {userResult && typeof userResult === 'object' && (
        <div
          className="mb-2 p-2 rounded-lg border shadow-md"
          style={{
            background: userResult.verdict === 'Pass'
              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)'
              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',
            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'
          }}
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              {userResult.verdict === 'Pass' ? (
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse"
                  style={{
                    background: 'linear-gradient(to right, #10b981, #059669)',
                    borderColor: '#86efac'
                  }}
                >
                  <TbCheck className="w-6 h-6 font-bold" style={{ color: '#ffffff' }} />
                </div>
              ) : (
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse"
                  style={{
                    background: 'linear-gradient(to right, #ef4444, #dc2626)',
                    borderColor: '#fca5a5'
                  }}
                >
                  <TbX className="w-6 h-6 font-bold" style={{ color: '#ffffff' }} />
                </div>
              )}
              <div>
                <span className="text-lg font-bold" style={{ color: '#1f2937' }}>🏆 Last Result</span>
                <div className="text-sm" style={{ color: '#6b7280' }}>
                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}
                </div>
              </div>
            </div>
            <span
              className="text-3xl font-bold shadow-lg"
              style={{
                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'
              }}
            >
              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%
            </span>
          </div>

          {/* Horizontal Layout for Results */}
          <div className="flex gap-1 justify-center flex-wrap">
            {/* Correct/Wrong - Horizontal */}
            <div
              className="flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md"
              style={{
                background: 'linear-gradient(to right, #dcfce7, #fecaca)',
                borderColor: '#86efac'
              }}
            >
              <div className="flex items-center gap-1">
                <TbCheck className="w-3 h-3" style={{ color: '#16a34a' }} />
                <span className="text-sm font-bold" style={{ color: '#15803d' }}>
                  {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <TbX className="w-3 h-3" style={{ color: '#dc2626' }} />
                <span className="text-sm font-bold" style={{ color: '#b91c1c' }}>
                  {(quiz.questions?.length || 0) - (typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0)}
                </span>
              </div>
            </div>

            {/* XP */}
            <div
              className="flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md"
              style={{
                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',
                borderColor: '#fde047'
              }}
            >
              <span className="text-sm">⭐</span>
              <span className="text-sm font-bold" style={{ color: '#92400e' }}>
                {userResult.xpEarned || userResult.points || 0}
              </span>
            </div>

            {/* Time - Horizontal if available */}
            {userResult.timeTaken && userResult.timeTaken > 0 && (
              <div
                className="flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md"
                style={{
                  background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',
                  borderColor: '#c4b5fd'
                }}
              >
                <TbClock className="w-3 h-3" style={{ color: '#9333ea' }} />
                <span className="text-sm font-bold" style={{ color: '#7c3aed' }}>
                  {formatCompletionTime(userResult.timeTaken)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Spacer to push buttons to bottom */}
      <div className="flex-1"></div>

      <div className="flex gap-2 mt-3">
        {/* Main Action Button - Bigger for retake */}
        <button
          onClick={() => onStart(quiz)}
          className="flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white"
          style={{
            background: userResult
              ? 'linear-gradient(to right, #f97316, #ef4444)'
              : 'linear-gradient(to right, #3b82f6, #8b5cf6)',
            fontSize: '13px',
            minHeight: '36px'
          }}
        >
          <TbPlayerPlay className="w-3 h-3" />
          {userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz'}
        </button>

        {/* Small Trophy Button - Only show when there are results */}
        {userResult && (
          <button
            onClick={() => onView(quiz)}
            className="px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white"
            style={{
              background: 'linear-gradient(to right, #fbbf24, #f97316)',
              fontSize: '13px',
              minHeight: '36px'
            }}
            title="View Results"
          >
            <TbTrophy className="w-3 h-3" />
          </button>
        )}
      </div>
    </div>
  );
};

export default Quiz;
