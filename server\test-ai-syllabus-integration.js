const mongoose = require('mongoose');
require('dotenv').config();
const Syllabus = require('./models/syllabusModel');
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');

async function testAISyllabusIntegration() {
  try {
    console.log('🧪 Testing AI Question Generation with PDF Syllabus Integration...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // 1. Check available syllabuses
    console.log('📚 Checking available syllabuses...');
    const syllabuses = await Syllabus.find({ 
      processingStatus: 'completed',
      isActive: true 
    }).select('title level subject classes processingStatus qualityScore extractedText');
    
    console.log(`Found ${syllabuses.length} processed syllabuses:`);
    syllabuses.forEach((syllabus, index) => {
      console.log(`${index + 1}. ${syllabus.title}`);
      console.log(`   Level: ${syllabus.level}, Subject: ${syllabus.subject}`);
      console.log(`   Classes: ${syllabus.classes.join(', ')}`);
      console.log(`   Quality Score: ${syllabus.qualityScore || 'N/A'}%`);
      console.log(`   Has extracted text: ${syllabus.extractedText ? 'Yes' : 'No'}`);
      console.log('');
    });
    
    if (syllabuses.length === 0) {
      console.log('⚠️ No processed syllabuses found. Please upload and process a PDF syllabus first.');
      console.log('💡 You can do this through the admin panel at /admin/syllabus');
      return;
    }
    
    // 2. Test AI service syllabus data retrieval
    console.log('🔍 Testing AI service syllabus data retrieval...');
    const testSyllabus = syllabuses[0];
    
    const aiService = new AIQuestionGenerationService();
    const syllabusData = await aiService.getSyllabusData(
      testSyllabus.level,
      testSyllabus.classes[0],
      testSyllabus.subject,
      testSyllabus._id
    );
    
    console.log('Syllabus data retrieved by AI service:');
    console.log(`- Source: ${syllabusData.source}`);
    console.log(`- Has topics: ${syllabusData.topics ? 'Yes' : 'No'}`);
    console.log(`- Has extracted text: ${syllabusData.extractedText ? 'Yes' : 'No'}`);
    console.log(`- Text length: ${syllabusData.extractedText ? syllabusData.extractedText.length : 0} characters`);
    console.log(`- Syllabus ID: ${syllabusData.syllabusId || 'N/A'}`);
    console.log('');
    
    // 3. Test prompt building with PDF content
    console.log('📝 Testing prompt building with PDF content...');
    const prompt = await aiService.buildMultipleChoicePrompt(
      testSyllabus.level,
      testSyllabus.classes[0],
      testSyllabus.subject,
      'medium',
      [],
      testSyllabus._id
    );
    
    console.log('Generated prompt preview (first 500 characters):');
    console.log(prompt.substring(0, 500) + '...');
    console.log('');
    
    // Check if PDF content is included in prompt
    if (prompt.includes('SYLLABUS CONTENT FROM PDF:')) {
      console.log('✅ PDF content is properly included in the prompt');
    } else {
      console.log('⚠️ PDF content may not be included in the prompt');
    }
    
    console.log('\n🎉 AI Question Generation with PDF Syllabus Integration Test Complete!');
    console.log('\n📋 Summary:');
    console.log(`- Available syllabuses: ${syllabuses.length}`);
    console.log(`- AI service can retrieve syllabus data: ${syllabusData.source ? 'Yes' : 'No'}`);
    console.log(`- PDF content included in prompts: ${prompt.includes('SYLLABUS CONTENT FROM PDF:') ? 'Yes' : 'No'}`);
    
    if (syllabuses.length > 0 && syllabusData.source && prompt.includes('SYLLABUS CONTENT FROM PDF:')) {
      console.log('\n✅ All systems working correctly! AI can generate questions using uploaded PDF syllabuses.');
    } else {
      console.log('\n⚠️ Some issues detected. Please check the syllabus upload and processing system.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the test
testAISyllabusIntegration();
