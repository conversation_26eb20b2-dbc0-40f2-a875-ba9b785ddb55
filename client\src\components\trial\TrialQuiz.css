/* Trial Quiz Enhanced Styling - Premium Experience */

/* Premium Animations */
@keyframes flash {
  0% { opacity: 0; }
  50% { opacity: 0.4; }
  100% { opacity: 0; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
  50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.6); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Flash Animation Classes */
.flash-animation {
  animation: flash 0.5s ease-in-out;
}

/* Premium Background Effects */
.trial-background {
  position: relative;
  overflow: hidden;
}

.trial-background::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

.flash-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, rgba(34, 197, 94, 0.1) 100%);
  z-index: 9999;
  pointer-events: none;
}

.flash-failure {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, rgba(239, 68, 68, 0.1) 100%);
  z-index: 9999;
  pointer-events: none;
}

/* Enhanced Timer Styling */
.trial-timer {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 3px solid;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 800;
  font-size: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  backdrop-filter: blur(10px);
  transform: translateY(0);
}

.trial-timer.normal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: rgba(102, 126, 234, 0.6);
  color: white;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trial-timer.normal:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5), 0 8px 20px rgba(0, 0, 0, 0.15);
}

.trial-timer.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-color: rgba(245, 87, 108, 0.6);
  color: white;
  box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trial-timer.warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(245, 87, 108, 0.5), 0 8px 20px rgba(0, 0, 0, 0.15);
}

.trial-timer.critical {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-color: rgba(255, 107, 107, 0.8);
  color: white;
  animation: pulse 1.5s infinite, shake 0.5s infinite;
  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.6), 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes shake {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-2px) translateY(-1px); }
  75% { transform: translateX(2px) translateY(1px); }
}

.trial-timer-icon {
  width: 24px;
  height: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Warning Ring Animation */
.timer-warning-ring {
  position: absolute;
  inset: 0;
  border: 2px solid rgba(253, 224, 71, 0.5);
  border-radius: 12px;
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Progress Bar Styling */
.trial-progress-container {
  margin-top: 16px;
}

.trial-progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.trial-progress-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.trial-progress-percentage {
  font-size: 14px;
  font-weight: 500;
  color: #2563eb;
}

.trial-progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e5e7eb;
  border-radius: 9999px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.trial-progress-fill {
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #4f46e5);
  border-radius: 9999px;
  transition: width 0.5s ease-out;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Question Card Styling */
.trial-question-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(20px);
}

.trial-question-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.trial-question-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 48px;
  color: white;
  position: relative;
  overflow: hidden;
}

.trial-question-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.trial-question-number {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.trial-question-number-badge {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.trial-question-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 24px;
}

/* Option Styling */
.trial-option {
  width: 100%;
  padding: 24px 28px;
  text-align: left;
  border-radius: 20px;
  border: 3px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 16px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.trial-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(102, 126, 234, 0.2),
    rgba(255, 255, 255, 0.4),
    rgba(102, 126, 234, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.trial-option:hover::before {
  left: 100%;
}

.trial-option.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%);
  animation: shimmer 2s ease-in-out infinite;
  pointer-events: none;
}

.trial-option:hover {
  border-color: #667eea;
  background: linear-gradient(145deg, #f0f4ff 0%, #e0e7ff 100%);
  box-shadow:
    0 12px 24px rgba(102, 126, 234, 0.15),
    0 4px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-3px) scale(1.02);
}

.trial-option.selected {
  border-color: #667eea;
  background: linear-gradient(145deg, #e0e7ff 0%, #c7d2fe 100%);
  color: #3730a3;
  box-shadow:
    0 16px 32px rgba(102, 126, 234, 0.25),
    0 8px 16px rgba(102, 126, 234, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transform: translateY(-2px) scale(1.03);
}

.trial-option-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trial-option-letter {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.trial-option.selected .trial-option-letter {
  border-color: #2563eb;
  background: #2563eb;
  color: white;
}

.trial-option-text {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
}

/* Navigation Buttons */
.trial-nav-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 32px;
  border-top: 1px solid #e5e7eb;
}

.trial-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.trial-btn:hover {
  transform: scale(1.02);
}

.trial-btn:active {
  transform: scale(0.98);
}

.trial-btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.trial-btn-secondary:hover {
  background: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.trial-btn-secondary:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.trial-btn-primary {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: white;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.trial-btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #1e40af);
  box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.trial-btn-success {
  background: linear-gradient(to right, #059669, #047857);
  color: white;
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.trial-btn-success:hover {
  background: linear-gradient(to right, #047857, #065f46);
  box-shadow: 0 12px 35px rgba(5, 150, 105, 0.4);
}

/* Loading Spinner */
.trial-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Input Styling */
.trial-input {
  width: 100%;
  padding: 20px 24px;
  border: 2px solid #d1d5db;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 500;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.trial-input:focus {
  outline: none;
  border-color: #2563eb;
  background: white;
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

/* Result Page Styling */
.trial-result-card {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  margin-bottom: 32px;
}

.trial-result-header {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  padding: 32px 40px;
  text-align: center;
  color: white;
}

.trial-result-score {
  font-size: 72px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.trial-result-label {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.trial-result-content {
  padding: 32px 40px;
}

.trial-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.trial-stat-card {
  text-align: center;
  padding: 24px;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.trial-stat-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  padding: 12px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trial-stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 4px;
}

.trial-stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Color variants for stat cards */
.trial-stat-blue .trial-stat-icon {
  background: #dbeafe;
  color: #2563eb;
}

.trial-stat-green .trial-stat-icon {
  background: #dcfce7;
  color: #16a34a;
}

.trial-stat-red .trial-stat-icon {
  background: #fee2e2;
  color: #dc2626;
}

.trial-stat-purple .trial-stat-icon {
  background: #f3e8ff;
  color: #9333ea;
}

/* Enhanced Responsive Design */

/* Mobile First - Base styles for mobile */
.trial-container {
  padding: 16px;
  max-width: 100%;
}

.trial-header {
  padding: 16px 20px;
  margin-bottom: 16px;
}

.trial-content {
  padding: 20px 16px;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .trial-container {
    padding: 24px;
    max-width: 768px;
    margin: 0 auto;
  }

  .trial-header {
    padding: 24px 32px;
    margin-bottom: 24px;
  }

  .trial-content {
    padding: 32px 24px;
  }

  .trial-timer {
    padding: 12px 24px;
    font-size: 18px;
  }

  .trial-question-header {
    padding: 32px 36px;
  }

  .trial-option {
    padding: 20px 24px;
  }

  .trial-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* Laptop Styles */
@media (min-width: 1024px) {
  .trial-container {
    max-width: 1024px;
    padding: 32px;
  }

  .trial-header {
    padding: 32px 48px;
    margin-bottom: 32px;
  }

  .trial-content {
    padding: 48px 40px;
  }

  .trial-timer {
    padding: 16px 32px;
    font-size: 20px;
  }

  .trial-question-header {
    padding: 40px 48px;
  }

  .trial-option {
    padding: 24px 28px;
  }

  .trial-stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

/* Desktop Styles */
@media (min-width: 1280px) {
  .trial-container {
    max-width: 1280px;
    padding: 40px;
  }

  .trial-header {
    padding: 40px 56px;
    margin-bottom: 40px;
  }

  .trial-content {
    padding: 56px 48px;
  }
}

/* Mobile Specific Adjustments */
@media (max-width: 767px) {
  .trial-timer {
    padding: 10px 16px;
    font-size: 16px;
    gap: 8px;
  }

  .trial-timer-icon {
    width: 18px;
    height: 18px;
  }

  .trial-question-header {
    padding: 20px 16px;
  }

  .trial-question-number-badge {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .trial-question-title {
    font-size: 18px;
    line-height: 1.4;
  }

  .trial-option {
    padding: 16px 18px;
    margin-bottom: 12px;
  }

  .trial-option-letter {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .trial-option-text {
    font-size: 14px;
    line-height: 1.4;
  }

  .trial-btn {
    padding: 12px 16px;
    font-size: 14px;
    gap: 6px;
  }

  .trial-nav-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .trial-nav-buttons .trial-btn {
    width: 100%;
    justify-content: center;
  }

  .trial-progress-container {
    margin-top: 12px;
  }

  .trial-progress-label,
  .trial-progress-percentage {
    font-size: 12px;
  }

  .trial-progress-bar {
    height: 8px;
  }

  .trial-result-score {
    font-size: 48px;
  }

  .trial-result-content {
    padding: 20px 16px;
  }

  .trial-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .trial-stat-card {
    padding: 16px;
  }

  .trial-stat-value {
    font-size: 18px;
  }

  .trial-stat-label {
    font-size: 12px;
  }

  .trial-input {
    padding: 16px 20px;
    font-size: 16px;
  }
}

/* Touch-friendly adjustments for mobile */
@media (max-width: 767px) and (pointer: coarse) {
  .trial-option {
    min-height: 60px;
    padding: 18px 20px;
  }

  .trial-btn {
    min-height: 48px;
    padding: 14px 20px;
  }

  .trial-input {
    min-height: 52px;
  }
}
