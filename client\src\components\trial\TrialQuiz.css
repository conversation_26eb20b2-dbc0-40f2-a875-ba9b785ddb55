/* Trial Quiz Enhanced Styling */

/* Flash Animation */
@keyframes flash {
  0% { opacity: 0; }
  50% { opacity: 0.4; }
  100% { opacity: 0; }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Flash Animation Classes */
.flash-animation {
  animation: flash 0.5s ease-in-out;
}

.flash-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(34, 197, 94, 0.3);
  z-index: 9999;
  pointer-events: none;
}

.flash-failure {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(239, 68, 68, 0.3);
  z-index: 9999;
  pointer-events: none;
}

/* Enhanced Timer Styling */
.trial-timer {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid;
  transition: all 0.3s ease;
  font-weight: bold;
  font-size: 18px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  position: relative;
}

.trial-timer.normal {
  background: linear-gradient(to right, #2563eb, #4f46e5);
  border-color: rgba(147, 197, 253, 0.5);
  color: white;
}

.trial-timer.warning {
  background: linear-gradient(to right, #eab308, #f97316);
  border-color: rgba(253, 224, 71, 0.5);
  color: white;
}

.trial-timer.critical {
  background: linear-gradient(to right, #dc2626, #b91c1c);
  border-color: rgba(252, 165, 165, 0.5);
  color: white;
  animation: pulse 1s infinite;
}

.trial-timer-icon {
  width: 24px;
  height: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Warning Ring Animation */
.timer-warning-ring {
  position: absolute;
  inset: 0;
  border: 2px solid rgba(253, 224, 71, 0.5);
  border-radius: 12px;
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Progress Bar Styling */
.trial-progress-container {
  margin-top: 16px;
}

.trial-progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.trial-progress-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.trial-progress-percentage {
  font-size: 14px;
  font-weight: 500;
  color: #2563eb;
}

.trial-progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e5e7eb;
  border-radius: 9999px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.trial-progress-fill {
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #4f46e5);
  border-radius: 9999px;
  transition: width 0.5s ease-out;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Question Card Styling */
.trial-question-card {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  overflow: hidden;
}

.trial-question-header {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  padding: 32px 40px;
  color: white;
}

.trial-question-number {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.trial-question-number-badge {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.trial-question-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 24px;
}

/* Option Styling */
.trial-option {
  width: 100%;
  padding: 20px 24px;
  text-align: left;
  border-radius: 16px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  margin-bottom: 12px;
  background: white;
  cursor: pointer;
}

.trial-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.trial-option.selected {
  border-color: #2563eb;
  background: #eff6ff;
  color: #1e40af;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
  transform: scale(1.01);
}

.trial-option-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trial-option-letter {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.trial-option.selected .trial-option-letter {
  border-color: #2563eb;
  background: #2563eb;
  color: white;
}

.trial-option-text {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
}

/* Navigation Buttons */
.trial-nav-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 32px;
  border-top: 1px solid #e5e7eb;
}

.trial-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.trial-btn:hover {
  transform: scale(1.02);
}

.trial-btn:active {
  transform: scale(0.98);
}

.trial-btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.trial-btn-secondary:hover {
  background: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.trial-btn-secondary:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.trial-btn-primary {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: white;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.trial-btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #1e40af);
  box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.trial-btn-success {
  background: linear-gradient(to right, #059669, #047857);
  color: white;
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.trial-btn-success:hover {
  background: linear-gradient(to right, #047857, #065f46);
  box-shadow: 0 12px 35px rgba(5, 150, 105, 0.4);
}

/* Loading Spinner */
.trial-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Input Styling */
.trial-input {
  width: 100%;
  padding: 20px 24px;
  border: 2px solid #d1d5db;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 500;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.trial-input:focus {
  outline: none;
  border-color: #2563eb;
  background: white;
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

/* Result Page Styling */
.trial-result-card {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  margin-bottom: 32px;
}

.trial-result-header {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  padding: 32px 40px;
  text-align: center;
  color: white;
}

.trial-result-score {
  font-size: 72px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.trial-result-label {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.trial-result-content {
  padding: 32px 40px;
}

.trial-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.trial-stat-card {
  text-align: center;
  padding: 24px;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.trial-stat-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  padding: 12px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trial-stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 4px;
}

.trial-stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Color variants for stat cards */
.trial-stat-blue .trial-stat-icon {
  background: #dbeafe;
  color: #2563eb;
}

.trial-stat-green .trial-stat-icon {
  background: #dcfce7;
  color: #16a34a;
}

.trial-stat-red .trial-stat-icon {
  background: #fee2e2;
  color: #dc2626;
}

.trial-stat-purple .trial-stat-icon {
  background: #f3e8ff;
  color: #9333ea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .trial-timer {
    padding: 8px 16px;
    font-size: 16px;
  }

  .trial-timer-icon {
    width: 20px;
    height: 20px;
  }

  .trial-question-header {
    padding: 24px 20px;
  }

  .trial-question-number-badge {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .trial-question-title {
    font-size: 20px;
  }

  .trial-option {
    padding: 16px 20px;
  }

  .trial-option-letter {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .trial-option-text {
    font-size: 15px;
  }

  .trial-btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .trial-result-score {
    font-size: 56px;
  }

  .trial-result-content {
    padding: 24px 20px;
  }

  .trial-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .trial-stat-card {
    padding: 16px;
  }

  .trial-stat-value {
    font-size: 20px;
  }
}
