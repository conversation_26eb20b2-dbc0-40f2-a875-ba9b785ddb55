{"version": 3, "file": "test.browser.js", "sourceRoot": "", "sources": ["../../../browser-test/test.browser.ts"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AAEjC,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAC,QAAQ,EAAE,EAAE,EAAC,MAAM,OAAO,CAAC;AACnC,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,kDAAkD;AAErE,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,EAAC,GAAG,EAAE,oBAAoB,IAAI,OAAO,EAAC,CAAC,CAAC;QACrE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACvC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;QAC/D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;YAC3B,GAAG,EAAE,oBAAoB,IAAI,cAAc;YAC3C,MAAM,EAAE,EAAC,KAAK,EAAE,OAAO,EAAC;SACzB,CAAC,CAAC;QACH,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACvC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;QAC7D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;YAC3B,GAAG,EAAE,oBAAoB,IAAI,OAAO;YACpC,mBAAmB,EAAE,MAAM,CAAC,KAAK;SAClC,CAAC,CAAC;QACH,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC9D,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,MAAM,SAAS,GAAG;YAChB;gBACE,cAAc,EAAE,kBAAkB;gBAClC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC;aACvC;YACD;gBACE,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE,cAAc;aACrB;SACF,CAAC;QACF,MAAM,QAAQ,GACZ,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QAC3E,MAAM,MAAM,GAAG,KAAK,QAAQ,IAAI,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,+BAA+B,QAAQ,EAAE,CAAC,CAAC;QAEvE,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,KAAK,QAAQ,qBAAqB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAClF,0BAA0B;YAC1B,OAAO,IAAI,QAAQ,CAAC;YACpB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAClC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;gBACrB,OAAO,IAAI,MAAM,CAAC;YACpB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,MAAM,CAAC;QAClB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;YAC3B,OAAO;YACP,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,oBAAoB,IAAI,OAAO;SACrC,CAAC,CAAC;QACH,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAgB,CAAC;QACtC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;QAClD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}