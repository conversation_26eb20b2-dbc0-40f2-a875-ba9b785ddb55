const express = require('express');
const cors = require('cors');
const app = express();
const port = 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    status: 'OK', 
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Login route
app.post('/api/users/login', (req, res) => {
  console.log('Login attempt:', req.body);
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        email: req.body.email,
        name: 'Test User'
      },
      token: 'test-token-123'
    }
  });
});

app.listen(port, () => {
  console.log(`✅ Test server running on port ${port}`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
  console.log(`🔗 Login test: http://localhost:${port}/api/users/login`);
});
