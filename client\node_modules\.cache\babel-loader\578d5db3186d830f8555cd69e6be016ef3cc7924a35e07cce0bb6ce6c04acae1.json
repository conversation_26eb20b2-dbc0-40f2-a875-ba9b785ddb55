{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbTrophy, TbCheck, TbX, TbClock, TbBrain, TbArrowRight, TbStar, TbUsers, TbBook, TbMessageCircle, TbChartBar, TbSettings } from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrialQuizResult = ({\n  result,\n  onTryAnother,\n  onRegister\n}) => {\n  _s();\n  var _result$questionResul;\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: {\n            y: 0.6\n          }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    }\n  }, [result.percentage]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n  const getPerformanceMessage = percentage => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n  const premiumFeatures = [{\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Access comprehensive study materials, notes, and resources\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with other students and track your progress\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\"\n  }, {\n    icon: TbUsers,\n    title: \"Unlimited Quizzes\",\n    description: \"Take as many quizzes as you want across all subjects\"\n  }, {\n    icon: TbStar,\n    title: \"Progress Tracking\",\n    description: \"Detailed analytics and performance insights\"\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes flash {\n          0% { opacity: 0; }\n          50% { opacity: 0.4; }\n          100% { opacity: 0; }\n        }\n        .flash-animation {\n          animation: flash 0.5s ease-in-out;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4 relative\",\n      children: [showFlash && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fixed inset-0 z-50 pointer-events-none flash-animation ${result.percentage >= 60 ? 'bg-green-500' : 'bg-red-500'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            ease: \"easeOut\"\n          },\n          className: \"text-center mb-8 sm:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3,\n              type: \"spring\",\n              stiffness: 200\n            },\n            className: `inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`,\n            onAnimationComplete: () => setAnimationComplete(true),\n            children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.6\n            },\n            className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n            children: \"Quiz Complete! \\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.8\n            },\n            className: `inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`,\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xl sm:text-2xl font-bold ${performance.color}`,\n              children: performance.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 1.0\n          },\n          className: \"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.8,\n                  delay: 1.2,\n                  type: \"spring\"\n                },\n                className: \"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\",\n                children: [result.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/90 text-lg sm:text-xl\",\n                children: \"Your Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\",\n              children: [{\n                label: \"Total Questions\",\n                value: result.totalQuestions,\n                icon: TbBook,\n                color: \"blue\",\n                delay: 1.4\n              }, {\n                label: \"Correct Answers\",\n                value: result.correctAnswers,\n                icon: TbCheck,\n                color: \"green\",\n                delay: 1.6\n              }, {\n                label: \"Wrong Answers\",\n                value: result.wrongAnswers,\n                icon: TbX,\n                color: \"red\",\n                delay: 1.8\n              }, {\n                label: \"Time Taken\",\n                value: formatTime(result.timeSpent),\n                icon: TbClock,\n                color: \"purple\",\n                delay: 2.0\n              }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: stat.delay\n                },\n                className: `p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`,\n                  children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                    className: `w-6 h-6 text-${stat.color}-600`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`,\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 font-medium\",\n                  children: stat.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 2.2\n              },\n              className: \"text-center mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${isPassed ? 'bg-green-100 text-green-700 border-2 border-green-200' : 'bg-red-100 text-red-700 border-2 border-red-200'}`,\n                children: [isPassed ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 2.4\n              },\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowDetails(!showDetails),\n                className: \"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: showDetails ? 'Hide Question Summary' : 'View Question Summary'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 2.6\n          },\n          className: \"text-center mb-8\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            children: /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\",\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Register Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 2.8\n          },\n          className: \"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl sm:text-3xl font-bold mb-2\",\n              children: \"\\uD83D\\uDD13 Unlock These Amazing Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-lg\",\n              children: \"Join thousands of students already excelling with BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 sm:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n              children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 3.0 + 0.1 * index\n                },\n                className: \"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-gray-800\",\n                    children: feature.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 leading-relaxed\",\n                  children: feature.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 flex items-center text-blue-600 font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Premium Feature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 3.4\n              },\n              className: \"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 mr-2 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 17\n                }, this), \"Advanced Quiz Features & Maximum Control\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    duration: 0.5,\n                    delay: 3.6\n                  },\n                  className: \"bg-white rounded-xl p-5 shadow-sm border border-purple-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(TbBook, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-bold text-gray-800\",\n                      children: \"Multiple Subject Selection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"text-sm text-gray-600 space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 414,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Choose from \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"15+ subjects\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 415,\n                          columnNumber: 41\n                        }, this), \" across all levels\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 418,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Mix and match subjects in custom quizzes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Subject-specific performance tracking\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Cross-subject comparison analytics\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    duration: 0.5,\n                    delay: 3.8\n                  },\n                  className: \"bg-white rounded-xl p-5 shadow-sm border border-blue-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(TbSettings, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-bold text-gray-800\",\n                      children: \"Maximum Quiz Control\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"text-sm text-gray-600 space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Set custom \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"time limits\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 448,\n                          columnNumber: 40\n                        }, this), \" (5-180 minutes)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Choose question count (5-100 questions)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 455,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Select difficulty levels (Easy, Medium, Hard)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 459,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Pause and resume quiz sessions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 4.0\n                },\n                className: \"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"font-bold text-gray-800 mb-4 text-center\",\n                  children: \"\\uD83D\\uDE80 Advanced Quiz Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                  children: [{\n                    icon: \"⏱️\",\n                    title: \"Smart Timer\",\n                    desc: \"Adaptive timing\"\n                  }, {\n                    icon: \"🎯\",\n                    title: \"Targeted Practice\",\n                    desc: \"Weak area focus\"\n                  }, {\n                    icon: \"📊\",\n                    title: \"Live Analytics\",\n                    desc: \"Real-time insights\"\n                  }, {\n                    icon: \"🏆\",\n                    title: \"Achievement System\",\n                    desc: \"Unlock rewards\"\n                  }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.9\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      duration: 0.3,\n                      delay: 4.2 + 0.1 * index\n                    },\n                    className: \"text-center p-3 bg-white rounded-lg shadow-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xl mb-1\",\n                      children: feature.icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-xs text-gray-800\",\n                      children: feature.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: feature.desc\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 3.6\n              },\n              className: \"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n                children: \"\\uD83C\\uDFAF Why Students Choose BrainWave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [{\n                  icon: \"🚀\",\n                  title: \"Instant Access\",\n                  desc: \"Start learning immediately\"\n                }, {\n                  icon: \"📱\",\n                  title: \"Mobile Friendly\",\n                  desc: \"Study anywhere, anytime\"\n                }, {\n                  icon: \"🎓\",\n                  title: \"Expert Content\",\n                  desc: \"Created by top educators\"\n                }, {\n                  icon: \"🏆\",\n                  title: \"Proven Results\",\n                  desc: \"98% success rate\"\n                }].map((benefit, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    duration: 0.4,\n                    delay: 3.8 + 0.1 * index\n                  },\n                  className: \"text-center p-4 bg-white rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl mb-2\",\n                    children: benefit.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-gray-800 mb-1\",\n                    children: benefit.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: benefit.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 4.2\n              },\n              className: \"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-xl font-bold mb-2\",\n                children: \"Ready to Excel? \\uD83C\\uDF1F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-100 mb-4\",\n                children: \"Join BrainWave today and unlock your full potential!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 justify-center items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    className: \"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.98\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Create Free Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-200 text-sm\",\n                  children: \"\\u2728 No credit card required \\u2022 Start immediately\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 9\n        }, this), showDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: \"auto\"\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl p-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-800 mb-4\",\n            children: \"Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: (_result$questionResul = result.questionResults) === null || _result$questionResul === void 0 ? void 0 : _result$questionResul.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg border-2 ${q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center ${q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                  children: q.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 38\n                  }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 72\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800 mb-2\",\n                    children: q.question\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm space-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Your answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`,\n                        children: q.userAnswer || 'Not answered'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 582,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 25\n                    }, this), !q.isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Correct answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 font-medium text-green-600\",\n                        children: q.correctAnswer\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.6\n          },\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onTryAnother,\n            className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n            children: \"Try Another Quiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",\n              children: \"Back to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-8\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\",\n            children: \"\\uD83C\\uDFAF Trial Mode - Register for unlimited access\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(TrialQuizResult, \"x28RHQNVH0Ttzr6DQTRChHtChMI=\");\n_c = TrialQuizResult;\nexport default TrialQuizResult;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "TbTrophy", "TbCheck", "TbX", "TbClock", "TbBrain", "TbArrowRight", "TbStar", "TbUsers", "TbBook", "TbMessageCircle", "TbChartBar", "TbSettings", "confetti", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrialQuizResult", "result", "onTryAnother", "onRegister", "_s", "_result$questionResul", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "showFlash", "setShowFlash", "isPassed", "percentage", "setTimeout", "particleCount", "spread", "origin", "y", "audio", "Audio", "volume", "play", "catch", "error", "console", "log", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getPerformanceMessage", "message", "color", "bg", "gradient", "performance", "premiumFeatures", "icon", "title", "description", "children", "jsx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "div", "initial", "opacity", "animate", "transition", "duration", "ease", "scale", "delay", "type", "stiffness", "onAnimationComplete", "h1", "split", "label", "value", "totalQuestions", "correctAnswers", "wrongAnswers", "timeSpent", "map", "stat", "index", "onClick", "to", "button", "whileHover", "whileTap", "feature", "x", "desc", "benefit", "height", "questionResults", "q", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport {\n  TbTrophy,\n  TbCheck,\n  TbX,\n  TbClock,\n  TbBrain,\n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar,\n  TbSettings\n} from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: { y: 0.6 }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    }\n  }, [result.percentage]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <>\n      <style jsx>{`\n        @keyframes flash {\n          0% { opacity: 0; }\n          50% { opacity: 0.4; }\n          100% { opacity: 0; }\n        }\n        .flash-animation {\n          animation: flash 0.5s ease-in-out;\n        }\n      `}</style>\n\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4 relative\">\n        {/* Flash Animation Overlay */}\n        {showFlash && (\n          <div\n            className={`fixed inset-0 z-50 pointer-events-none flash-animation ${\n              result.percentage >= 60\n                ? 'bg-green-500'\n                : 'bg-red-500'\n            }`}\n          />\n        )}\n\n        <div className=\"max-w-6xl mx-auto relative z-10\">\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\" />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\"\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className=\"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\"\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-blue-600 font-medium\">\n                    <TbStar className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Premium Feature</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Better Quiz Features */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.4 }}\n              className=\"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\">\n                <TbBrain className=\"w-6 h-6 mr-2 text-purple-600\" />\n                Advanced Quiz Features & Maximum Control\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Multiple Subject Selection */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.6 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-purple-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbBook className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Multiple Subject Selection</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Mix and match subjects in custom quizzes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Subject-specific performance tracking</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Cross-subject comparison analytics</span>\n                    </li>\n                  </ul>\n                </motion.div>\n\n                {/* Maximum Control */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.8 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-blue-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbSettings className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Maximum Quiz Control</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Choose question count (5-100 questions)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Select difficulty levels (Easy, Medium, Hard)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Pause and resume quiz sessions</span>\n                    </li>\n                  </ul>\n                </motion.div>\n              </div>\n\n              {/* Advanced Features */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 4.0 }}\n                className=\"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\"\n              >\n                <h5 className=\"font-bold text-gray-800 mb-4 text-center\">🚀 Advanced Quiz Features</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {[\n                    { icon: \"⏱️\", title: \"Smart Timer\", desc: \"Adaptive timing\" },\n                    { icon: \"🎯\", title: \"Targeted Practice\", desc: \"Weak area focus\" },\n                    { icon: \"📊\", title: \"Live Analytics\", desc: \"Real-time insights\" },\n                    { icon: \"🏆\", title: \"Achievement System\", desc: \"Unlock rewards\" }\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}\n                      className=\"text-center p-3 bg-white rounded-lg shadow-sm\"\n                    >\n                      <div className=\"text-xl mb-1\">{feature.icon}</div>\n                      <div className=\"font-semibold text-xs text-gray-800\">{feature.title}</div>\n                      <div className=\"text-xs text-gray-600\">{feature.desc}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            </motion.div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default TrialQuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,EACfC,UAAU,EACVC,UAAU,QACL,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgC,QAAQ,GAAGX,MAAM,CAACY,UAAU,IAAI,EAAE;IAExC,IAAID,QAAQ,EAAE;MACZ;MACAE,UAAU,CAAC,MAAM;QACfnB,QAAQ,CAAC;UACPoB,aAAa,EAAE,GAAG;UAClBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAI;QACnB,CAAC,CAAC;;QAEF;QACA,IAAI;UACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,kBAAkB,CAAC;UAC3CD,KAAK,CAACE,MAAM,GAAG,GAAG;UAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACpC;;QAEA;QACAf,YAAY,CAAC,IAAI,CAAC;QAClBG,UAAU,CAAC,MAAMH,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL;MACAG,UAAU,CAAC,MAAM;QACf;QACAH,YAAY,CAAC,IAAI,CAAC;QAClBG,UAAU,CAAC,MAAMH,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACV,MAAM,CAACY,UAAU,CAAC,CAAC;EAEvB,MAAMc,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,KAAIG,gBAAiB,GAAE;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAIpB,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BqB,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIxB,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BqB,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE,gBAAgB;MACvBC,EAAE,EAAE,aAAa;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIxB,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BqB,OAAO,EAAE,eAAe;MACxBC,KAAK,EAAE,eAAe;MACtBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIxB,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BqB,OAAO,EAAE,cAAc;MACvBC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE,eAAe;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIxB,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BqB,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAGL,qBAAqB,CAAChC,MAAM,CAACY,UAAU,CAAC;EAC5D,MAAMD,QAAQ,GAAGX,MAAM,CAACY,UAAU,IAAI,EAAE;EAExC,MAAM0B,eAAe,GAAG,CACtB;IACEC,IAAI,EAAEjD,MAAM;IACZkD,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAErD,OAAO;IACbsD,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/C,UAAU;IAChBgD,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhD,eAAe;IACrBiD,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAElD,OAAO;IACbmD,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEnD,MAAM;IACZoD,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE7C,OAAA,CAAAE,SAAA;IAAA4C,QAAA,gBACE9C,OAAA;MAAO+C,GAAG;MAAAD,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVnD,OAAA;MAAKoD,SAAS,EAAC,8FAA8F;MAAAN,QAAA,GAE1GjC,SAAS,iBACRb,OAAA;QACEoD,SAAS,EAAG,0DACVhD,MAAM,CAACY,UAAU,IAAI,EAAE,GACnB,cAAc,GACd,YACL;MAAE;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACF,eAEDnD,OAAA;QAAKoD,SAAS,EAAC,iCAAiC;QAAAN,QAAA,gBAEhD9C,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCmC,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAE,CAAE;UAC9BoC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/CP,SAAS,EAAC,2BAA2B;UAAAN,QAAA,gBAErC9C,OAAA,CAAChB,MAAM,CAACqE,GAAG;YACTC,OAAO,EAAE;cAAEM,KAAK,EAAE;YAAE,CAAE;YACtBJ,OAAO,EAAE;cAAEI,KAAK,EAAE;YAAE,CAAE;YACtBH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE,GAAG;cAAEC,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC1EX,SAAS,EAAG,mHAAkHX,WAAW,CAACD,QAAS,iBAAiB;YACpKwB,mBAAmB,EAAEA,CAAA,KAAMpD,oBAAoB,CAAC,IAAI,CAAE;YAAAkC,QAAA,eAEtD9C,OAAA,CAACd,QAAQ;cAACkE,SAAS,EAAC;YAAsD;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEbnD,OAAA,CAAChB,MAAM,CAACiF,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CT,SAAS,EAAC,+DAA+D;YAAAN,QAAA,EAC1E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAEZnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAElC,CAAC,EAAE;YAAG,CAAE;YAC/BmC,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAElC,CAAC,EAAE;YAAE,CAAE;YAC9BoC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CT,SAAS,EAAG,uCAAsCX,WAAW,CAACF,EAAG,oBAAmBE,WAAW,CAACH,KAAK,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM;YAAApB,QAAA,eAE1H9C,OAAA;cAAGoD,SAAS,EAAG,iCAAgCX,WAAW,CAACH,KAAM,EAAE;cAAAQ,QAAA,EAChEL,WAAW,CAACJ;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAI,CAAE;UACpCJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CT,SAAS,EAAC,sFAAsF;UAAAN,QAAA,gBAGhG9C,OAAA;YAAKoD,SAAS,EAAG,oBAAmBX,WAAW,CAACD,QAAS,qCAAqC;YAAAM,QAAA,eAC5F9C,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1B9C,OAAA,CAAChB,MAAM,CAACqE,GAAG;gBACTC,OAAO,EAAE;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBACtBJ,OAAO,EAAE;kBAAEI,KAAK,EAAE;gBAAE,CAAE;gBACtBH,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE,GAAG;kBAAEC,IAAI,EAAE;gBAAS,CAAE;gBAC1DV,SAAS,EAAC,4DAA4D;gBAAAN,QAAA,GAErE1C,MAAM,CAACY,UAAU,EAAC,GACrB;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA;gBAAKoD,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAElD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnD,OAAA;YAAKoD,SAAS,EAAC,qCAAqC;YAAAN,QAAA,gBAElD9C,OAAA;cAAKoD,SAAS,EAAC,qDAAqD;cAAAN,QAAA,EACjE,CACC;gBACEqB,KAAK,EAAE,iBAAiB;gBACxBC,KAAK,EAAEhE,MAAM,CAACiE,cAAc;gBAC5B1B,IAAI,EAAEjD,MAAM;gBACZ4C,KAAK,EAAE,MAAM;gBACbuB,KAAK,EAAE;cACT,CAAC,EACD;gBACEM,KAAK,EAAE,iBAAiB;gBACxBC,KAAK,EAAEhE,MAAM,CAACkE,cAAc;gBAC5B3B,IAAI,EAAExD,OAAO;gBACbmD,KAAK,EAAE,OAAO;gBACduB,KAAK,EAAE;cACT,CAAC,EACD;gBACEM,KAAK,EAAE,eAAe;gBACtBC,KAAK,EAAEhE,MAAM,CAACmE,YAAY;gBAC1B5B,IAAI,EAAEvD,GAAG;gBACTkD,KAAK,EAAE,KAAK;gBACZuB,KAAK,EAAE;cACT,CAAC,EACD;gBACEM,KAAK,EAAE,YAAY;gBACnBC,KAAK,EAAEtC,UAAU,CAAC1B,MAAM,CAACoE,SAAS,CAAC;gBACnC7B,IAAI,EAAEtD,OAAO;gBACbiD,KAAK,EAAE,QAAQ;gBACfuB,KAAK,EAAE;cACT,CAAC,CACF,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB3E,OAAA,CAAChB,MAAM,CAACqE,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAG,CAAE;gBAC/BmC,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAE,CAAE;gBAC9BoC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAEa,IAAI,CAACb;gBAAM,CAAE;gBACjDT,SAAS,EAAG,iBAAgBsB,IAAI,CAACpC,KAAM,iCAAgCoC,IAAI,CAACpC,KAAM,kBAAkB;gBAAAQ,QAAA,gBAEpG9C,OAAA;kBAAKoD,SAAS,EAAG,6BAA4BsB,IAAI,CAACpC,KAAM,kDAAkD;kBAAAQ,QAAA,eACxG9C,OAAA,CAAC0E,IAAI,CAAC/B,IAAI;oBAACS,SAAS,EAAG,gBAAesB,IAAI,CAACpC,KAAM;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACNnD,OAAA;kBAAKoD,SAAS,EAAG,uCAAsCsB,IAAI,CAACpC,KAAM,WAAW;kBAAAQ,QAAA,EAC1E4B,IAAI,CAACN;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNnD,OAAA;kBAAKoD,SAAS,EAAC,mCAAmC;kBAAAN,QAAA,EAC/C4B,IAAI,CAACP;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,GAdDwB,KAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEK,KAAK,EAAE;cAAI,CAAE;cACpCJ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEK,KAAK,EAAE;cAAE,CAAE;cAClCH,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CT,SAAS,EAAC,kBAAkB;cAAAN,QAAA,eAE5B9C,OAAA;gBAAKoD,SAAS,EAAG,kFACfrC,QAAQ,GACJ,uDAAuD,GACvD,iDACL,EAAE;gBAAA+B,QAAA,GACA/B,QAAQ,gBACPf,OAAA,CAACb,OAAO;kBAACiE,SAAS,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/BnD,OAAA,CAACZ,GAAG;kBAACgE,SAAS,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC3B,eACDnD,OAAA;kBAAA8C,QAAA,EACG/B,QAAQ,GAAG,iCAAiC,GAAG;gBAAsC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBC,OAAO,EAAE;gBAAED,OAAO,EAAE;cAAE,CAAE;cACxBE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CT,SAAS,EAAC,aAAa;cAAAN,QAAA,eAEvB9C,OAAA;gBACE4E,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAAC,CAACD,WAAW,CAAE;gBAC5C2C,SAAS,EAAC,6IAA6I;gBAAAN,QAAA,gBAEvJ9C,OAAA,CAACJ,UAAU;kBAACwD,SAAS,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClCnD,OAAA;kBAAA8C,QAAA,EAAOrC,WAAW,GAAG,uBAAuB,GAAG;gBAAuB;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAG,CAAE;UAC/BmC,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAE,CAAE;UAC9BoC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CT,SAAS,EAAC,kBAAkB;UAAAN,QAAA,eAE5B9C,OAAA,CAACf,IAAI;YAAC4F,EAAE,EAAC,WAAW;YAAA/B,QAAA,eAClB9C,OAAA,CAAChB,MAAM,CAAC8F,MAAM;cACZ1B,SAAS,EAAC,mOAAmO;cAC7O2B,UAAU,EAAE;gBAAEnB,KAAK,EAAE,IAAI;gBAAEvC,CAAC,EAAE,CAAC;cAAE,CAAE;cACnC2D,QAAQ,EAAE;gBAAEpB,KAAK,EAAE;cAAK,CAAE;cAAAd,QAAA,gBAE1B9C,OAAA;gBAAA8C,QAAA,EAAM;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBnD,OAAA,CAACT,YAAY;gBAAC6D,SAAS,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAG,CAAE;UAC/BmC,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAE,CAAE;UAC9BoC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CT,SAAS,EAAC,4EAA4E;UAAAN,QAAA,gBAGtF9C,OAAA;YAAKoD,SAAS,EAAC,uFAAuF;YAAAN,QAAA,gBACpG9C,OAAA;cAAIoD,SAAS,EAAC,qCAAqC;cAAAN,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAN,QAAA,EAAC;YAErC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNnD,OAAA;YAAKoD,SAAS,EAAC,YAAY;YAAAN,QAAA,gBACzB9C,OAAA;cAAKoD,SAAS,EAAC,sDAAsD;cAAAN,QAAA,EAClEJ,eAAe,CAAC+B,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAClC3E,OAAA,CAAChB,MAAM,CAACqE,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAG,CAAE;gBAC/BmC,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAE,CAAE;gBAC9BoC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;gBAAO,CAAE;gBAC1DvB,SAAS,EAAC,sIAAsI;gBAAAN,QAAA,gBAEhJ9C,OAAA;kBAAKoD,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,gBAC/C9C,OAAA;oBAAKoD,SAAS,EAAC,4JAA4J;oBAAAN,QAAA,eACzK9C,OAAA,CAACiF,OAAO,CAACtC,IAAI;sBAACS,SAAS,EAAC;oBAAoB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACNnD,OAAA;oBAAIoD,SAAS,EAAC,iCAAiC;oBAAAN,QAAA,EAAEmC,OAAO,CAACrC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACNnD,OAAA;kBAAGoD,SAAS,EAAC,+BAA+B;kBAAAN,QAAA,EAAEmC,OAAO,CAACpC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtEnD,OAAA;kBAAKoD,SAAS,EAAC,kDAAkD;kBAAAN,QAAA,gBAC/D9C,OAAA,CAACR,MAAM;oBAAC4D,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCnD,OAAA;oBAAMoD,SAAS,EAAC,SAAS;oBAAAN,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA,GAhBDwB,KAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAG,CAAE;cAC/BmC,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAE,CAAE;cAC9BoC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CT,SAAS,EAAC,0FAA0F;cAAAN,QAAA,gBAEpG9C,OAAA;gBAAIoD,SAAS,EAAC,mFAAmF;gBAAAN,QAAA,gBAC/F9C,OAAA,CAACV,OAAO;kBAAC8D,SAAS,EAAC;gBAA8B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4CAEtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnD,OAAA;gBAAKoD,SAAS,EAAC,uCAAuC;gBAAAN,QAAA,gBAEpD9C,OAAA,CAAChB,MAAM,CAACqE,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChC1B,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE;kBAAE,CAAE;kBAC9BzB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAE;kBAAI,CAAE;kBAC1CT,SAAS,EAAC,4DAA4D;kBAAAN,QAAA,gBAEtE9C,OAAA;oBAAKoD,SAAS,EAAC,wBAAwB;oBAAAN,QAAA,gBACrC9C,OAAA;sBAAKoD,SAAS,EAAC,yGAAyG;sBAAAN,QAAA,eACtH9C,OAAA,CAACN,MAAM;wBAAC0D,SAAS,EAAC;sBAAoB;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACNnD,OAAA;sBAAIoD,SAAS,EAAC,yBAAyB;sBAAAN,QAAA,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACNnD,OAAA;oBAAIoD,SAAS,EAAC,iCAAiC;oBAAAN,QAAA,gBAC7C9C,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,sBAAsB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CnD,OAAA;wBAAA8C,QAAA,GAAM,cAAY,eAAA9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,sBAAkB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,sBAAsB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAAwC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,sBAAsB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAAqC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,sBAAsB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAAkC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE;kBAAG,CAAE;kBAC/B1B,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE;kBAAE,CAAE;kBAC9BzB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAE;kBAAI,CAAE;kBAC1CT,SAAS,EAAC,0DAA0D;kBAAAN,QAAA,gBAEpE9C,OAAA;oBAAKoD,SAAS,EAAC,wBAAwB;oBAAAN,QAAA,gBACrC9C,OAAA;sBAAKoD,SAAS,EAAC,wGAAwG;sBAAAN,QAAA,eACrH9C,OAAA,CAACH,UAAU;wBAACuD,SAAS,EAAC;sBAAoB;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACNnD,OAAA;sBAAIoD,SAAS,EAAC,yBAAyB;sBAAAN,QAAA,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACNnD,OAAA;oBAAIoD,SAAS,EAAC,iCAAiC;oBAAAN,QAAA,gBAC7C9C,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,oBAAoB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CnD,OAAA;wBAAA8C,QAAA,GAAM,aAAW,eAAA9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,oBAAgB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,oBAAoB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAAuC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,oBAAoB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAA6C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACLnD,OAAA;sBAAIoD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,gBAC9B9C,OAAA;wBAAMoD,SAAS,EAAC,oBAAoB;wBAAAN,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CnD,OAAA;wBAAA8C,QAAA,EAAM;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAG,CAAE;gBAC/BmC,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAElC,CAAC,EAAE;gBAAE,CAAE;gBAC9BoC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC1CT,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,gBAE3E9C,OAAA;kBAAIoD,SAAS,EAAC,0CAA0C;kBAAAN,QAAA,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvFnD,OAAA;kBAAKoD,SAAS,EAAC,uCAAuC;kBAAAN,QAAA,EACnD,CACC;oBAAEH,IAAI,EAAE,IAAI;oBAAEC,KAAK,EAAE,aAAa;oBAAEuC,IAAI,EAAE;kBAAkB,CAAC,EAC7D;oBAAExC,IAAI,EAAE,IAAI;oBAAEC,KAAK,EAAE,mBAAmB;oBAAEuC,IAAI,EAAE;kBAAkB,CAAC,EACnE;oBAAExC,IAAI,EAAE,IAAI;oBAAEC,KAAK,EAAE,gBAAgB;oBAAEuC,IAAI,EAAE;kBAAqB,CAAC,EACnE;oBAAExC,IAAI,EAAE,IAAI;oBAAEC,KAAK,EAAE,oBAAoB;oBAAEuC,IAAI,EAAE;kBAAiB,CAAC,CACpE,CAACV,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBACnB3E,OAAA,CAAChB,MAAM,CAACqE,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEK,KAAK,EAAE;oBAAI,CAAE;oBACpCJ,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEK,KAAK,EAAE;oBAAE,CAAE;oBAClCH,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;oBAAO,CAAE;oBAC1DvB,SAAS,EAAC,+CAA+C;oBAAAN,QAAA,gBAEzD9C,OAAA;sBAAKoD,SAAS,EAAC,cAAc;sBAAAN,QAAA,EAAEmC,OAAO,CAACtC;oBAAI;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClDnD,OAAA;sBAAKoD,SAAS,EAAC,qCAAqC;sBAAAN,QAAA,EAAEmC,OAAO,CAACrC;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1EnD,OAAA;sBAAKoD,SAAS,EAAC,uBAAuB;sBAAAN,QAAA,EAAEmC,OAAO,CAACE;oBAAI;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GARtDwB,KAAK;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAG,CAAE;cAC/BmC,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAE,CAAE;cAC9BoC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CT,SAAS,EAAC,wFAAwF;cAAAN,QAAA,gBAElG9C,OAAA;gBAAIoD,SAAS,EAAC,kDAAkD;gBAAAN,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnD,OAAA;gBAAKoD,SAAS,EAAC,sDAAsD;gBAAAN,QAAA,EAClE,CACC;kBAAEH,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,gBAAgB;kBAAEuC,IAAI,EAAE;gBAA6B,CAAC,EAC3E;kBAAExC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,iBAAiB;kBAAEuC,IAAI,EAAE;gBAA0B,CAAC,EACzE;kBAAExC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,gBAAgB;kBAAEuC,IAAI,EAAE;gBAA2B,CAAC,EACzE;kBAAExC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,gBAAgB;kBAAEuC,IAAI,EAAE;gBAAmB,CAAC,CAClE,CAACV,GAAG,CAAC,CAACW,OAAO,EAAET,KAAK,kBACnB3E,OAAA,CAAChB,MAAM,CAACqE,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAI,CAAE;kBACpCJ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEK,KAAK,EAAE;kBAAE,CAAE;kBAClCH,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGc;kBAAO,CAAE;kBAC1DvB,SAAS,EAAC,+CAA+C;kBAAAN,QAAA,gBAEzD9C,OAAA;oBAAKoD,SAAS,EAAC,eAAe;oBAAAN,QAAA,EAAEsC,OAAO,CAACzC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDnD,OAAA;oBAAKoD,SAAS,EAAC,kCAAkC;oBAAAN,QAAA,EAAEsC,OAAO,CAACxC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEnD,OAAA;oBAAKoD,SAAS,EAAC,uBAAuB;oBAAAN,QAAA,EAAEsC,OAAO,CAACD;kBAAI;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GARtDwB,KAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAG,CAAE;cAC/BmC,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAElC,CAAC,EAAE;cAAE,CAAE;cAC9BoC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CT,SAAS,EAAC,0FAA0F;cAAAN,QAAA,gBAEpG9C,OAAA;gBAAIoD,SAAS,EAAC,wBAAwB;gBAAAN,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9DnD,OAAA;gBAAGoD,SAAS,EAAC,oBAAoB;gBAAAN,QAAA,EAAC;cAAoD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1FnD,OAAA;gBAAKoD,SAAS,EAAC,6DAA6D;gBAAAN,QAAA,gBAC1E9C,OAAA,CAACf,IAAI;kBAAC4F,EAAE,EAAC,WAAW;kBAAA/B,QAAA,eAClB9C,OAAA,CAAChB,MAAM,CAAC8F,MAAM;oBACZ1B,SAAS,EAAC,6HAA6H;oBACvI2B,UAAU,EAAE;sBAAEnB,KAAK,EAAE;oBAAK,CAAE;oBAC5BoB,QAAQ,EAAE;sBAAEpB,KAAK,EAAE;oBAAK,CAAE;oBAAAd,QAAA,gBAE1B9C,OAAA;sBAAA8C,QAAA,EAAM;oBAAmB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChCnD,OAAA,CAACT,YAAY;sBAAC6D,SAAS,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPnD,OAAA;kBAAKoD,SAAS,EAAC,uBAAuB;kBAAAN,QAAA,EAAC;gBAEvC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ1C,WAAW,iBACVT,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAE8B,MAAM,EAAE;UAAE,CAAE;UACnC7B,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAE8B,MAAM,EAAE;UAAO,CAAE;UACxC5B,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BN,SAAS,EAAC,yCAAyC;UAAAN,QAAA,gBAEnD9C,OAAA;YAAIoD,SAAS,EAAC,sCAAsC;YAAAN,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEnD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAN,QAAA,GAAAtC,qBAAA,GACvBJ,MAAM,CAACkF,eAAe,cAAA9E,qBAAA,uBAAtBA,qBAAA,CAAwBiE,GAAG,CAAC,CAACc,CAAC,EAAEZ,KAAK,kBACpC3E,OAAA;cAAiBoD,SAAS,EAAG,2BAC3BmC,CAAC,CAACC,SAAS,GAAG,8BAA8B,GAAG,0BAChD,EAAE;cAAA1C,QAAA,eACD9C,OAAA;gBAAKoD,SAAS,EAAC,4BAA4B;gBAAAN,QAAA,gBACzC9C,OAAA;kBAAKoD,SAAS,EAAG,yDACfmC,CAAC,CAACC,SAAS,GAAG,yBAAyB,GAAG,uBAC3C,EAAE;kBAAA1C,QAAA,EACAyC,CAAC,CAACC,SAAS,gBAAGxF,OAAA,CAACb,OAAO;oBAACiE,SAAS,EAAC;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACZ,GAAG;oBAACgE,SAAS,EAAC;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNnD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAN,QAAA,gBACrB9C,OAAA;oBAAGoD,SAAS,EAAC,gCAAgC;oBAAAN,QAAA,EAAEyC,CAAC,CAACE;kBAAQ;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DnD,OAAA;oBAAKoD,SAAS,EAAC,mBAAmB;oBAAAN,QAAA,gBAChC9C,OAAA;sBAAA8C,QAAA,gBACE9C,OAAA;wBAAMoD,SAAS,EAAC,eAAe;wBAAAN,QAAA,EAAC;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnDnD,OAAA;wBAAMoD,SAAS,EAAG,oBAAmBmC,CAAC,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;wBAAA1C,QAAA,EACpFyC,CAAC,CAACG,UAAU,IAAI;sBAAc;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EACH,CAACoC,CAAC,CAACC,SAAS,iBACXxF,OAAA;sBAAA8C,QAAA,gBACE9C,OAAA;wBAAMoD,SAAS,EAAC,eAAe;wBAAAN,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtDnD,OAAA;wBAAMoD,SAAS,EAAC,iCAAiC;wBAAAN,QAAA,EAAEyC,CAAC,CAACI;sBAAa;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1BEwB,KAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAKDnD,OAAA,CAAChB,MAAM,CAACqE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAG,CAAE;UAC/BmC,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAElC,CAAC,EAAE;UAAE,CAAE;UAC9BoC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CT,SAAS,EAAC,gDAAgD;UAAAN,QAAA,gBAE1D9C,OAAA;YACE4E,OAAO,EAAEvE,YAAa;YACtB+C,SAAS,EAAC,0GAA0G;YAAAN,QAAA,EACrH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnD,OAAA,CAACf,IAAI;YAAC4F,EAAE,EAAC,GAAG;YAAA/B,QAAA,eACV9C,OAAA;cAAQoD,SAAS,EAAC,6FAA6F;cAAAN,QAAA,EAAC;YAEhH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGbnD,OAAA;UAAKoD,SAAS,EAAC,kBAAkB;UAAAN,QAAA,eAC/B9C,OAAA;YAAMoD,SAAS,EAAC,uFAAuF;YAAAN,QAAA,EAAC;UAExG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC5C,EAAA,CAtmBIJ,eAAe;AAAAyF,EAAA,GAAfzF,eAAe;AAwmBrB,eAAeA,eAAe;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}