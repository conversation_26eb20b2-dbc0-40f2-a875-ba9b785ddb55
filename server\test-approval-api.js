const mongoose = require("mongoose");
const axios = require("axios");
require("dotenv").config();

async function testApprovalAPI() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");
    const User = require("./models/userModel");

    // Find an admin user for authentication
    const adminUser = await User.findOne({ isAdmin: true });
    if (!adminUser) {
      console.log("❌ No admin user found");
      return;
    }

    // Find a standalone generation with questions that now has an examId (from our previous test)
    const generation = await AIQuestionGeneration.findOne({
      examId: { $ne: null },
      generationStatus: "completed",
      "generatedQuestions.0": { $exists: true },
      "generatedQuestions.approved": false
    });

    if (!generation) {
      console.log("❌ No suitable generation found for testing");
      return;
    }

    console.log(`📋 Testing approval API for generation: ${generation._id}`);
    console.log(`   - ExamId: ${generation.examId}`);
    console.log(`   - Questions: ${generation.generatedQuestions.length}`);

    // Test the approval API endpoint
    const approvalData = {
      generationId: generation._id.toString(),
      approvedQuestionIds: [0], // Approve the first question
      rejectedQuestions: []
    };

    console.log("🚀 Calling approval API...");
    
    try {
      const response = await axios.post('http://localhost:5000/api/ai-questions/approve-questions', approvalData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminUser._id}` // Simple auth for testing
        }
      });

      console.log("✅ API Response:", response.data);

      if (response.data.success) {
        console.log(`🎉 Successfully approved ${response.data.data.approvedQuestions} questions!`);
        
        // Check if the exam now has questions
        const Exam = require("./models/examModel");
        const exam = await Exam.findById(generation.examId);
        if (exam) {
          console.log(`📊 Exam "${exam.name}" now has ${exam.questions.length} questions`);
        }
      }

    } catch (apiError) {
      if (apiError.response) {
        console.log("❌ API Error:", apiError.response.data);
      } else {
        console.log("❌ Network Error:", apiError.message);
      }
    }

    await mongoose.disconnect();
    console.log("\n✅ Test completed");

  } catch (error) {
    console.error("❌ Test failed:", error);
    await mongoose.disconnect();
  }
}

testApprovalAPI();
